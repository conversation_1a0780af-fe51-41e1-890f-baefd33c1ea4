import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, UntypedFormControl, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { OperationService } from 'src/app/authentication/operation-registeration/services/operation.service';
import { AlertHandlerService } from 'src/app/modules/core/alerts/alert-handler.service';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { AuthService } from 'src/app/shared/services/auth.service';
import { PhoneInputComponent } from '../../../../shared/components/inputs/phone-input/phone-input.component';
import { SecondaryBtnComponent } from '../../../../shared/components/secondary-btn/secondary-btn.component';
import { NtranslatePipe } from '../../../../shared/pipes/ntranslate.pipe';

@Component({
    selector: 'app-operation-edit-phone',
    templateUrl: './operation-edit-phone.component.html',
    styleUrls: ['./operation-edit-phone.component.scss'],
    standalone: true,
    imports: [FormsModule, ReactiveFormsModule, PhoneInputComponent, SecondaryBtnComponent, NtranslatePipe]
})
export class OperationEditPhoneComponent implements OnInit {

  countries!: any[];
  selectedCountry!: any;

  public editPhoneForm: FormGroup;

  constructor(private _formBuilder: FormBuilder, private _router: Router, private translateService: TranslationService,

    private alertService: AlertHandlerService, private _authService: AuthService, private operationService: OperationService) {
    // Countries
    this.countries = [
      { name: '+20', code: 'EG' },
      // { name: '+966', code: 'SA' },
      // { name: '+971', code: 'AE' },
    ];

    this.selectedCountry = this.countries[0];
  }

  ngOnInit(): void {
    this.editPhoneForm = this._formBuilder.group({
      phone: new UntypedFormControl(null, [
        Validators.required,
        Validators.pattern(
          /^[\+]?[0-9]+[\s]?,01[0-5]\d{1,8}/
        ),
        Validators.minLength(15)
      ]),

    });
  }

  public validateControl = (controlName: string) => {
    return (
      this.editPhoneForm.controls[controlName].invalid &&
      this.editPhoneForm.controls[controlName].touched
    );
  };

  public hasError = (controlName: string, errorName: string) => {
    return this.editPhoneForm.controls[controlName].hasError(errorName);
  };

  editPhone(EditPhoneFormValue: any) {
    const phoneDetails = this.editPhoneForm.controls['phone'].value.split(',');

    this._authService
      .phoneNumberExists(phoneDetails[1])
      .subscribe((x) => {
        if (!x) {
          this.operationService.update('phone', phoneDetails[1]);

          this._router.navigate(['/authentication/register-operator/verification']);
        }
        else {
          this.alertService.error({ message: this.translateService.instant('PhoneNumberExists') });
        }
      });

  }

}
