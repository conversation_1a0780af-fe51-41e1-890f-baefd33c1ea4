import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { DialogModule } from 'primeng/dialog';
import { RadioButtonModule } from 'primeng/radiobutton';
import { Observable, map } from 'rxjs';
import { AlertHandlerService } from 'src/app/modules/core/alerts/alert-handler.service';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { DarkBtnComponent } from 'src/app/shared/components/dark-btn/dark-btn.component';
import { RedBtnComponent } from 'src/app/shared/components/red-btn/red-btn.component';
import { ListingDetails, ListingStatus } from 'src/app/shared/models/listing.model';
import { LookupDTO } from 'src/app/shared/models/lookup.model';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { DeviceDetectionService } from 'src/app/shared/services/device-detection.service';
import { ListingService } from 'src/app/shared/services/listing.service';
import { LookupService } from 'src/app/shared/services/lookup.service';
import { ListingOffersComponent } from '../listing-offers/listing-offers.component';

@Component({
  selector: 'app-ad-status-view',
  standalone: true,
  imports: [CommonModule, DarkBtnComponent, RedBtnComponent, NtranslatePipe, RouterModule, DialogModule, FormsModule, ReactiveFormsModule, RadioButtonModule, ListingOffersComponent],
  templateUrl: './ad-status-view.component.html',
  styleUrls: ['./ad-status-view.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AdStatusViewComponent implements OnInit {
  @Input() listing: ListingDetails;


  statusId = 1;

  reasons$: Observable<LookupDTO[]>;

  reasonVisible: boolean = false;

  offersVisible: boolean = false;

  form: FormGroup;

  listingStatus = ListingStatus;

  constructor(
    private listingService: ListingService,
    private router: Router,
    private fb: FormBuilder,
    private alertService: AlertHandlerService,
    private lookupService: LookupService,
    private translateService: TranslationService,
    public device: DeviceDetectionService,
    private cd: ChangeDetectorRef

  ) { }

  ngOnInit(): void {

    this.form = this.fb.group({
      RemoveReason: new FormControl('', [Validators.required]),
    });


  }

  edit() {
    this.listingService.editAd(this.listing.listingID, this.listing.status == this.listingStatus.approved);
  }
  delete() {

    this.listingService.deleteAd(this.listing.listingID, () => {
      this.router.navigate(['/listing/list']);
    }, this.form.value.RemoveReason);

    this.reasonVisible = false;
    this.cd.markForCheck();

  }

  openDeleteReasons() {

    this.alertService.warn({
      message: this.translateService.instant("Are you sure you want to remove this listing?"),
      description: this.listing.status == this.listingStatus.approved ? this.translateService.instant("my_listing_delete_listing_has_offer") : '',

      buttons: [
        { title: this.translateService.instant("Yes, Remove"), value: true },
        { title: this.translateService.instant("No, Keep"), value: false },
      ]
    }, (e) => {
      if (e) {
        this.reasons$ = this.lookupService.getremovereasons().pipe(map(res => res.data));
        this.reasonVisible = true;
        this.cd.markForCheck();


      }
    });

  }

}
