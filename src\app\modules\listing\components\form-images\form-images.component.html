<div class="images_holder" #target>
  <div class="help_msg">
    <i class="pi pi-info-circle"></i>{{ "create_listing_help_images" | translate }}
  </div>
  <h2 [ngClass]="{'noListingImages': urls.length == 0}">
    <div>
      <span>{{ "Chooseupto" | translate }}<b>15</b> {{ "Photos" | translate }} <b *ngIf="urls.length == 0"
          class="required">*</b></span>
      <!-- <h3>{{ "MAX_FILE_SIZE" | translate}}</h3> -->
    </div>
    <span class="total_images">
      <svg data-id="camera_icon" viewBox="0 0 14 12" fill="none">
        <path
          d="M11.6654 2.00131H9.9387L8.80536 0.861313C8.74307 0.799526 8.66919 0.750642 8.58797 0.717466C8.50674 0.684289 8.41977 0.667473 8.33203 0.66798H5.66536C5.57763 0.667473 5.49065 0.684289 5.40943 0.717466C5.3282 0.750642 5.25432 0.799526 5.19203 0.861313L4.0587 2.00131H2.33203C1.8016 2.00131 1.29289 2.21203 0.917818 2.5871C0.542745 2.96217 0.332031 3.47088 0.332031 4.00131V9.33465C0.332031 9.86508 0.542745 10.3738 0.917818 10.7489C1.29289 11.1239 1.8016 11.3346 2.33203 11.3346H11.6654C12.1958 11.3346 12.7045 11.1239 13.0796 10.7489C13.4546 10.3738 13.6654 9.86508 13.6654 9.33465V4.00131C13.6654 3.47088 13.4546 2.96217 13.0796 2.5871C12.7045 2.21203 12.1958 2.00131 11.6654 2.00131ZM6.9987 9.33465C6.47128 9.33465 5.95571 9.17825 5.51718 8.88523C5.07865 8.59222 4.73685 8.17574 4.53502 7.68847C4.33319 7.2012 4.28038 6.66502 4.38327 6.14774C4.48616 5.63046 4.74014 5.1553 5.11308 4.78236C5.48602 4.40942 5.96117 4.15545 6.47846 4.05255C6.99574 3.94966 7.53192 4.00247 8.01919 4.2043C8.50646 4.40613 8.92293 4.74793 9.21595 5.18646C9.50897 5.62499 9.66536 6.14056 9.66536 6.66798C9.66536 7.37522 9.38441 8.0535 8.88432 8.5536C8.38422 9.0537 7.70594 9.33465 6.9987 9.33465Z"
          fill="currentColor" />
      </svg>
      <b>{{ urls.length }}</b></span>
  </h2>

  <div class="images_listio" [ngClass]="{'loading' : loadingimage}">
    <!-- Got cover image -->
    <div class="image_icon cover_icon">
      <ng-container *ngIf="!coverURL.url">
        <input type="file" accept="image/* , .heic, .heif" (change)="onSelectFile($event , true)"
          [multiple]="urls.length == 0" />
        <svg data-id="add_image_icon" viewBox="0 0 72 66" fill="none">
          <path
            d="M58.2359 12.1115H56.9558C55.6758 12.1115 54.7157 11.353 54.0757 9.83597L50.2355 4.96677C49.2754 2.31196 47.0353 0.794922 44.4752 0.794922H26.8742C24.6341 0.794922 22.394 2.31196 21.1139 4.96677L17.5937 9.83597C16.9537 11.353 15.9936 12.1115 14.7136 12.1115H13.4335C6.39312 12.1115 0.632812 18.9382 0.632812 27.2819V50.0374C0.632812 58.3811 6.39312 65.2078 13.4335 65.2078H58.2359C65.2763 65.2078 71.0366 58.3811 71.0366 50.0374V27.2819C71.0366 18.9382 65.2763 12.1115 58.2359 12.1115Z"
            fill="#722282" fill-opacity="0.2" />
          <rect x="33.1523" y="21.7617" width="3.85188" height="26.9635" rx="1.92594" fill="#722282" />
          <rect x="48.5625" y="33.3105" width="3.85193" height="26.9631" rx="1.92597"
            transform="rotate(90 48.5625 33.3105)" fill="#722282" />
        </svg>

      </ng-container>
      <ng-container *ngIf="coverURL.url">
        <span class="delete" (click)="removeImage(coverURL)">
          <i class="pi pi-times"></i>
        </span>
        <img [src]="coverURL?.url | listingImagePath:'myListing'" />
      </ng-container>
      <div class="cover_label">{{ 'Cover' | translate }}</div>
    </div>
    <!-- Get all available images -->
    <ng-container *ngFor="let url of urls">
      <div class="image_icon" *ngIf="coverURL?.uid !== url.uid">
        <span class="delete" (click)="removeImage(url)">
          <i class="pi pi-times"></i>
        </span>
        <img [src]="url.url  | listingImagePath:'myListing'" (click)="coverURL = url" />
        <div class="cover_label">{{ 'Cover' | translate }}</div>
      </div>
    </ng-container>

    <!-- Set image add button -->

    <div class="image_icon"
      *ngFor="let item of [].constructor(urls.length > 0 ? (14 - (urls.length - 1)) : 14); let i = index">
      <input type="file" accept="image/*, .heic, .heif" (change)="onSelectFile($event , false)" multiple />
      <svg data-id="add_image_icon" viewBox="0 0 72 66" fill="none">
        <path
          d="M58.2359 12.1115H56.9558C55.6758 12.1115 54.7157 11.353 54.0757 9.83597L50.2355 4.96677C49.2754 2.31196 47.0353 0.794922 44.4752 0.794922H26.8742C24.6341 0.794922 22.394 2.31196 21.1139 4.96677L17.5937 9.83597C16.9537 11.353 15.9936 12.1115 14.7136 12.1115H13.4335C6.39312 12.1115 0.632812 18.9382 0.632812 27.2819V50.0374C0.632812 58.3811 6.39312 65.2078 13.4335 65.2078H58.2359C65.2763 65.2078 71.0366 58.3811 71.0366 50.0374V27.2819C71.0366 18.9382 65.2763 12.1115 58.2359 12.1115Z"
          fill="#722282" fill-opacity="0.2" />
        <rect x="33.1523" y="21.7617" width="3.85188" height="26.9635" rx="1.92594" fill="#722282" />
        <rect x="48.5625" y="33.3105" width="3.85193" height="26.9631" rx="1.92597"
          transform="rotate(90 48.5625 33.3105)" fill="#722282" />
      </svg>

    </div>
  </div>
</div>