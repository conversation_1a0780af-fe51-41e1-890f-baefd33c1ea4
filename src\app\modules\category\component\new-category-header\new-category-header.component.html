<ng-container *ngIf="banners$ | async as banners; else fallbackTemplate">
  <div class="container">
    <div class="category-header">
      <app-hero-section [banners$]="banners$"></app-hero-section>

      <ng-container *ngIf="stickerImage$ | async as stickerImage">
        <div
          class="slider-cover"
          [ngClass]="{ rtl: isRtl }"
          *ngIf="stickerImage"
        >
          <img
            [src]="(stickerImage$ | async)?.url"
            [alt]="categoryInfo.name"
            class="slider-cover-background"
          />
          <div class="overlay-content">
            <h3 class="title">
              {{ categoryInfo.name | translate }}
            </h3>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
</ng-container>

<ng-template #fallbackTemplate>
  <app-category-header
    [category]="category"
    [subCategoryId]="subCategoryId"
  ></app-category-header>
</ng-template>
