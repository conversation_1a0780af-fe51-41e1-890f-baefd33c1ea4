import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BrowserService } from '@src/app/modules/core/service/browser.service';
import { Observable, of, ReplaySubject } from 'rxjs';
import { catchError, finalize, map, shareReplay, tap } from 'rxjs/operators';

@Injectable({
    providedIn: 'root'
})
export class SvgRegistryService {
    private svgCache: Map<string, string> = new Map();
    private loadedIcons: Set<string> = new Set();
    private spriteElement: SVGElement | null = null;

    private pendingRequests: Map<string, Observable<boolean>> = new Map();

    constructor(private http: HttpClient, private browser: BrowserService) {
        this.initializeSpriteElement();
    }

    private initializeSpriteElement(): void {
        if (!this.spriteElement && this.browser.isBrowser()) {
            this.spriteElement = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            this.spriteElement.setAttribute('style', 'display: none');
            this.spriteElement.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
            document.body.appendChild(this.spriteElement);
        }
    }

    loadSvg(name: string): Observable<boolean> {
        if (this.loadedIcons.has(name)) {
            return of(true);
        }

        if (this.pendingRequests.has(name)) {
            return this.pendingRequests.get(name)!;
        }

        if (this.svgCache.has(name)) {
            this.addToSprite(name, this.svgCache.get(name)!);
            return of(true);
        }

        const request$ = this.http.get(`assets/icons/${name}.svg`, { responseType: 'text' })
            .pipe(
                tap(svgContent => {
                    this.svgCache.set(name, svgContent);
                    this.addToSprite(name, svgContent);
                }),
                map(() => true),
                catchError(error => {
                    console.error(`Failed to load SVG icon: ${name}`, error);
                    return of(false);
                }),
                // Use shareReplay(1) to share the result with all subscribers
                shareReplay(1),
                // Remove from pending requests when complete
                finalize(() => {
                    this.pendingRequests.delete(name);
                })
            );

        this.pendingRequests.set(name, request$);
        return request$;
    }

    private addToSprite(name: string, svgContent: string): void {

        if (this.browser.isBrowser()) {
            const div = document.createElement('div');
            div.innerHTML = svgContent;
            const svg = div.querySelector('svg');

            if (!svg || !this.spriteElement) {
                return;
            }

            const existingSymbol = document.getElementById(`icon-${name}`);
            if (existingSymbol) {
                return;
            }

            const symbol = document.createElementNS('http://www.w3.org/2000/svg', 'symbol');
            symbol.setAttribute('id', `icon-${name}`);
            symbol.setAttribute('viewBox', svg.getAttribute('viewBox') || '0 0 24 24');

            while (svg.childNodes.length > 0) {
                symbol.appendChild(svg.childNodes[0]);
            }

            this.spriteElement.appendChild(symbol);
            this.loadedIcons.add(name);

        }

    }

    isIconLoaded(name: string): boolean {
        return this.loadedIcons.has(name);
    }

    preloadIcons(names: string[]): Observable<boolean> {
        const loadObservables = names.map(name => this.loadSvg(name));

        const completion$ = new ReplaySubject<boolean>(1);

        let loadedCount = 0;

        loadObservables.forEach(obs => {
            obs.subscribe({
                next: success => {
                    loadedCount++;
                    if (loadedCount === names.length) {
                        completion$.next(true);
                        completion$.complete();
                    }
                },
                error: () => {
                    loadedCount++;
                    if (loadedCount === names.length) {
                        completion$.next(false);
                        completion$.complete();
                    }
                }
            });
        });

        return completion$.asObservable();
    }
}