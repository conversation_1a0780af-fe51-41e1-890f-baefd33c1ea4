import { trigger, transition, style, animate, query, stagger } from "@angular/animations";

export const fadeAnimation = trigger('fadeAnimation', [
    transition(':enter', [
      style({ opacity: 0 }), animate('0.5s', style({ opacity: 1 }))]
    )
  ]);


  export  const listAnimation = trigger('listAnimation', [
    transition('* => *', [ // each time the binding value changes
      query(':enter', [
        style({ opacity: 0 }),
        stagger(300, [
          animate('0.8s', style({  opacity: 1}))
        ])
      ],{optional: true})
    ])
  ]);


  export const  fadeInUp =trigger('fadeInUp', [
    transition(':enter', [
      style({ opacity: 0, transform: 'translateY(40px)' }),
      animate(
        '350ms 100ms ease-in-out',
        style({ opacity: 1, transform: 'translateY(0)' })
      ),
    ]),
    transition(':leave', [
      style({ opacity: 1, transform: 'scale(1)' }),
      animate(
        '300ms  ease-in-out',
        style({ opacity: 0, transform: 'scale(0.5) transalteY(40px)' })
      ),
    ]),
  ])
