@import "mixins";
@import "variables";

.banner_wrapper {
    overflow: hidden;
    position: relative;
    background: #000;
}

.header_image {
    position: relative;

    &:before {
        display: block;
        content: "";
        width: 100%;
        padding-top: 59.1%;
    }

    @include Large {
        &:before {
            display: block;
            content: "";
            width: 100%;
            padding-top: 28.57142857142857%;
        }
    }

    &:after {
        content: '';
        position: absolute;
        top: 0px;
        left: 0px;
        width: 100%;
        height: 100%;
        z-index: 2;
    }

    picture {
        position: absolute;
        top: 0px;
        left: 0px;
        width: 100%;
        height: 100%;
    }

    img {
        position: relative;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    h2 {
        position: absolute;
        z-index: 3;
        top: 50%;
        transform: translateY(-50%);
        right: 4%;
        color: #fff;
        font-weight: 600;
        font-size: clamp(1.25rem, 5.5vw, 1.5rem);
        font-weight: 600;
        color: #e8d8f7;
        width: 40%;
        text-align: right;
        line-height: 1.5;

    }

}


.submenu_list {
    position: absolute;
    bottom: 0px;
    left: 0px;
    z-index: 2;
    width: 100%;

    ul {
        list-style: none;
        margin: 0px;
        padding: 10px 0px;
        white-space: nowrap;
        overflow: auto;
        -ms-overflow-style: none;
        scrollbar-width: none;
        display: flex;

        &::-webkit-scrollbar {
            display: none;
        }
    }


}

.category_item {
    background: rgb(255 255 255 / 88%);
    color: $primary;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 10px 22px;
    border-radius: 45px;
    font-size: clamp(0.688rem, 3vw, 0.875rem);
    cursor: pointer;
    margin: 0px 7px;
    font-weight: 600;
    white-space: nowrap;
}


.list {
    display: flex;
    gap: 10px;
    align-items: center;
    padding: 15px;

    a {
        border-radius: 46px;
        color: var(--primary-color);
        font-weight: 400;
    }

    svg {
        width: 14px;
        height: 14px;
        fill: rgba(45, 49, 66, 0.30);
        opacity: 0.5;
        transform: rotate(var(--arrow-rotation));
    }

}

.sublinks {
    position: relative;
}

.sublinks .next_btn,
.sublinks .prev_btn {
    position: absolute;
    padding: 20px;
    left: 0px;
    top: 0px;
    transform: translateX(-100%);
    color: #fff;
    cursor: pointer;
}

.sublinks .next_btn {
    right: 0px;
    left: auto;
    transform: translateX(100%);
}


@include Large {

    .banner_wrapper {
        border-radius: 12px;
        overflow: hidden;
    }

    .submenu_list {
        left: 50%;
        width: 90%;
        transform: translateX(-50%);
    }


    .list {
        a {
            padding: 12px 15px;
        }



    }

    .header_image {
        position: relative;
        height: 305px;

        h2 {
            top: 50%;
            transform: translateY(-45%);
            left: auto;
            right: 0px;
            width: 55%;
            text-align: center;
            font-size: clamp(1.25rem, 6vw, 2.5rem);

        }
    }

    .sub_categories_list {
        background: linear-gradient(180deg,
                #f7f7fb 0%,
                rgba(45, 49, 66, 0.06) 30%,
                rgba(#2d3142, 0.06) 100%);

    }



}