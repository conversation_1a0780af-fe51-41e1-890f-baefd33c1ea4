import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { DialogModule } from 'primeng/dialog';
import { PaginatorModule } from 'primeng/paginator';
import { CategoryRoutingModule } from 'src/app/modules/category/category-routing.module';
import { CategoryFooterComponent } from 'src/app/modules/category/component/category-footer/category-footer.component';
import { CategoryHeaderComponent } from 'src/app/modules/category/component/category-header/category-header.component';
import { MainCategoryComponent } from 'src/app/modules/category/pages/main-category/main-category.component';
import { ItemCardRowComponent } from 'src/app/shared/components/item-card-row/item-card-row.component';
import { ItemCardComponent } from 'src/app/shared/components/item-card/item-card.component';
import { ItemsSliderComponent } from 'src/app/shared/components/items-slider/items-slider.component';
import { LayoutToggleBtnComponent } from 'src/app/shared/components/layout-toggle-btn/layout-toggle-btn.component';
import { NoResultComponent } from 'src/app/shared/components/no-result/no-result.component';
import { SideFilterComponent } from 'src/app/shared/components/side-filter/side-filter.component';
import { ItemCardRowSkeletonComponent } from 'src/app/shared/components/skeleton/item-card-row-skeleton/item-card-row-skeleton.component';
import { YouMayLikeComponent } from 'src/app/shared/components/you-may-like/you-may-like.component';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { ManageCategoriesService } from './services/manage-categoies.service';


@NgModule({
    imports: [
        CommonModule,
        CategoryRoutingModule,
        DialogModule,
        SideFilterComponent,
        ItemsSliderComponent,
        ItemCardRowComponent,
        ItemCardComponent,
        CategoryHeaderComponent,
        PaginatorModule,
        YouMayLikeComponent,
        ItemCardRowSkeletonComponent,
        NtranslatePipe,
        NoResultComponent,
        LayoutToggleBtnComponent,
        CategoryFooterComponent,
        MainCategoryComponent
    ],
    providers: [ManageCategoriesService]
})
export class CategoryModule { }
