<div class="alert-body">
    <app-success-icon></app-success-icon>
    <h2>{{ message }}</h2>
    <div [ngSwitch]="btns.length > 0">
        <div *ngSwitchCase="true" class="btns_list">
            <ng-container *ngFor="let btn of btns">
                <ng-container [ngSwitch]="btn.value">
                    <app-dark-btn *ngSwitchCase="true" [btnText]="btn.title" btnType="button" [bigBtn]="true"
                        (click)="close(btn.value)"></app-dark-btn>

                    <app-gray-btn *ngSwitchDefault [btnText]="btn.title" btnType="button" [bigBtn]="true"
                        (click)="close(btn.value)"></app-gray-btn>
                </ng-container>

            </ng-container>
        </div>
        <div *ngSwitchDefault>
            <Button pButton pRipple [label]="'Ok' | translate" (click)="close(true)"></Button>
        </div>
    </div>
</div>