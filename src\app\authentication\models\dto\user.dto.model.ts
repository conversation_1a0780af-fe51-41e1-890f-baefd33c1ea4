import { UserSettingModel } from "@src/app/shared/models/profile.model";

export class User {
  id: string;
  name: string;
  firstname: string;
  lastname: string;
  userName: string;
  role: string;
  refreshToken: string;
  image: string;
  rate: string;
  userImage: string;
  token: string;
  PhoneNumber: string;
  defaultLanguage: string;
  phoneConfirmed: boolean;
  email: string;
  userID?: string;
  businessName?: string;
  setting?: UserSettingModel;
  account?: UserAccountTypeDto;

}
export interface UserForAuthenticationDto {
  email: string;
  password: string;
  clientURI: string;
}

export interface UserAccountTypeDto {
  accountType: 'consumer' | 'business';
}
export interface UserForRegistrationDto {
  userName: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
  confirmPassword: string;
  clientURI: string;
  ReferredByCode?: string;
  AccountType?: string;
}
export interface RegisterUserWithOTP {
  phoneNumber: string;
  userName: string;
  firstName: string;
  lastName: string;
  password: string;
  otp: string;
  defaultLang: string;
  areaID: number;
  ReferredByCode: string;
  AccountType: string;
  AccountSource: string;
  Platform: string;
  OSVersion: string;

}
export interface UserForAuthenticationDto {
  email: string;
  password: string;
  clientURI: string;
}
export interface VerifyPhoneNumber {
  phonenumber: string;
}
export interface VerifyPhoneNumberResponse {
  resend_token: string;
}
