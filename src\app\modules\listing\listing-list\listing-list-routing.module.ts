import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ListingStatus } from 'src/app/shared/models/listing.model';
import { FavouriteListingComponent } from './pages/favourite-listing/favourite-listing.component';
import { MyListingComponent } from './pages/my-listing/my-listing.component';

const routes: Routes = [
  // { path: '', redirectTo: 'selling', pathMatch: 'full' },

  {
    path: '',
    redirectTo: 'active',
    pathMatch: 'full'
  },
  {
    path: 'active',
    component: MyListingComponent,
    data: { type: ListingStatus.approved }
  },
  {
    path: 'waiting',
    component: MyListingComponent,
    data: { type: ListingStatus.created }
  },
  {
    path: 'rejected',
    component: MyListingComponent,
    data: { type: ListingStatus.rejected }
  },
  {
    path: 'sold',
    component: MyListingComponent,
    data: { type: ListingStatus.sold }
  },
  {
    path: 'blocked',
    component: MyListingComponent,
    data: { type: ListingStatus.blocked }
  },
  {
    path: 'favourites',
    component: FavouriteListingComponent
  },

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ListingListRoutingModule { }
