import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { LazyloadDirective } from 'src/app/shared/directives/lazyload.directive';
import { ListingView } from 'src/app/shared/models/listing.model';
import { ListingImagePathPipe } from 'src/app/shared/pipes/listing-image-path.pipe';
import { NCurrencyPipe } from 'src/app/shared/pipes/ncurrency.pipe';

@Component({
  selector: 'app-ad-mini-closable',
  standalone: true,
  imports: [CommonModule, LazyloadDirective, NCurrencyPipe, ListingImagePathPipe],
  templateUrl: './ad-mini-closable.component.html',
  styleUrls: ['./ad-mini-closable.component.scss']
})
export class AdMiniClosableComponent implements OnInit {

  listingItem: ListingView;

  @Input() set item(value) {
    if (value.images && value.images.length > 0) {
      value.imageURL = value.images[0].imageURL;
    }
    this.listingItem = value;
  };

  @Output() onClose = new EventEmitter();

  @Input() isCloseable: boolean = true;

  constructor() { }

  ngOnInit(): void {
  }

}
