import { CommonModule, NgOptimizedImage } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { Observable } from 'rxjs';
import { BannerModel } from 'src/app/shared/models/common.model';
import { CommonService } from 'src/app/shared/services/common.service';

@Component({
  selector: 'app-smart-banner',
  standalone: true,
  imports: [CommonModule, RouterModule, NgOptimizedImage],
  templateUrl: './smart-banner.component.html',
  styleUrls: ['./smart-banner.component.scss']
})
export class SmartBannerComponent implements OnInit {

  banner$: Observable<BannerModel>;
  constructor(
    private commonService: CommonService,
  ) { }

  ngOnInit(): void {
    this.banner$ = this.commonService.smartBanner$;
  }

}
