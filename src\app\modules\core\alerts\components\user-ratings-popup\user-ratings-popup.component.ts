import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NoResultComponent } from '@src/app/shared/components/no-result/no-result.component';
import { UserRateModel } from '@src/app/shared/models/profile.model';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { RatingModule } from 'primeng/rating';
import { Observable, map } from 'rxjs';
import { LazyloadDirective } from 'src/app/shared/directives/lazyload.directive';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { ProfileService } from 'src/app/shared/services/profile.service';

@Component({
  selector: 'app-user-ratings-popup',
  standalone: true,
  imports: [
    CommonModule,
    LazyloadDirective,
    NtranslatePipe,
    RatingModule,
    FormsModule,
    NoResultComponent
  ],
  templateUrl: './user-ratings-popup.component.html',
  styleUrls: ['./user-ratings-popup.component.scss']
})
export class UserRatingsPopupComponent implements OnInit {

  rates$: Observable<UserRateModel[]>;

  userRating: number = 0;

  constructor(
    private _profileService: ProfileService,
    private ref: DynamicDialogRef, public config: DynamicDialogConfig
  ) { }

  ngOnInit(): void {

    this.rates$ = this._profileService.getUserRates(this.config.data.message).pipe(map(res => res.data),);
  }

}
