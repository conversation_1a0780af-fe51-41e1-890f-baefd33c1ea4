import { Pipe, PipeTransform } from '@angular/core';
import { AdsSectionDTO } from '@src/app/shared/models/lookup.model';
import { AdsService } from '@src/app/shared/services/ads.service';

@Pipe({
    name: 'adsStrip',
    standalone: true,
    pure: true
})
export class AdsStripPipe implements PipeTransform {
    constructor(private adsService: AdsService) {

    }
    transform(ads: AdsSectionDTO[], value: string): any {

        const targetAds = ads?.filter(ad => ad.locationName === value);

        if (targetAds && targetAds.length > 0) {
        }

        return false;
    }
}