@import "mixins";
@import "variables";

h1 {
    font-size: 24px;
    font-weight: 600;

    @include rtl2(font-size, 22px);
}

.list_title {
    font-size: 18px;
}

.tabs {
    margin: 0px;
    padding: 0px;
    list-style: none;
    display: flex;
    white-space: nowrap;
    overflow: auto;
    padding-bottom: 10px;

    &::-webkit-scrollbar {
        display: none;
    }

    li {
        padding: 13px 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        cursor: pointer;

        &:hover,
        &.active {
            position: relative;
            font-weight: 600;

            &:after {
                content: "";
                position: absolute;
                width: 50%;
                height: 4px;
                background: var(--primary-color);
                left: 50%;
                bottom: 0%;
                transform: translateX(-50%);
                border-top-right-radius: 3px;
                border-top-left-radius: 3px;
            }
        }
    }

    @include Large {
        padding-bottom: 0px !important;
        height: 100%;
    }
}

.list-start {
    @include Large {
        display: flex;
        gap: 16px;
    }
}

.list-col {
    padding: 0px 16px;
    margin-top: 10px;

    @include Large {
        display: flex;
        justify-content: space-between;
        background: #fff;
        border-radius: 12px;
    }
}

.items_list {
    margin-top: 10px;
    min-height: 76vh;
    position: relative;
    z-index: 1;

    @include Large {
        margin-top: 30px;
    }
}

.list_content {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 16px;
}

.listing_dropdown {
    &::ng-deep {
        .p-dropdown {
            width: 100%;
            border: none;
            box-shadow: 0px 1px 1px #e2e2e2;
        }

        .selected-filter-value {
            display: flex;
            gap: 10px;
            align-items: center;
            font-size: 1rem;
            font-weight: bold;

            span {
                background: $orangeColor;
                width: 32px;
                height: 32px;
                border-radius: 50%;
                color: #fff;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
    }
}

.title_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.mylisting_search_box {
    background: #fff;
    height: 50px;
    display: flex;
    gap: 5px;
    align-items: center;
    border-radius: 30px;
    box-shadow: 0px 1px 1px #e2e2e2;
    flex: 1;
    overflow: hidden;
    padding: 0px 8px;

    input {
        flex: 1;
        width: 100%;
    }

    button {
        flex-shrink: 0;
        width: 31px;
        height: 31px;
        background-color: $primary;
        border-color: $primary;
        box-shadow: none !important;
        padding: 7px;
        color: #fff;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        line-height: 1;
    }

    .srch_icon {
        margin-inline-start: 0px;
        width: 20px;
        height: 20px;
    }

    @include Large {
        box-shadow: none;
        border: 1px solid rgba($color: $text-color, $alpha: 0.21)
    }
}

.top_filter_actions {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 16px 0px;
}

.mobile_filter_actions {
    display: flex;
    gap: 10px;

    &::ng-deep {
        .p-inputtext {
            height: 50px;
            display: flex;
            align-items: center;
        }
    }
}

.filter_btn {
    cursor: pointer;

    svg {
        width: 50px;
        height: 50px;
    }
}


@include Large {
    h1 {
        margin: 30px 0px;
    }

}