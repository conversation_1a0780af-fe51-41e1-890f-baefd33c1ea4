import { Component } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, UntypedFormControl, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AlertHandlerService } from 'src/app/modules/core/alerts/alert-handler.service';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { AuthService } from 'src/app/shared/services/auth.service';
import { PhoneInputComponent } from '../../../../shared/components/inputs/phone-input/phone-input.component';
import { SecondaryBtnComponent } from '../../../../shared/components/secondary-btn/secondary-btn.component';
import { NtranslatePipe } from '../../../../shared/pipes/ntranslate.pipe';
import { PartnerService } from '../../services/partner.service';

@Component({
  selector: 'app-partner-edit-phone',
  standalone: true,
  imports: [FormsModule, ReactiveFormsModule, PhoneInputComponent, SecondaryBtnComponent, NtranslatePipe],
  templateUrl: './partner-edit-phone.component.html',
  styleUrl: './partner-edit-phone.component.scss'
})
export class PartnerEditPhoneComponent {
  countries!: any[];
  selectedCountry!: any;

  public editPhoneForm: FormGroup;

  constructor(private _formBuilder: FormBuilder, private _router: Router, private translateService: TranslationService,

    private alertService: AlertHandlerService, private _authService: AuthService, private partnerService: PartnerService) {
    // Countries
    this.countries = [
      { name: '+20', code: 'EG' },
      // { name: '+966', code: 'SA' },
      // { name: '+971', code: 'AE' },
    ];

    this.selectedCountry = this.countries[0];
  }

  ngOnInit(): void {
    this.editPhoneForm = this._formBuilder.group({
      phone: new UntypedFormControl(null, [
        Validators.required,
        Validators.pattern(
          /^[\+]?[0-9]+[\s]?,01[0-5]\d{1,8}/
        ),
        Validators.minLength(15)
      ]),

    });
  }

  public validateControl = (controlName: string) => {
    return (
      this.editPhoneForm.controls[controlName].invalid &&
      this.editPhoneForm.controls[controlName].touched
    );
  };

  public hasError = (controlName: string, errorName: string) => {
    return this.editPhoneForm.controls[controlName].hasError(errorName);
  };

  editPhone(EditPhoneFormValue: any) {
    const phoneDetails = this.editPhoneForm.controls['phone'].value.split(',');

    this._authService
      .phoneNumberExists(phoneDetails[1])
      .subscribe((x) => {
        if (!x) {
          this.partnerService.update('phone', phoneDetails[1]);

          this._router.navigate(['/authentication/register-partner/verification']);
        }
        else {
          this.alertService.error({ message: this.translateService.instant('PhoneNumberExists') });
        }
      });

  }
}
