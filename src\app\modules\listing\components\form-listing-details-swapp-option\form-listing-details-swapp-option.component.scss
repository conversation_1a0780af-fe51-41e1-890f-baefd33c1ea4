@import "mixins";
@import "variables";
@import "../../style/common.scss";

.white_box {
    border-radius: 12px;
    background: url(/assets/images/swapp_open.png) #fff no-repeat bottom right;
    padding: 20px 24px;
    position: relative;
    margin-bottom: 25px;
    background-size: 41%;
    cursor: pointer;
    transition: all 0.3s ease-out;

    &.active{
        border: 1px solid $orangeColor;
    }

    &.specific {
        background: url(/assets/images/swapp_specific.png) #fff no-repeat bottom right;
        background-size: 41%;
    }

    
    p{
        width: 57%;
        font-size: 14px;
        font-weight: 400;
        line-height: 2;
    }
    h2{
        font-size: 20px;
        font-weight: 800;
        margin-bottom: 15px;
    }
}


@include Large{
    .white_box {
        height: 235px;
    }
}
@include XLarge{
    .white_box {
        background-size: 52%;
        height: 415px;
        padding: 50px;
        &.specific {
            background-size: 52%;
        }
        p{
            font-size: 24px;
        }
        h2{
            font-size: 32px;
        }
    }
    
}