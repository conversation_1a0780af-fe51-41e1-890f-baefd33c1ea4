import { Pipe, PipeTransform } from '@angular/core';
import { Router } from '@angular/router';
import { HomeSections } from 'src/app/shared/models/listing.model';

@Pipe({
  name: 'homesectionlink',
  standalone: true,
  pure: true
})
export class HomesectionlinkPipe implements PipeTransform {

  constructor(private router?: Router) { }

  transform(item: HomeSections): unknown {

    if (item.filters) {
      const filters = Object.keys(item.filters);
      const queryParams = Object.keys(item.filters)
        .filter(filter => item.filters[filter] !== null)
        .map(filter => `${filter}=${item.filters[filter]}`)
        .join('&');

      const params = {};
      filters.forEach(filter => {
        params[filter] = item.filters[filter];
      });



      return ['/search', { queryParams: params }];
    }

    return item.type == 'category' ? ('/category/' + (item.parentId ? item.parentId + '/' + item.id : item.id)) : '/tag/' + (item.label);
  }



}
