import { Inject, Injectable, InjectionToken } from "@angular/core";
import { OAuthStorage } from "angular-oauth2-oidc";

export const BROWSER_STORAGE = new InjectionToken<Storage>('Browser Storage');

@Injectable({
    providedIn: 'root'
})
export class BrowserStorageService implements OAuthStorage {
    constructor(@Inject(BROWSER_STORAGE) private storage: Storage) { }

    getItem(key: string): string | null {
        return this.storage.getItem(key);
    }

    setItem(key: string, value: string): void {
        this.storage.setItem(key, value);
    }

    removeItem(key: string): void {
        this.storage.removeItem(key);
    }

    clear(): void {
        this.storage.clear();
    }
}
