<div class="auth_form_wrapper personal_info">
  <app-progress-head [progressVal]="25" [showProgress]="true"></app-progress-head>
  <div class="row">
    <div class="col-md-8 col-lg-10 col-xl-8 mx-auto mw-form">
      <h3>{{ 'Complete Your Info' | translate}}</h3>
      <form [formGroup]="form" autocomplete="off" novalidate (ngSubmit)="editPersonalInfo(form.value)">
        <label pRipple class="upload_photo">
          <input #imgSrc type="file" type="file" accept="image/*, .heic, .heif" (change)="handleFileInput($event)" />
          <img src="{{imageBase}}" alt="" id="uploadedImg" *ngIf="imageBase; else camIcon" />
          <ng-template #camIcon>
            <img src="assets/img/camera-icon.svg" alt="" class="camera_icon" />
          </ng-template>
        </label>

        <div class="inputs_holder">
          <div class="row">
            <div class="col-md-12 mb-4">
              <input type="text" pInputText class="w-100 input_holder" [placeholder]="'First Name' | translate"
                formControlName="firstName" [maxlength]="20" [ngClass]="{
                  error_inpt:
                    validateControl('firstName')
                }" />
              <ul class="error_list" *ngIf="validateControl('firstName')">
                <li *ngFor="let error of form.get('firstName')!.errors | keyvalue">
                  <i class="info_icon fail">
                    <svg data-id="exclamation_icon" viewBox="0 0 3 12" fill="none">
                      <path
                        d="M2.37571 0.818182L2.18182 7.94744H0.362216L0.163352 0.818182H2.37571ZM1.27202 11.1293C0.943892 11.1293 0.662169 11.0133 0.426847 10.7812C0.191525 10.5459 0.0755209 10.2642 0.0788353 9.93608C0.0755209 9.61127 0.191525 9.33286 0.426847 9.10085C0.662169 8.86884 0.943892 8.75284 1.27202 8.75284C1.58688 8.75284 1.86364 8.86884 2.10227 9.10085C2.34091 9.33286 2.46188 9.61127 2.4652 9.93608C2.46188 10.1548 2.40388 10.3554 2.29119 10.5376C2.18182 10.7166 2.03764 10.8608 1.85866 10.9702C1.67969 11.0762 1.48414 11.1293 1.27202 11.1293Z"
                        fill="currentColor" />
                    </svg>
                  </i>{{ error.value | translate }}
                </li>
              </ul>
            </div>
            <div class="col-md-12 mb-4">
              <input type="text" pInputText class="w-100 input_holder" [placeholder]="'Last Name' | translate"
                formControlName="lastName" [maxlength]="20" [ngClass]="{
                  error_inpt:
                    validateControl('lastName')
                }" />
              <ul class="error_list" *ngIf="validateControl('lastName')">
                <li *ngFor="let error of form.get('lastName')!.errors | keyvalue">
                  <i class="info_icon fail">
                    <svg data-id="exclamation_icon" viewBox="0 0 3 12" fill="none">
                      <path
                        d="M2.37571 0.818182L2.18182 7.94744H0.362216L0.163352 0.818182H2.37571ZM1.27202 11.1293C0.943892 11.1293 0.662169 11.0133 0.426847 10.7812C0.191525 10.5459 0.0755209 10.2642 0.0788353 9.93608C0.0755209 9.61127 0.191525 9.33286 0.426847 9.10085C0.662169 8.86884 0.943892 8.75284 1.27202 8.75284C1.58688 8.75284 1.86364 8.86884 2.10227 9.10085C2.34091 9.33286 2.46188 9.61127 2.4652 9.93608C2.46188 10.1548 2.40388 10.3554 2.29119 10.5376C2.18182 10.7166 2.03764 10.8608 1.85866 10.9702C1.67969 11.0762 1.48414 11.1293 1.27202 11.1293Z"
                        fill="currentColor" />
                    </svg>
                  </i>{{ error.value | translate }}
                </li>
              </ul>
            </div>
          </div>
        </div>
        <app-secondary-btn [btnText]="'Next' | translate" btnType="submit" [bigBtn]="true"
          [btnDisabled]="!form.valid"></app-secondary-btn>
      </form>
    </div>
  </div>
</div>