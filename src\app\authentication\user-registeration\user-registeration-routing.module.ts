import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AccountTypeComponent } from './account-type/account-type.component';
import { ChooseCityComponent } from './choose-city/choose-city.component';
import { EditPhoneNumberComponent } from './edit-phone-number/edit-phone-number.component';
import { InterestsComponent } from './interests/interests.component';
import { NewAccountComponent } from './new-account/new-account.component';
import { PersonalInfoComponent } from './personal-info/personal-info.component';
import { SuccessComponent } from './success/success.component';
import { UserRegisterationComponent } from './user-registeration.component';
import { VerificationComponent } from './verification/verification.component';

const routes: Routes = [
  {
    path: '',
    component: UserRegisterationComponent,
    children: [
      {
        path: 'verification',
        component: VerificationComponent,
      },
      {
        path: 'edit-phone-number',
        component: EditPhoneNumberComponent,
      },
      {
        path: 'personal-info',
        component: PersonalInfoComponent,
      },
      {
        path: '',
        component: AccountTypeComponent,
      },
      {
        path: 'choose-city',
        component: ChooseCityComponent,
      },
      {
        path: 'interests',
        component: InterestsComponent,
      },
      {
        path: 'success',
        component: SuccessComponent,
      },
      {
        path: 'new-account',
        component: NewAccountComponent,
      },
      {
        path: ':code',
        component: AccountTypeComponent,
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class UserRegisterationRoutingModule { }
