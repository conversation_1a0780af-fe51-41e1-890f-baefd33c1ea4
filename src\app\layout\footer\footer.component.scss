@import "mixins";
@import "variables";

.footer {
    background: #fff;
    padding: 16px 25px;

    .row>* {
        padding: 0px;
    }
}

.footer_desktop {
    position: relative;
    margin-top: 7px;
}

.footer_desktop span {
    display: block;
    transform: translateY(25px);
    font-size: 18px;
}

.footer_mobile {
    background: #fff;
    margin-top: 30px;


    div {
        background: linear-gradient(270deg, rgb(255, 255, 255) 0%, rgb(242 101 34 / 30%) 100%);
        display: flex;
        align-items: center;
        justify-content: start;
        position: relative;
        height: 90px;
        @include rtl2(background, linear-gradient(98deg, rgb(255, 255, 255) 0%, rgb(242 101 34 / 30%) 100%));
    }

    img {
        transform: translateY(-10px);
        margin-inline-start: 20px;
        margin-inline-end: 25px;

        @include rtl(margin-inline-start, 34px);
        @include rtl(margin-inline-end, 0px);


    }

    a {
        background: var(--primary-color);
        color: #fff;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0px 20px;
        border-radius: 35px;
    }
}



.footer h2 {
    font-size: 18px;
    padding: 41px 0px 0px;
    color: $text-color;
    margin-bottom: 19px;
}

.footer_info {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 19px;
    font-size: 16px;
    width: 100%;

}

.footer_info i {
    width: 53px;
    height: 53px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 15px;
    background: #2d31420f;

}

.footer_info svg {
    width: 18px;
    height: 18px;
}


.contacts_box {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.social a {
    display: inline-flex;
    justify-content: center;
    align-items: center;

    img {
        width: 45px;
        height: 45px;
    }
}

.social {
    display: flex;
    justify-content: space-between;
}

.copyright {
    padding: 50px 0px;
    color: $text-color;
    font-size: 14px;
    margin: 0px 0px 24px;
}

.links {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding-top: 40px;
    gap: 20px;
    margin-bottom: 22px;
}


.links a {
    color: #000;
    font-size: 18px;
}



.app_text span {
    padding: 20px;
    font-size: 18px;
}

.app_text {
    display: flex;
}

.app_links {
    display: flex;
    gap: 20px;
    padding: 20px 0px;
}

.app_links img {
    max-width: 100%;
}

a {
    color: $text-color;
}

@include Large {
    .footer {
        h2 {
            min-height: 61px;
            margin-bottom: 20px;
        }

        i {
            width: 38px;
            height: 38px;
        }
    }

    .contacts_box {
        gap: 12px;
    }


    .app_text span {
        padding: 42px;
        font-size: 24px;
    }

    .contacts_col {
        display: flex;
        justify-content: space-around
    }

    .social {
        justify-content: flex-start;
        gap: 24px;

        a {
            img {
                width: 38px;
                height: 38px;
            }
        }
    }


}

@include XLarge {
    .footer_info {
        width: auto;
    }

    .links {
        gap: 14px;

        a {
            font-size: 16px;
        }
    }
}