import { Inject, Injectable, Optional } from '@angular/core';
import { SERVER_CACHE_KEY } from '@src/app/shared/services/common.service';

@Injectable({
    providedIn: 'root',
})
export class ServerCacheService {

    constructor(@Optional() @Inject(SERVER_CACHE_KEY) private cache: any) { }

    setCache(key: string, data: any) {
        this.cache.set(key, data, 60000);
    }

    async getCache(key: string) {
        const cacheEntry = await this.cache.get(key);
        if (cacheEntry) {
            return cacheEntry;
        }
        return null;
    }



    deleteCache(key: string) {
        this.cache.delete(key);
    }



}



