@import "variables";
@import "mixins";

.list_title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 16px;

    b {
        width: 30px;
        height: 30px;
        background: $orangeColor;
        color: #fff;
        font-weight: normal;
        font-size: 1rem;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
    }
}



.offers_box {
    background: #fff;
    padding: 15px;
    border-radius: 12px;


    &::ng-deep {
        .offer_card {
            border: 1px solid rgba($color: $text-color, $alpha: 0.16);
            border-radius: 12px;
        }
    }

    @include Large {
        box-shadow: none;

    }
}

.list_content {
    max-height: 80vh;
    overflow: auto;
}

.tabs {
    margin: 0px;
    padding: 0px;
    list-style: none;
    display: flex;
    white-space: nowrap;
    overflow: auto;
    padding-bottom: 10px;

    &::-webkit-scrollbar {
        display: none;
    }

    li {
        padding: 13px 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        cursor: pointer;

        &:hover,
        &.active {
            position: relative;
            font-weight: 600;

            &:after {
                content: "";
                position: absolute;
                width: 50%;
                height: 4px;
                background: var(--primary-color);
                left: 50%;
                bottom: 0%;
                transform: translateX(-50%);
                border-top-right-radius: 3px;
                border-top-left-radius: 3px;
            }
        }
    }

    @include Large {
        padding-bottom: 0px !important;
        height: 100%;
    }
}