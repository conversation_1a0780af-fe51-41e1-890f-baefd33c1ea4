import { DOCUMENT, isPlatformBrowser } from '@angular/common';
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';

declare const gtag: Function;

@Injectable({
    providedIn: 'root'
})
export class BrowserService {
    platformId: any;
    constructor(@Inject(PLATFORM_ID) private pId,
        @Inject(DOCUMENT) private document: Document) {
        this.platformId = pId;
    }

    isSlowNetwork() {
        let isSLow = false;
        if (this.isBrowser()) {
            const connection = (navigator as any);
            const networkConnection = connection.connection || connection.mozConnection || connection.webkitConnection;
            isSLow = ((networkConnection && networkConnection.effectiveType === 'slow-2g') || (networkConnection && networkConnection.effectiveType === '2g') || (navigator.userAgent.match('Moto G') != null));
        }
        return isSLow;
    }

    isBrowser() {
        return isPlatformBrowser(this.platformId);;
    }



    scrollTo(obj: any) {
        if (this.isBrowser()) {
            window.scrollTo(obj);
        }
    }

    openShare(url) {
        if (this.isBrowser()) {
            let currentLink = location.href;
            currentLink = encodeURIComponent(currentLink);
            window.open(url + currentLink, '_blank');
        }
    }

    setStorageItem(key, value) {
        if (this.isBrowser()) {
            localStorage.setItem(key, value);
        }
    }

    open(url: string) {
        if (this.isBrowser()) {
            return window.open(url, '_blank');
        }
        return undefined;
    }

    getStorageItem(key) {
        if (this.isBrowser()) {
            return localStorage.getItem(key);
        }
        return '';
    }

    addClassToBody(className: string) {
        if (this.isBrowser()) {
            this.document.body.classList.add(className);
        }
    }

    removeStorageItem(key) {
        if (this.isBrowser()) {
            localStorage.removeItem(key);
        }
    }

    getWindowVariable(value) {
        let v = value;
        if (this.isBrowser()) {
            return String(window[value]);
        }
        return undefined;

    }

    reload() {
        if (this.isBrowser()) {
            return window.location.reload();
        }

    }

    href(url: string) {
        if (this.isBrowser()) {
            return window.location.href = url;
        }
        return undefined;
    }

    getPathName() {
        if (this.isBrowser()) {
            return location.pathname;
        }

        return '';

    }

}
