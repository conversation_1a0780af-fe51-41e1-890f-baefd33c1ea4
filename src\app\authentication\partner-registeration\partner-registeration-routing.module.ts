import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PartnerEditPhoneComponent } from './page/partner-edit-phone/partner-edit-phone.component';
import { PartnerIndexComponent } from './page/partner-index/partner-index.component';
import { PartnerInterestsComponent } from './page/partner-interests/partner-interests.component';
import { PartnerRegisterationComponent } from './page/partner-registeration/partner-registeration.component';
import { PartnerSuccessComponent } from './page/partner-success/partner-success.component';
import { PartnerVerificationComponent } from './page/partner-verification/partner-verification.component';

const routes: Routes = [{
  path: '', component: PartnerRegisterationComponent,
  children: [
    {
      path: 'verification',
      component: PartnerVerificationComponent,
    },
    {
      path: 'edit-phone-number',
      component: PartnerEditPhoneComponent,
    },
    {
      path: 'interests',
      component: PartnerInterestsComponent,
    },
    {
      path: 'success',
      component: PartnerSuccessComponent,
    },
    {
      path: '',
      component: PartnerIndexComponent,
    },
    {
      path: ':code',
      component: PartnerIndexComponent,
    },
  ],
}];


@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PartnerRegisterationRoutingModule { }
