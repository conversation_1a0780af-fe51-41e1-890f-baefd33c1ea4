import { inject, Injectable, makeStateKey, TransferState } from "@angular/core";
import { environment } from "@src/environments/environment";

type PageLoad = Awaited<ReturnType<typeof pageLoad>>;

const key = '4swappData';


// Abstract
@Injectable()
export abstract class DataService {
    abstract value: PageLoad | null;
    abstract load(savedLanguage: string, isMobile: number): Promise<void>;
}

// Server
@Injectable()
export class DataServiceServer extends DataService {
    state = inject(TransferState);
    value: PageLoad | null = null;
    async load(savedLanguage: string = 'ar-eg', isMobile: number = 1) {
        const data = await pageLoad(savedLanguage, isMobile);
        this.state.set(makeStateKey<PageLoad>(key), data);
        this.value = data;
    }
}

// Browser
@Injectable()
export class DataServiceBrowser extends DataService {
    state = inject(TransferState);
    value: PageLoad | null = null;
    async load(savedLanguage: string = 'ar-eg', isMobile: number = 1) {
        const data = this.state.get(makeStateKey<PageLoad>(key), null);
        this.value = data;
    }
}



async function pageLoad(savedLanguage: string = 'ar-eg', isMobile: number = 1) {
    let categoriesData = '';
    let adsData = [];
    let settingsData = [];
    let languagesData = {};

    if (environment.production) {
        const response = await fetch(`${environment.clintURL}/api/cache/common/${savedLanguage}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        if (response.ok) {
            const data = await response.json();
            categoriesData = data.data?.categoriesData;
            adsData = data.data?.adsData;
            settingsData = data.data?.settingsData;
            languagesData = data.data?.languagesData;
        }
        return { languagesData, settingsData, adsData, categoriesData };
    }





    const categories_response = await fetch(`${environment.userURL}/api/render/Category/v2/home`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Accept-Language': savedLanguage
        }
    });


    if (categories_response.ok) {
        const data = await categories_response.json();
        categoriesData = data.data;
    }

    // Load ads
    const ads_response = await fetch(`${environment.userURL}/api/spMediaProvider?IsMobile=${isMobile ? 'true' : 'false'}&Language=${savedLanguage}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Accept-Language': savedLanguage
        }
    });

    if (ads_response.ok) {
        const data = await ads_response.json();
        adsData = data.data;
    }

    // Load settings
    const settings_response = await fetch(`${environment.userURL}/api/GeneralSettings?platform=1`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Accept-Language': savedLanguage
        }
    });

    if (settings_response.ok) {
        const data = await settings_response.json();
        settingsData = data.data;
    }

    // Load languages
    const languages_response = await fetch(`${environment.adminURL}/api/language/i18n?lang=${savedLanguage}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Accept-Language': savedLanguage
        }
    });

    if (languages_response.ok) {
        const data = await languages_response.json();

        const lowerCaseTranslations: any = {};
        for (const key in data) {
            if (data.hasOwnProperty(key)) {
                lowerCaseTranslations[key.trim().toLowerCase()] = data[key];
            }
        }
        languagesData = lowerCaseTranslations;
    }

    return { languagesData, settingsData, adsData, categoriesData };

}