import { DOCUMENT, isPlatformBrowser } from '@angular/common';
import { Component, Inject, PLATFORM_ID } from '@angular/core';

@Component({
  selector: 'app-google-tag-manager',
  standalone: true,
  imports: [],
  templateUrl: './google-tag-manager.component.html',
  styleUrl: './google-tag-manager.component.scss'
})
export class GoogleTagManagerComponent {
  gtmId = 'GTM-PCZ9LRFC';
  constructor(
    @Inject(PLATFORM_ID) private readonly platformId: Object,
    @Inject(DOCUMENT) private document: Document

  ) {
    if (isPlatformBrowser(this.platformId)) {
      // const scriptGtag = this.document.createElement('script') as HTMLScriptElement;
      // scriptGtag.src = `//www.googletagmanager.com/gtag/js?id=${this.gtmId}`;
      // scriptGtag.async = true;
      // this.document.head.appendChild(scriptGtag);

      // const scriptInit = this.document.createElement('script') as HTMLScriptElement;
      // const scriptBody = this.document.createTextNode(`
      //   window.dataLayer = window.dataLayer || [];
      //   function gtag() {
      //     dataLayer.push(arguments);
      //   }
      //   gtag('js', new Date());
      //   gtag('config', '${this.gtmId}');
      // `);

      // scriptInit.appendChild(scriptBody);
      // this.document.head.appendChild(scriptInit);
    }
  }
}
