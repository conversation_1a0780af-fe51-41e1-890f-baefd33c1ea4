import { NgIf } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { SvgRegistryService } from '../services/svg-registry.service';

@Component({
  selector: 'app-svg-icon',
  standalone: true,
  imports: [NgIf],
  template: `
    <svg [attr.width]="size" [attr.height]="size" *ngIf="isLoaded">
      <use [attr.href]="'#icon-' + name"></use>
    </svg>
    <div *ngIf="!isLoaded" [style.width.px]="size" [style.height.px]="size"></div>
  `,
  styles: []
})
export class SvgIconComponent implements OnInit {
  @Input() name: string = '';
  @Input() size: number = 24;
  @Input() color: string = 'currentColor';
  isLoaded = false;

  private subscription: Subscription | null = null;

  constructor(private svgRegistry: SvgRegistryService) { }

  ngOnInit(): void {
    // Check if already loaded first
    if (this.svgRegistry.isIconLoaded(this.name)) {
      this.isLoaded = true;
      return;
    }

    // Otherwise subscribe to load
    this.subscription = this.svgRegistry.loadSvg(this.name).subscribe(success => {
      this.isLoaded = success;
    });
  }

  ngOnDestroy(): void {
    // Clean up subscription when component is destroyed
    if (this.subscription) {
      this.subscription.unsubscribe();
      this.subscription = null;
    }
  }
}