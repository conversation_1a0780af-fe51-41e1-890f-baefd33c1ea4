import { isPlatformBrowser } from '@angular/common';
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { AlertHandlerService } from '@src/app/modules/core/alerts/alert-handler.service';

@Injectable({
    providedIn: 'root'
})
export class HeicConversionService {
    private heicConvert: any;

    constructor(@Inject(PLATFORM_ID) private platformId: Object, private alertService: AlertHandlerService) {
        if (isPlatformBrowser(this.platformId)) {
            import('heic-to').then((module) => {
                this.heicConvert = module.heicTo;
            });
        }
    }

    async convertIfHeic(file: File): Promise<File> {

        if (/\.hei(c|f)+$/.test(file.name.toLowerCase())) {

            this.alertService.loader({ message: 'uploading_images' }, null, false);



            const blog = await this.heicConvert({
                blob: file,
                type: "image/jpeg",
                quality: 0.5
            });

            this.alertService.closeLoader();



            const fileName = file.name.replace(/\.[^/.]+$/, ".jpg")
            return this.blobToFile(blog, fileName)
        }
        return file;
    }
    blobToFile(theBlob: Blob, fileName: string): File {
        const options: FilePropertyBag = { type: theBlob.type };
        return new File([theBlob], fileName, options);
    }
}