<div class="all-content" [ngClass]="{
    'hide-search' : ((hideSearchBar$ | async) && device.isMobile) || ((hideSearchBar$ | async) && device.isTablet),
   }">
    <app-open-app-nav />
    <div class="header_wrapper">
        <app-top-nav></app-top-nav>
    </div>

    <app-strip
        [hideSearch]="((hideSearchBar$ | async) && device.isMobile) || ((hideSearchBar$ | async) && device.isTablet)"></app-strip>

    <div class="min-h-container">
        <router-outlet></router-outlet>

    </div>

    <div>
        <app-footer></app-footer>

    </div>

</div>