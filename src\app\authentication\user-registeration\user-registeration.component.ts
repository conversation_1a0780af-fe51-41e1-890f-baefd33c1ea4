import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { MetaService } from '@src/app/modules/core/service/meta.service';
import { AuthImgSideComponent } from '../../shared/components/auth-img-side/auth-img-side.component';
import { LanguageButtonComponent } from '../../shared/components/language-button/language-button.component';

@Component({
  selector: 'app-user-registeration',
  templateUrl: './user-registeration.component.html',
  styleUrls: ['./user-registeration.component.scss'],
  standalone: true,
  imports: [
    AuthImgSideComponent,
    RouterOutlet,
    LanguageButtonComponent,
  ],
})
export class UserRegisterationComponent implements OnInit {

  constructor(private metService: MetaService) { }

  ngOnInit(): void {

    this.metService.set({
    });
  }

}
