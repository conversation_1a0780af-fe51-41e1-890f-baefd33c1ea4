import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, UntypedFormControl, Validators } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { AuthService } from '@services/auth.service';
import { ButtonDirective } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { Ripple } from 'primeng/ripple';
import { AlertHandlerService } from 'src/app/modules/core/alerts/alert-handler.service';
import { BrowserService } from 'src/app/modules/core/service/browser.service';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { PatternValidatorService } from 'src/app/shared/services/pattern-validator/pattern-validator.service';
import { AuthSocialComponent } from '../../../shared/components/auth-social/auth-social.component';
import { PasswordInputComponent } from '../../../shared/components/inputs/password-input/password-input.component';
import { PhoneInputComponent } from '../../../shared/components/inputs/phone-input/phone-input.component';
import { PrimaryBtnComponent } from '../../../shared/components/primary-btn/primary-btn.component';
import { SecondaryBtnComponent } from '../../../shared/components/secondary-btn/secondary-btn.component';
import { NtranslatePipe } from '../../../shared/pipes/ntranslate.pipe';

@Component({
  selector: 'app-new-account',
  templateUrl: './new-account.component.html',
  styleUrls: ['./new-account.component.scss'],
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [
    ButtonDirective,
    Ripple,
    RouterLink,
    FormsModule,
    ReactiveFormsModule,
    PhoneInputComponent,
    PasswordInputComponent,
    CheckboxModule,
    SecondaryBtnComponent,
    AuthSocialComponent,
    PrimaryBtnComponent,
    NtranslatePipe,
  ],
})
export class NewAccountComponent implements OnInit, AfterViewInit {
  countries!: any[];
  selectedCountry!: any;
  public registerForm: FormGroup;
  constructor(
    private _authService: AuthService,
    private _formBuilder: FormBuilder,
    private translateService: TranslationService,
    private _router: Router,
    private alertService: AlertHandlerService,
    private cdr: ChangeDetectorRef,
    private browser: BrowserService
  ) {
    // Countries
    this.countries = [
      { name: '+20', code: 'EG' },
      // { name: '+966', code: 'SA' },
      // { name: '+971', code: 'AE' },
    ];

    this.selectedCountry = this.countries[0];
  }
  ngAfterViewInit(): void {
    this.cdr.detectChanges();
  }

  ngOnInit(): void {
    this.browser.removeStorageItem('firstName');
    this.browser.removeStorageItem('lastName');
    this.browser.removeStorageItem('image');

    this.registerForm = this._formBuilder.group({
      phone: new UntypedFormControl(null, [
        Validators.required,
        Validators.pattern(/^[\+]?[0-9]+[\s]?,01[0-5]\d{1,8}/),
        Validators.minLength(15),
      ]),
      password: new FormControl(
        '',
        Validators.compose([
          Validators.required,
          PatternValidatorService.PatternValidator(/^(?=.*[A-Z])(?=.*[a-z])/, {
            hasUppercaseAndLowercase: true,
          }),
          PatternValidatorService.PatternValidator(/^(?=.*[0-9]).*$/, { hasNumbers: true }),
          PatternValidatorService.PatternValidator(/^(?=.*[^a-zA-Z0-9])(?!.*\s).*$/, { hasSymbols: true }),
          Validators.minLength(8),
          Validators.maxLength(32),
        ])
      )
    });

  }

  get registerFormControl() {
    return this.registerForm.controls;
  }

  validateValue(controlName: string) {
    return (
      this.registerForm.controls[controlName].valid &&
      this.registerForm.controls[controlName].touched
    );
  }

  getValue(controlName: string) {
    return this.registerForm.controls[controlName].value;
  }

  public validateControl = (
    controlName: string,
    checkTouched: boolean = true
  ) => {
    if (checkTouched) {
      return (
        this.registerForm.controls[controlName].valid &&
        this.registerForm.controls[controlName].touched
      );
    }

    return this.registerForm.controls[controlName].valid;
  };

  checkFormIsInValid(): boolean {
    return !(
      this.validateControl('phone') &&
      this.validateControl('password')
    );
  }

  isControlInvalid(controlName: string): boolean {
    const control = this.registerForm.get(controlName);
    return control!.errors !== null;
  }

  public hasError = (controlName: string, errorName: string) => {
    return this.registerForm.controls[controlName].hasError(errorName);
  };

  submit() {
    if (!this.registerForm.valid) { return }
    const phoneDetails = this.registerForm.controls['phone'].value.split(',');
    this._authService.phoneNumberExists(phoneDetails[1]).subscribe((x) => {


      if (!x) {
        this.browser.setStorageItem('phone', phoneDetails[1]);
        this.browser.setStorageItem(
          'password',
          this.registerForm.controls['password'].value
        );

        this._router.navigate(['/authentication/register/personal-info']);
      } else {
        this.alertService.error({
          message: this.translateService.instant('PhoneNumberExists'),
        });
      }
    });
  }
  login() {
    //debugger;
    this._authService.redirectLogin();
  }
}
