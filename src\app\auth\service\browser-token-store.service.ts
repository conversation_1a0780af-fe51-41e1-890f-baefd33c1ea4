import { Injectable } from '@angular/core';
import { OAuthStorage, } from "angular-oauth2-oidc";
import { SsrCookieService } from 'ngx-cookie-service-ssr';


@Injectable()
export class BrowserTokenStoreService implements OAuthStorage {

  constructor(private cookieService: SsrCookieService) {
  }


  getItem(key: string): string {
    return this.cookieService.get(key);
  }

  removeItem(key: string): void {
    this.cookieService.delete(key);
  }

  setItem(key: string, data: string): void {
    this.cookieService.set(key, data);
  }

  // cookie related functions removed for brevity
}
