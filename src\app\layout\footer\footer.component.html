<div class="footer_mobile" *ngIf="!(ds.isDesktop)">
  <div>
    <img [ngSrc]="'assets/images/download_app_mobile.webp'" alt="4swapp mobile app" width="178" height="110" priority />
    <a href="https://dl.4sw.app" target="_blank" rel="nofollow">
      {{ 'Download app' | translate }}
    </a>
  </div>
</div>


<div class="footer">
  <div class="container">
    <div class="row">
      <div class="col-md-6 col-lg-4">
        <h2>{{ 'Follow Us' | translate }}</h2>
        <div>
          <div class="social">
            <a href="https://www.facebook.com/profile.php?id=61553064838325" target="_blank" rel="nofollow"><img
                [ngSrc]="'assets/img/icons/facebook.svg'" alt="4swapp facebook" width="45" height="45"
                fetchpriority="low" /></a>
            <a href="https://www.instagram.com/4swapp/" target="_blank" rel="nofollow">
              <img [ngSrc]="'assets/img/icons/Instagram.svg'" alt="4swapp instagram" width="45" height="45" /></a>
            <a href="https://twitter.com/4Swapp" target="_blank"><img [ngSrc]="'assets/img/icons/twitter.svg'"
                alt="4swapp twitter" width="45" height="45" fetchpriority="low" /></a>
            <a href="https://www.linkedin.com/company/4swapp" target="_blank" rel="nofollow"><img
                [ngSrc]="'assets/img/icons/linkedin.svg'" alt="4swapp linkedin" width="45" height="45"
                fetchpriority="low" /></a>
            <a href="https://www.tiktok.com/@4swapp?lang=ar" target="_blank" rel="nofollow"><img
                [ngSrc]="'assets/img/icons/tiktok.svg'" alt="4swapp tiktok" width="45" height="45"
                fetchpriority="low" /></a>

            <a href="https://www.youtube.com/@4Swapp" target="_blank" rel="nofollow"><img
                [ngSrc]="'assets/img/icons/youtube.svg'" alt="4swapp youtube" width="45" height="45"
                fetchpriority="low" /></a>
          </div>
        </div>
      </div>
      <div class="col-lg-3 footer_desktop" *ngIf="ds.isDesktop">
        <a href="https://dl.4sw.app" target="_blank" rel="nofollow">
          <span>{{ 'Download App' | translate }}</span>

          <img [ngSrc]="'assets/images/download_app_desktop.webp?v=1.0.1'" alt="4swapp desktop app" width="246"
            height="185" priority />
        </a>
      </div>

      <div class="col-lg-5 contacts_col">
        <div>
          <h2>{{ 'Contact Us' | translate}}</h2>
          <div class="contacts_box">
            <a target="_blank" href="https://wa.me/201098684444" class="footer_info" rel="nofollow">
              <svg data-id="whatsapp_icon" viewBox="0 0 58 58" space="preserve" fill="#000000">
                <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                <g id="SVGRepo_iconCarrier">
                  <g>
                    <path style="fill:#2CB742;"
                      d="M0,58l4.988-14.963C2.457,38.78,1,33.812,1,28.5C1,12.76,13.76,0,29.5,0S58,12.76,58,28.5 S45.24,57,29.5,57c-4.789,0-9.299-1.187-13.26-3.273L0,58z">
                    </path>
                    <path style="fill:#FFFFFF;"
                      d="M47.683,37.985c-1.316-2.487-6.169-5.331-6.169-5.331c-1.098-0.626-2.423-0.696-3.049,0.42 c0,0-1.577,1.891-1.978,2.163c-1.832,1.241-3.529,1.193-5.242-0.52l-3.981-3.981l-3.981-3.981c-1.713-1.713-1.761-3.41-0.52-5.242 c0.272-0.401,2.163-1.978,2.163-1.978c1.116-0.627,1.046-1.951,0.42-3.049c0,0-2.844-4.853-5.331-6.169 c-1.058-0.56-2.357-0.364-3.203,0.482l-1.758,1.758c-5.577,5.577-2.831,11.873,2.746,17.45l5.097,5.097l5.097,5.097 c5.577,5.577,11.873,8.323,17.45,2.746l1.758-1.758C48.048,40.341,48.243,39.042,47.683,37.985z">
                    </path>
                  </g>
                </g>
              </svg> <span class="ltr_text">01098684444</span>
            </a>
            <a href="mailto:<EMAIL>" target="_blank" rel="nofollow">
              <div class="footer_info">
                <svg data-id="email_icon" viewBox="0 0 16 13" fill="none">
                  <rect opacity="0.7" x="1.17969" y="0.261719" width="13.9181" height="12.5263" rx="6" fill="#2D3142" />
                  <path d="M0.789062 4.13281L8.14594 8.00485L15.5028 4.13281" stroke="#F3EBE8" stroke-width="2" />
                </svg> <span>support&#64;4sw.app</span>
              </div>
            </a>
            <a [href]="blogUrl" target="_blank" rel="nofollow">
              <div class="footer_info">
                <svg viewBox="0 0 24 24" fill="none">
                  <path opacity="0.8"
                    d="M16.3441 24C20.5292 24 23.9275 20.5906 23.9498 16.4278L23.9944 10.3009L23.9219 9.96605L23.721 9.54755L23.3806 9.28528C22.9398 8.93932 20.7022 9.3076 20.0995 8.76075C19.6698 8.37015 19.6029 7.66705 19.4745 6.70728C19.2346 4.85468 19.0839 4.75982 18.7938 4.12927C17.7503 1.90839 14.8877 0.228784 12.9235 0H7.60567C3.4206 0 0 3.41502 0 7.58335V16.4278C0 20.5906 3.4206 24 7.60567 24H16.3441ZM7.70612 6.19391H11.9247C12.7282 6.19391 13.3811 6.84678 13.3811 7.64473C13.3811 8.43711 12.7282 9.09556 11.9247 9.09556H7.70612C6.90258 9.09556 6.24971 8.43711 6.24971 7.64473C6.24971 6.84678 6.897 6.19391 7.70612 6.19391ZM6.24971 16.3162C6.24971 15.5238 6.90258 14.871 7.70612 14.871H16.2771C17.0751 14.871 17.728 15.5183 17.728 16.3162C17.728 17.0974 17.0807 17.7614 16.2771 17.7614H7.70612C6.897 17.7614 6.24971 17.103 6.24971 16.3162Z"
                    fill="currentColor" />
                </svg> <span>{{ "Blog" | translate }}</span>
              </div>
            </a>
          </div>
        </div>
        @defer () {
        <div class="links" *ngIf="pagesList$ | async as pagesList">
          <ng-container *ngFor="let page of pagesList">
            <ng-container *ngFor="let child of page.childs">
              <a *ngIf="child.isClickable" [routerLink]="['/page/' + child.slug]" rel="nofollow">{{
                child.name
                }}</a>
              <a *ngIf="!child.isClickable">{{ child.name }}</a>
            </ng-container>
          </ng-container>
        </div>
        }
      </div>

      <div class="col-md-12" class="copyright">
        {{ '4Swapp Copyright' | translate}}
      </div>
    </div>
  </div>
</div>