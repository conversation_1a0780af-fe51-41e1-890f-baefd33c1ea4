import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { FooterComponent } from '@src/app/layout/footer/footer.component';
import { StripComponent } from '@src/app/layout/top-nav/components/strip/strip.component';
import { SideMenuService } from '@src/app/layout/top-nav/service/side-menu.service';
import { TopNavComponent } from '@src/app/layout/top-nav/top-nav.component';
import { DeviceDetectionService } from '@src/app/shared/services/device-detection.service';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-inner-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TopNavComponent,
    FooterComponent,
    StripComponent
  ],
  templateUrl: './inner-layout.component.html',
  styleUrl: './inner-layout.component.scss'
})
export class InnerLayoutComponent {

  hideSearchBar$: Observable<boolean>;

  constructor(
    private menuService: SideMenuService,
    public device: DeviceDetectionService
  ) {
    this.hideSearchBar$ = menuService.unSearchBar$;
  }




}
