
.seller_profile_image{
    position: relative;

    &::before , &::after {
        content: '';
        width: 58px;
        height: 58px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate3d(-50%,-50%,0);
        background: var(--main-membership-color);
        border-radius: 50%;
        z-index: 0;
    }

    &.membership{
        &::after{
            background: #ffffffe8;
            width: 56px;
            height: 56px;
        }
    }

    img {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
        flex-shrink: 0;
        position: relative;
    }
}

.seller_membership{
    transform: scale(0.6) translate3d(-20px, 0px, 0);
    pointer-events: none;
    z-index: 2;
    position: relative;
}