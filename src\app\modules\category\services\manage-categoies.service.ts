import { Injectable } from "@angular/core";
import { UntilDestroy, untilDestroyed } from "@ngneat/until-destroy";
import { Store } from "@ngrx/store";
import { LookupDTO } from "@src/app/shared/models/lookup.model";
import { LookupService } from "@src/app/shared/services/lookup.service";
import { getCategories } from "@src/app/store/app/selectors/app.selector";
import { MenuItem } from "primeng/api";
import { BehaviorSubject, map, tap } from "rxjs";
@UntilDestroy({ checkProperties: true })
@Injectable({
    providedIn: 'root'
})
export class ManageCategoriesService {

    private category: BehaviorSubject<LookupDTO> = new BehaviorSubject<LookupDTO>(null);
    category$ = this.category.asObservable();

    private categories: BehaviorSubject<MenuItem> = new BehaviorSubject<MenuItem>(null);
    categories$ = this.category.asObservable();




    constructor(
        private store: Store,
        private _lookupService: LookupService
    ) {

        this.store.select(getCategories).pipe(
            untilDestroyed(this),
            map(res => res.menu), map(res => this._lookupService.convertToMenuItems(res)), tap(res => {
                this.categories.next(res);
            })).subscribe();
    }

    setCategory(category: LookupDTO) {
        this.category.next(category);
    }

}