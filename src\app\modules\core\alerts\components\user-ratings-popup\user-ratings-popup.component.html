<div *ngIf="rates$ | async as rates" class="rating_box">

  <h2>{{ 'Ratings' | translate}}</h2>

  <div class="rating_list">
    <div *ngIf="rates.length > 0">
      <div *ngFor="let item of rates" class="rate_item">

        <div>
          <img [lazyload]="item?.userImage!" [placeholder]="'assets/img/placeholder/user-img-placeholder.png'" />
          <div>
            <b>{{ item.userName }}</b>
            <span>{{ item.rateDate | date:'MM/yyyy' }}</span>
          </div>

        </div>

        <p-rating [ngModel]="item.rate" [readonly]="true" [cancel]="false" />


      </div>
    </div>
    <app-no-result [type]="1" *ngIf="rates.length == 0"></app-no-result>

  </div>
</div>