@import "variables";
@import "mixins";

.container-fluid {
    min-height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;

    .row {
        width: 100%;

        .card {
            min-height: 10rem;
        }
    }
}



.confirmation_box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    height: 100%;

    h2 {
        margin: 20px auto;
        font-weight: 600;
        font-size: 1.8rem;
    }

    h3 {
        margin-bottom: 60px;
        font-size: 1.5rem;
    }

    app-success-icon {
        margin-inline: auto;
        display: inline-flex;
    }
}

.success_content {
    margin-top: -25vh;
}

.error_message {
    svg {
        width: 48%;
        max-width: 200px;
    }

    a {
        margin-top: 20vh;
    }
}

.success_page {
    height: 100vh;
    position: relative;
    width: 100vw;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: linear-gradient(180deg,
            rgba(250, 175, 64, 0) 0%,
            rgba(250, 175, 64, 0.17) 100%);
    padding: clamp(50px, 15vw, 117px) 20px 0;

}


.footer_img {
    position: absolute;
    bottom: 0px;
    width: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: max-content;
}

.continue_btn {
    padding: 14px;
    background-color: #2d3142 !important;
    border: 0 !important;
    box-shadow: none !important;
    font-size: 18px;
    font-weight: 600;
    color: #fff;
    @include borderRadius(43px);
    width: 100%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;

    // @media (max-width: 991.98px) {
    //   max-width: 100%;
    // }

    .p-button-label {
        font-size: 18px;
        font-weight: 600;
    }
}