@import "variables";
@import "mixins";

.bottom-sticky {
    background: #f7f7fb;
    padding: 20px 0px;
}


.cash_icon {
    border-radius: 30px;
    background: rgba(45, 49, 66, 0.06);
    font-size: 11px;
    padding: 6px 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    max-height: fit-content;

    svg {
        width: 11px;
        height: 9px;
        margin-inline-end: 5px;
    }
}


.item_price {
    font-size: clamp(20px, 6vw, 30px);
    color: $primary;
    padding: 10px 0px;
    display: flex;
    gap: 10px;
}



.item_actions {
    display: flex;
    gap: 20px;
    margin: 20px 0px;

    app-gray-btn {
        flex: 1.5;
    }

    app-secondary-btn {
        flex: 2;
    }
}

h1 {
    font-size: clamp(22px, 6vw, 24px);
    color: #3B4158;
    font-weight: 600;
    margin: 14px 0px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-direction: column;
    word-break: break-word;

    .item_title {
        order: 2;
    }

    .icon_text {
        order: 1;
    }

    @include Large {

        align-items: center;
        flex-direction: row;

        .item_title {
            order: 1;
        }

        .icon_text {
            order: 2;
        }

    }

}

.description {
    margin: 30px 0px;
    font-size: 0.9rem;
    padding: 0px;
    line-height: 2;
    word-break: break-word;

    h2 {
        font-size: 1.2rem;
    }


    &::ng-deep {
        p {
            margin: 0px;
        }
    }
}

.itemProps {
    list-style: none;
    margin: 0px;
    padding: 0px;

    li {
        display: flex;
        justify-content: space-between;
        font-size: 0.9rem;
        font-weight: 400;
        padding: 13px 0px;

        &:nth-child(even) {
            background: linear-gradient(270deg, rgba(45, 49, 66, 0.00) 0%, rgba(45, 49, 66, 0.03) 15.54%, rgba(45, 49, 66, 0.03) 48.44%, rgba(45, 49, 66, 0.02) 87.08%, rgba(45, 49, 66, 0.00) 100%);
        }

        span {
            &:nth-of-type(2) {
                font-weight: 400;
            }
        }
    }
}



.itemTags {
    margin-bottom: 30px;

    h3 {
        font-size: 1.2rem;
    }

    ul {
        list-style: none;
        margin: 0px;
        padding: 0px;
        padding-right: 0px !important;
        display: flex;
        flex-wrap: wrap;
        gap: 15px;

        li {
            background: $text-color;
            color: #fff;
            font-size: 0.85rem;
            padding: 7px 12px;
            border-radius: 20px;
        }
    }



}



.itemFeatures {
    margin-bottom: 80px;

    h2 {
        margin-bottom: 14px;
        font-size: 1.2rem;
    }


    ul {
        list-style: none;
        padding: 0px;
        margin: 0px;
        display: flex;
        flex-wrap: wrap;
        gap: 14px;

        li {
            padding: 10px 20px;
            border-radius: 77px;
            background: rgba(123, 69, 172, 0.05);
            font-size: 0.8rem;
        }
    }
}


@include Large {
    .item_actions {
        border-radius: 12px;
        background: #fff;
        padding: 17px 25px;
    }
}