import { Pipe, PipeTransform } from '@angular/core';
import { BannerModel } from '@src/app/shared/models/common.model';
import { DeviceDetectionService } from '@src/app/shared/services/device-detection.service';

@Pipe({
    name: 'homeBanners',
    standalone: true,
})
export class HomeBannersPipe implements PipeTransform {
    constructor(
        private dv: DeviceDetectionService
    ) { }
    transform(array: BannerModel[]): BannerModel[] {

        if (!array || array.length === 0) {
            return [];
        }

        if (this.dv.isMobile) {
            return array.filter(banner => banner.bannerType === 'MobileMainBanner');
        } else {
            return array.filter(banner => banner.bannerType === 'WebMainBanner');
        }

    }
}