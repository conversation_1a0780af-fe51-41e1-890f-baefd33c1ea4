<p-menu #op appendTo="#notButton" [popup]="true" [model]="items">
  <ng-template pTemplate="start">
    <div class="notifi_body">
      <div class="notifi_content">
        <app-notifications-header [(IsRead)]="isRead" (changeData)="loadNotificaitons()" />
        <div *ngFor="let notification of notifications; trackBy : trackby" class="noti_item"
          [ngClass]="{'not-seen' : !notification.seen}">
          <ng-container [ngSwitch]="notification.entityType">
            <ng-container *ngSwitchCase="NotificationEntityType.Listing">
              <ng-container
                *ngTemplateOutlet="notificationTemplate; context: {$implicit: notification, route: { url : '/advertisement/' + notification.entityID}}"></ng-container>
            </ng-container>

            <ng-container *ngSwitchCase="NotificationEntityType.Offer">
              <ng-container
                *ngTemplateOutlet="notificationTemplate; context: {$implicit: notification, route: { url : '/offers/messages' , queryParams: { id: notification.entityID }}}"></ng-container>
            </ng-container>

            <ng-container *ngSwitchCase="NotificationEntityType.Identity">
              <ng-container
                *ngTemplateOutlet="notificationTemplate; context: {$implicit: notification, route: { url : '/authentication/profile'}}"></ng-container>
            </ng-container>

            <ng-container *ngSwitchDefault>
              <ng-container
                *ngTemplateOutlet="notificationTemplate; context: {$implicit: notification, route: { url : '/advertisement/' + notification.entityID}}"></ng-container>
            </ng-container>
          </ng-container>


        </div>

        <div *ngIf="notifications.length == 0" class="no-message">{{'Empty' | translate}}</div>
      </div>

      <div class="show_all" *ngIf="notifications.length > 3">
        <a [routerLink]="'/notifications'" rel="nofollow">
          {{ 'Show all' | translate}}
        </a>
      </div>
    </div>
  </ng-template>
</p-menu>

<div class="notifications_holder" #notButton>

  <button (click)="op.toggle($event);closeMenu();" class="notification_btn">
    <span class="notification_icon">
      <img src="assets/img/notification.svg" alt="" />
    </span>
    <span class="notification_count" *ngIf="totalUnSeen > 0">{{ totalUnSeen <= 99 ? totalUnSeen : '99+' }}</span>
  </button>

</div>

<ng-template #notificationTemplate let-notification let-route="route" let-icon="icon">
  <a (click)="seen(notification.id , route)">
    <div>
      <div class="icon_img">
        <img class="noti_type" [lazyload]="notification.icon"
          [placeholder]="'assets/img/placeholder/notification_placeholder.svg'" />
      </div>
    </div>
    <div>
      <div class="title">{{ notification.name }}</div>
      <div class="icon_text">
        <svg data-id="clock_icon" viewBox="0 0 12 12" fill="none">
          <g opacity="0.4">
            <circle cx="5.75" cy="5.75" r="5.25" stroke="#2D3142" />
            <path d="M5.75 3.35352V5.91749L7.66667 6.70768" stroke="#2D3142" stroke-linecap="round" />
          </g>
        </svg>

        <span>{{ notification.date | fromNow}}</span>
      </div>
    </div>
  </a>
</ng-template>