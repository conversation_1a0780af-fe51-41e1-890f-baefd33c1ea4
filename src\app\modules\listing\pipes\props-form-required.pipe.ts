import { Pipe, PipeTransform } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { map, Observable } from 'rxjs';

@Pipe({
  name: 'propsFormRequired',
  standalone: true
})
export class PropsFormRequiredPipe implements PipeTransform {

  constructor() { }

  transform(form: FormGroup, fieldName: string, isMandatory: boolean = false): Observable<boolean> {

    const control = form.get(fieldName);

    if (!control) {
      return new Observable<boolean>(subscriber => subscriber.next(false));
    }

    return control.statusChanges.pipe(
      map((status) => {
        return status == 'INVALID' && control.hasError('required') && isMandatory;
      })
    );
  }

}
