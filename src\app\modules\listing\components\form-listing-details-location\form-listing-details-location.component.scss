@import "variables";
@import "mixins";
@import "../../style/common.scss";

.location_page{
    margin-bottom: 48px;

    h2{
        color: $text-color;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 24px;
    }

    ::ng-deep{
        .p-dropdown{
            border-radius: 36px;
            border: 1px solid #F4F4F5;
            width: 100%;
            padding: 4pxf;
            .p-dropdown-label {
                padding: 12px 20px 14px;
                text-transform: capitalize;
                color: $text-color;
                font-size: 16px;
                font-weight: 400;
            }

            .p-dropdown-trigger-icon{
                color: rgba($text-color, .4);

            }
        }
    }
}

.location_box{
    margin-bottom: 28px;
    max-width: 398px;

}


.radio_box{
    margin-bottom: 40px;
     ::ng-deep{
        
        .p-radiobutton .p-radiobutton-box .p-radiobutton-icon {
            background: url(/assets/img/check-icon.svg) $orangeColor no-repeat center center;
            background-color: $orangeColor;
        }
    }
    
    .radio_item {
        display: flex;
        gap: 12px;
        margin-bottom: 20px;
        label{
            font-size: 16px;
        }
    
    }
}


@include Large{
    .submit{
        margin-top: 50px;
    }
}