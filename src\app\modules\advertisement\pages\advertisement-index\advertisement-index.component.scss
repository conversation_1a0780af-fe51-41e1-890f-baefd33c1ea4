@import "variables";
@import "mixins";

.back_btn {
    color: var(--primary-sub-color);
    font-weight: bold;
    margin-bottom: 16px;
    display: flex;
    gap: 10px;

    .pi {
        @include rtl2(transform, rotate(180deg));
    }
}

.cash_icon {
    border-radius: 30px;
    background: rgba(45, 49, 66, 0.06);
    font-size: 11px;
    padding: 6px 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    max-height: fit-content;

    svg {
        width: 11px;
        height: 9px;
        margin-inline-end: 5px;
    }
}

.views_info {
    font-size: 0.85rem;
    background: rgba($color: $text-color, $alpha: 0.1);
    padding: 0px 15px;
    border-radius: 20px;
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}


.item_price {
    font-size: clamp(20px, 6vw, 30px);
    color: $primary;
    padding: 10px 0px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}

.dot {
    display: inline-flex;
    width: 4px;
    height: 4px;
    background: $text-color;
    opacity: 0.3;
    border-radius: 3px;
    margin: 0px 6px;
}

.item_actions {
    display: flex;
    gap: 10px;
    z-index: 10;
    background: #f7f7fb;
    width: 100%;
    padding: 10px 0px;

    @include rtl2(direction, ltr);

    app-gray-btn {
        flex: 1;
    }

    app-secondary-btn {
        flex: 1;
    }
}

.action-box {
    background: $secondary;

    app-gray-btn {
        flex-grow: 0;
    }

    &::ng-deep {
        button.mid_btn {
            padding: 14px !important;
        }
    }
}

.report_box {
    text-align: center;
}

.lising_status_icon {
    border-radius: 46px;
    background: #ffede4f8;
    padding: 10px 18px;
    color: $orangeColor;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    text-transform: capitalize;

    svg {
        width: 18px;
        height: 18px;
    }
}



.slider_holder {
    margin-top: 40px;
}

.item_name {
    color: #3B4158;
    font-weight: 600;
    margin: 20px 0px 0px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-direction: column;
    word-break: break-word;

    .item_title {
        order: 2;
    }

    .icon_text {
        order: 1;
        justify-content: space-between;
        width: 100%;
    }

    h1 {
        font-size: clamp(22px, 6vw, 24px);

    }

    @include Large {

        align-items: center;
        flex-direction: row;

        .item_title {
            order: 1;
        }

        .icon_text {
            order: 2;
            width: auto;

            .views_info {
                display: none;
            }
        }

    }
}

.description {
    margin: 30px 0px;
    font-size: 0.9rem;
    padding: 0px;
    line-height: 2;
    word-break: break-word;

    h2 {
        font-size: 1.2rem;
    }

    &::ng-deep {
        p {
            margin: 0px;
        }
    }
}

.swapp_details {
    margin-bottom: 10px;
    display: flex;
    gap: 10px;
    align-items: center;
}




.itemProps {
    list-style: none;
    margin: 0px;
    padding: 0px;

    &.more {
        li:nth-child(n+6) {
            display: none !important;
        }
    }

    li {
        display: flex;
        justify-content: space-between;
        font-size: 0.9rem;
        font-weight: 400;
        padding: 13px 0px;

        &:nth-child(even) {
            background: linear-gradient(270deg, rgba(45, 49, 66, 0.00) 0%, rgba(45, 49, 66, 0.03) 15.54%, rgba(45, 49, 66, 0.03) 48.44%, rgba(45, 49, 66, 0.02) 87.08%, rgba(45, 49, 66, 0.00) 100%);
        }

        span {
            &:nth-of-type(2) {
                font-weight: 400;
            }
        }
    }
}



.itemTags {
    margin-bottom: 30px;

    h3 {
        font-size: 1.2rem;
    }

    .tagsList {
        list-style: none;
        margin: 0px;
        padding: 0px;
        padding-right: 0px !important;
        display: flex;
        flex-wrap: wrap;
        gap: 15px;

        a {
            background: $text-color;
            color: #fff;
            font-size: 0.85rem;
            padding: 7px 12px;
            border-radius: 20px;
            cursor: pointer;
        }
    }



}




.itemFeatures {
    margin-bottom: 80px;

    h2 {
        margin-bottom: 14px;
        font-size: 1.2rem;
    }


    ul {
        list-style: none;
        padding: 0px;
        margin: 0px;
        display: flex;
        flex-wrap: wrap;
        gap: 14px;

        li {
            padding: 10px 20px;
            border-radius: 77px;
            background: rgba(123, 69, 172, 0.05);
            font-size: 0.8rem;


        }
    }
}




@include Large {

    .slider_holder {
        padding: 0px 0px;
    }

    .item_name {
        margin: 30px 0px;
    }

    .item_actions {
        border-radius: 12px;
        background: #fff;
        padding: 17px 25px;
    }

    .seller_info {
        border-radius: 8px;
        overflow: hidden;
    }

    .item_actions {
        border-radius: 12px;
        background: #fff;
        padding: 17px 25px;
    }

    .item_price {
        margin-bottom: 5px;
    }
}