import { Inject, Injectable } from '@angular/core';
import { OAuthStorage } from 'angular-oauth2-oidc';

@Injectable()
export class ServerTokenStoreService implements OAuthStorage {
  private cookies: Map<string, string>;
  constructor(@Inject('cookies') c: any,) {
    let cookies = JSON.parse(c);
    this.cookies = new Map<string, string>();

    for (let cookie of cookies) {
      this.cookies.set(cookie.key, cookie.value);
    }
  }

  getItem(key: string): string {
    if (this.cookies) {
      return this.cookies.get(key) as string;
    }

    return '';
  }

  removeItem(key: string): void { }

  setItem(key: string, data: string): void { }
}