@import "mixins";
@import "variables";

@import "swapp-options-style.scss";

.listing_info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    padding: 14px 24px;
    border-radius: 12px;
    min-height: 90px;
    font-size: 14px;

    b {
        font-size: 20px;
    }
}

.closable_list {
    max-height: 32vh;
    overflow: auto;
    margin-bottom: 10px;
}


.engage_links {
    display: none;
    gap: 18px;

    @include Large {
        display: flex;
    }
}

.engage_user_links {
    border-radius: 12px;
    padding: 14px;
    margin: 1rem 0rem;
    background: #fff;
    display: flex;
    flex-direction: column;
}

.engage_link {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 14px;
    border: 1px solid #92949e;
    border-radius: 30px;
    padding: 15px 20px;
    font-size: 0.85rem;
    cursor: pointer;


    &:nth-of-type(2) {
        flex: 1;
    }

    svg {
        width: 17px;
        height: 17px;
    }

    span {
        justify-content: center;
        display: inline-flex;
        flex: 1;
    }

}