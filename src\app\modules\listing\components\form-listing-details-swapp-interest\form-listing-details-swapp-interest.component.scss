@import "mixins";
@import "../../style/common.scss";

.swapp_interest_box{
    margin-top: 40px;
}
.form_box {
    background: #fff;
    padding: 22px;
}

.interest_titles {
    h2{
        font-size: 20px;
        font-weight: 800;
        margin-bottom: 15px;
    }
}


.inputField{
    border-radius: 46px;
    background: #fff;
    font-size: 16px;
    font-weight: 400;
    padding: 19px 25px;
    color: #2d3142;
    width: 100%;
    margin-bottom: 24px;
    display: flex;
    border: 1px solid #e1e1e1;
}

input{
    width: 100%;
    background: transparent;
    border: none;
}
input {
    
    &::placeholder{
        font-size: 16px;
        font-weight: 400;
        color: $text-color;
    }
}

@include Large{
    .form_box {
        border-radius: 10px;
    }
}