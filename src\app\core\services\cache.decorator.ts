import { Observable, of, tap } from "rxjs";

export interface CacheOptions {
    duration?: number
}

export function Cacheable(params: CacheOptions = {}) {
    const defaultValues: Partial<CacheOptions> = {
        duration: 60000,
    }

    params = {
        ...defaultValues,
        ...params
    };

    let originalFunc: Function;

    let result: Promise<any> | Observable<any> | any;
    let value: any;
    let funcType: string;
    let cacheUntil: Date;

    let inProgress = false;

    const cacheValue = (val, now) => {
        cacheUntil = new Date(now.getTime() + params.duration);
        value = val;
    };

    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        originalFunc = descriptor.value;
        descriptor.value = function (...args: any[]) {

            const now = new Date();
            if (value && cacheUntil && cacheUntil > now) {
                switch (funcType) {
                    case "observable": return of(value);
                    case "promise": return Promise.resolve(value);
                    default: return value;
                }
            }

            if (inProgress) {
                return result;
            }
            inProgress = true;


            result = originalFunc.apply(this, args);

            if (result instanceof Observable) {
                funcType = 'observable';
                return result.pipe(
                    tap(val => {
                        cacheValue(val, now);
                        inProgress = false;
                    }));
            } else if (result instanceof Promise) {
                funcType = 'promise';
                return result
                    .then(value => {
                        cacheValue(value, now);
                        inProgress = false;
                        return value;
                    });
            } else {
                funcType = 'value';
                cacheValue(result, now);
                inProgress = false;
                return result;
            }
        };
    };
}

