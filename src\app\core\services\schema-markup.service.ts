import { DOCUMENT, isPlatformServer } from '@angular/common';
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { ListingDetails } from '@src/app/shared/models/listing.model';
import { AppcenterService } from '@src/app/shared/services/appcenter.service';
import { CommonService } from '@src/app/shared/services/common.service';
import { environment } from '@src/environments/environment';
import { AppUtils } from '@src/utils/app-utils';
import { MenuItem } from 'primeng/api';

@Injectable({
    providedIn: 'root'
})
export class SchemaMarkupService {
    private currentLang: string;

    constructor(
        @Inject(PLATFORM_ID) private platformId: Object,
        @Inject(DOCUMENT) private document: Document,
        private translateService: AppcenterService,
        private commonService: CommonService

    ) {
        // Initialize the current language
        this.currentLang = this.translateService.lang || 'ar-eg';

    }

    /**
     * Add Schema.org markup to the document head
     * @param schema The schema object to add
     */
    private addSchema(schema: any): void {
        if (isPlatformServer(this.platformId)) {
            // Only execute in SSR context
            const script = this.document.createElement('script');
            script.type = 'application/ld+json';
            script.text = JSON.stringify(schema);
            this.document.head.appendChild(script);
        }
    }

    /**
     * Add language alternate links to the document head
     * @param path The current path without language prefix
     */
    addLanguageAlternates(path: string): void {
        if (isPlatformServer(this.platformId)) {
            const existingAlternates = this.document.querySelectorAll('link[rel="alternate"][hreflang]');
            existingAlternates.forEach(link => link.remove());

            // Add new alternates
            const enLink = this.document.createElement('link');
            enLink.rel = 'alternate';
            enLink.hreflang = 'ar-eg';
            enLink.href = `${environment.clintURL}/ar-eg/${path}`;

            const arLink = this.document.createElement('link');
            arLink.rel = 'alternate';
            arLink.hreflang = 'en-us';
            arLink.href = `${environment.clintURL}/en-us/${path}`;

            const canonicalLink = this.document.createElement('link');
            canonicalLink.rel = 'canonical';
            canonicalLink.href = `${environment.clintURL}/${this.currentLang}/${path}`;

            this.document.head.appendChild(enLink);
            this.document.head.appendChild(arLink);
            this.document.head.appendChild(canonicalLink);
        }
    }

    /**
     * Generate Home Page Schema Markup
     * @param homeData Additional data for the home page
     */
    generateHomeSchema(homeData?: any): void {
        const isArabic = this.currentLang === 'ar-eg';

        const completedScheme =
        {
            "@context": "https://schema.org",
            "@graph": [
                {
                    "@type": "WebSite",
                    "@id": `${environment.clintURL}/${this.currentLang}/home/<USER>
                    "url": `${environment.clintURL}/${this.currentLang}/home`,
                    "name": isArabic ? "فورسواب" : "4Swapp",
                    "description": "فورسواب عليه آلاف الاعلانات للبدل والبيع .. من عربيات عقارات موبايلات أثاث أو أي حاجة تخطر على بالك",
                    "potentialAction": {
                        "@type": "SearchAction",
                        "target": `${environment.clintURL}/${this.currentLang}/search/{search_term_string}`,
                        "query-input": "required name=search_term_string"
                    },
                    "inLanguage": this.currentLang == "ar-eg" ? "ar" : "en",
                    "hasPart": {
                        "@type": "SiteNavigationElement",
                        "name": isArabic
                            ? [
                                "الرئيسية",
                                "عربيات و مركبات",
                                "عقارات",
                                "هواتف وأجهزة لوحية",
                                "إلكترونيات وأجهزة",
                                "أثاث و ديكور",
                                "موضة وجمال",
                                "حيوانات أليفة",
                                "مستلزمات أطفال",
                                "الهوايات",
                                "الأسئلة الشائعة"
                            ]
                            : [
                                "Home",
                                "Vehicles & Cars",
                                "Real Estate & Properties",
                                "Mobiles & Tablets",
                                "Electronics & Appliances",
                                "Furniture & Decor",
                                "Fashion & Beauty",
                                "Pets",
                                "Kids & Babies",
                                "Hobbies",
                                "FAQ"
                            ],
                        "url": isArabic ? [
                            environment.clintURL + "/ar-eg/home",
                            environment.clintURL + "/ar-eg/category/1/%D8%B9%D8%B1%D8%A8%D9%8A%D8%A7%D8%AA-%D9%88-%D9%85%D8%B1%D9%83%D8%A8%D8%A7%D8%AA",
                            environment.clintURL + "/ar-eg/category/2/%D8%B9%D9%82%D8%A7%D8%B1%D8%A7%D8%AA",
                            environment.clintURL + "/ar-eg/category/3/%D9%87%D9%88%D8%A7%D8%AA%D9%81-%D9%88%D8%A3%D8%AC%D9%87%D8%B2%D8%A9-%D9%84%D9%88%D8%AD%D9%8A%D8%A9",
                            environment.clintURL + "/ar-eg/category/4/%D8%A5%D9%84%D9%83%D8%AA%D8%B1%D9%88%D9%86%D9%8A%D8%A7%D8%AA-%D9%88%D8%A3%D8%AC%D9%87%D8%B2%D8%A9",
                            environment.clintURL + "/ar-eg/category/5/%D8%A3%D8%AB%D8%A7%D8%AB-%D9%88-%D8%AF%D9%8A%D9%83%D9%88%D8%B1",
                            environment.clintURL + "/ar-eg/category/6/%D9%85%D9%88%D8%B6%D8%A9-%D9%88%D8%AC%D9%85%D8%A7%D9%84",
                            environment.clintURL + "/ar-eg/category/7/%D8%AD%D9%8A%D9%88%D8%A7%D9%86%D8%A7%D8%AA-%D8%A3%D9%84%D9%8A%D9%81%D8%A9",
                            environment.clintURL + "/ar-eg/category/8/%D9%85%D8%B3%D8%AA%D9%84%D8%B2%D9%85%D8%A7%D8%AA-%D8%A3%D8%B7%D9%81%D8%A7%D9%84",
                            environment.clintURL + "/ar-eg/category/9/%D8%A7%D9%84%D9%87%D9%88%D8%A7%D9%8A%D8%A7%D8%AA",
                            environment.clintURL + "/ar-eg/page/faqs",
                        ] : [
                            environment.clintURL + "/en-us/home",
                            environment.clintURL + "/en-us/category/1/vehicles-&-cars",
                            environment.clintURL + "/en-us/category/2/real-estate-&-properties",
                            environment.clintURL + "/en-us/category/3/mobiles-&-tablets",
                            environment.clintURL + "/en-us/category/4/electronics-&-appliances",
                            environment.clintURL + "/en-us/category/5/furniture-&-decor",
                            environment.clintURL + "/en-us/category/6/fashion-&-beauty",
                            environment.clintURL + "/en-us/category/7/pets",
                            environment.clintURL + "/en-us/category/8/kids-&-babies",
                            environment.clintURL + "/en-us/category/9/hobbies",
                            environment.clintURL + "/en-us/page/faqs",
                        ]
                    }
                },
                {
                    "@type": "WebPage",
                    "@id": `${environment.clintURL}/${this.currentLang}/home/<USER>
                    "url": `${environment.clintURL}/${this.currentLang}/home`,
                    "name": isArabic ? 'فورسواب | فورسواب الموقع الأول للبدل في مصر.. بدل .. بيع .. إشتري' : '4Swapp | فورسواب الموقع الأول للبدل في مصر.. بدل .. بيع .. إشتري',
                    "isPartOf": {
                        "@id": `${environment.clintURL}/${this.currentLang}/home/<USER>
                    },
                    "inLanguage": this.currentLang == "ar-eg" ? "ar" : "en",
                },
                {
                    "@type": ["Organization", "LocalBusiness"],
                    "@id": `${environment.clintURL}/${this.currentLang}/home/<USER>
                    "name": isArabic ? "فورسواب" : "4Swapp",
                    "image": `${environment.clintURL}/assets/images/4swapp-ogimage.jpg?v=1.1.2`,
                    "url": `${environment.clintURL}/${this.currentLang}/home`,
                    "logo": {
                        "@type": "ImageObject",
                        "url": `${environment.clintURL}/assets/img/4swapp-logo.svg?v=1.1.2`,
                        "width": "300",
                        "height": "57"
                    },
                    "address": {
                        "@type": "PostalAddress",
                        "streetAddress": "2 Soliman Abaza , Dokki, Giza Governorate",
                        "addressLocality": "Giza",
                        "addressRegion": "Giza",
                        "postalCode": "12655",
                        "addressCountry": "EG"
                    },
                    "geo": {
                        "@type": "GeoCoordinates",
                        "latitude": "30.0486952",
                        "longitude": "31.2033835"
                    },
                    "telephone": "+201098684444",
                    "priceRange": "$$",
                    "openingHoursSpecification": [
                        {
                            "@type": "OpeningHoursSpecification",
                            "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
                            "opens": "09:00",
                            "closes": "17:00"
                        },
                        {
                            "@type": "OpeningHoursSpecification",
                            "dayOfWeek": ["Saturday"],
                            "opens": "09:00",
                            "closes": "17:00"
                        }
                    ],
                    "sameAs": [
                        "https://www.youtube.com/@4Swapp",
                        "https://www.tiktok.com/@4swapp?lang=ar",
                        "https://www.linkedin.com/company/4swapp",
                        "https://twitter.com/4Swapp",
                        "https://www.instagram.com/4swapp/",
                        "https://www.facebook.com/profile.php?id=61553064838325"
                    ],
                    "contactPoint": [
                        {
                            "@type": "ContactPoint",
                            "telephone": "+201098684444",
                            "contactType": "customer service",
                            "areaServed": "EG",
                            "availableLanguage": ["English", "Arabic"]
                        },
                        {
                            "@type": "ContactPoint",
                            "telephone": "+201098684444",
                            "contactType": "technical support",
                            "areaServed": "EG",
                            "availableLanguage": ["English", "Arabic"]
                        }
                    ],
                },
                {
                    "@type": "ItemList",
                    "@id": `${environment.clintURL}/${this.currentLang}/home/<USER>
                    "name": "Main Categories",
                    "itemListElement": isArabic ?
                        [
                            {
                                "@type": "ListItem",
                                "position": 1,
                                "name": "عربيات و مركبات",
                                "url": environment.clintURL + "/ar-eg/category/1/%D8%B9%D8%B1%D8%A8%D9%8A%D8%A7%D8%AA-%D9%88-%D9%85%D8%B1%D9%83%D8%A8%D8%A7%D8%AA"
                            },
                            {
                                "@type": "ListItem",
                                "position": 2,
                                "name": "عقارات",
                                "url": environment.clintURL + "/ar-eg/category/2/%D8%B9%D9%82%D8%A7%D8%B1%D8%A7%D8%AA"
                            },
                            {
                                "@type": "ListItem",
                                "position": 3,
                                "name": "هواتف وأجهزة لوحية",
                                "url": environment.clintURL + "/ar-eg/category/3/%D9%87%D9%88%D8%A7%D8%AA%D9%81-%D9%88%D8%A3%D8%AC%D9%87%D8%B2%D8%A9-%D9%84%D9%88%D8%AD%D9%8A%D8%A9"
                            },
                            {
                                "@type": "ListItem",
                                "position": 4,
                                "name": "إلكترونيات وأجهزة",
                                "url": environment.clintURL + "/ar-eg/category/4/%D8%A5%D9%84%D9%83%D8%AA%D8%B1%D9%88%D9%86%D9%8A%D8%A7%D8%AA-%D9%88%D8%A3%D8%AC%D9%87%D8%B2%D8%A9"
                            },
                            {
                                "@type": "ListItem",
                                "position": 5,
                                "name": "أثاث و ديكور",
                                "url": environment.clintURL + "/ar-eg/category/5/%D8%A3%D8%AB%D8%A7%D8%AB-%D9%88-%D8%AF%D9%8A%D9%83%D9%88%D8%B1"
                            },
                            {
                                "@type": "ListItem",
                                "position": 6,
                                "name": "موضة وجمال",
                                "url": environment.clintURL + "/ar-eg/category/6/%D9%85%D9%88%D8%B6%D8%A9-%D9%88%D8%AC%D9%85%D8%A7%D9%84"
                            },
                            {
                                "@type": "ListItem",
                                "position": 7,
                                "name": "حيوانات أليفة",
                                "url": environment.clintURL + "/ar-eg/category/7/%D8%AD%D9%8A%D9%88%D8%A7%D9%86%D8%A7%D8%AA-%D8%A3%D9%84%D9%8A%D9%81%D8%A9"
                            },
                            {
                                "@type": "ListItem",
                                "position": 8,
                                "name": "مستلزمات أطفال",
                                "url": environment.clintURL + "/ar-eg/category/8/%D9%85%D8%B3%D8%AA%D9%84%D8%B2%D9%85%D8%A7%D8%AA-%D8%A3%D8%B7%D9%81%D8%A7%D9%84"
                            },
                            {
                                "@type": "ListItem",
                                "position": 9,
                                "name": "الهوايات",
                                "url": environment.clintURL + "/ar-eg/category/9/%D8%A7%D9%84%D9%87%D9%88%D8%A7%D9%8A%D8%A7%D8%AA"
                            }
                        ]
                        :
                        [
                            {
                                "@type": "ListItem",
                                "position": 1,
                                "name": "Vehicles & Cars",
                                "url": environment.clintURL + "/en-us/category/1/vehicles-&-cars"
                            },
                            {
                                "@type": "ListItem",
                                "position": 2,
                                "name": "Real Estate & Properties",
                                "url": environment.clintURL + "/en-us/category/2/real-estate-&-properties"
                            },
                            {
                                "@type": "ListItem",
                                "position": 3,
                                "name": "Mobiles & Tablets",
                                "url": environment.clintURL + "/en-us/category/3/mobiles-&-tablets"
                            },
                            {
                                "@type": "ListItem",
                                "position": 4,
                                "name": "Electronics & Appliances",
                                "url": environment.clintURL + "/en-us/category/4/electronics-&-appliances"
                            },
                            {
                                "@type": "ListItem",
                                "position": 5,
                                "name": "Furniture & Decor",
                                "url": environment.clintURL + "/en-us/category/5/furniture-&-decor"
                            },
                            {
                                "@type": "ListItem",
                                "position": 6,
                                "name": "Fashion & Beauty",
                                "url": environment.clintURL + "/en-us/category/6/fashion-&-beauty"
                            },
                            {
                                "@type": "ListItem",
                                "position": 7,
                                "name": "Pets",
                                "url": environment.clintURL + "/en-us/category/7/pets"
                            },
                            {
                                "@type": "ListItem",
                                "position": 8,
                                "name": "Kids & Babies",
                                "url": environment.clintURL + "/en-us/category/8/kids-&-babies"
                            },
                            {
                                "@type": "ListItem",
                                "position": 9,
                                "name": "Hobbies",
                                "url": environment.clintURL + "/en-us/category/9/hobbies"
                            },
                        ]

                }
            ]
        };


        this.addSchema(completedScheme);
        //this.addLanguageAlternates('');
    }

    /**
     * Generate Sub Category Page Schema Markup
     * @param name category name
     * @param description category description
     * @param url category URL
     * @param categoryId category ID
     * @param parentId parent category
     * @param subCategories List of subcategories
     * @param pagination Pagination information
     */
    generateCategorySchema(name: string, description: string, url: string, categoryId?: number, parentCategory?: MenuItem, subCategories?: MenuItem[], pagination?: any): void {
        const isArabic = this.currentLang === 'ar-eg';


        const schema =
        {
            "@context": "https://schema.org",
            "@graph": [
                {
                    "@type": "CollectionPage",
                    "@id": `${url}#webpage`,
                    "url": `${url}`,
                    "name": name,
                    "description": description,
                    "isPartOf": {
                        "@id": `${environment.clintURL}/${this.currentLang}/home/<USER>
                    },
                    "inLanguage": this.currentLang == "ar-eg" ? "ar" : "en",
                },
                {
                    "@type": "BreadcrumbList",
                    "@id": `${url}#breadcrumb`,
                    "itemListElement": [
                        {
                            "@type": "ListItem",
                            "position": 1,
                            "name": this.currentLang == "ar-eg" ? "الرئيسية" : "Home",
                            "item": `${environment.clintURL}/${this.currentLang}/home`
                        },
                        ...(parentCategory ? [{
                            "@type": "ListItem",
                            "position": 1,
                            "name": parentCategory?.label,
                            "item": `${environment.clintURL}/${this.currentLang}/category/${parentCategory?.id}/` + this.slug(parentCategory?.label)
                        }] : []),
                        {
                            "@type": "ListItem",
                            "position": 2,
                            "name": name
                        }
                    ]
                },
                ...(subCategories?.length > 0 ?
                    [{
                        "@type": "ItemList",
                        "@id": `${url}#subcategories`,
                        "name": isArabic ? "أقسام " + name + " الفرعية" : name + " Subcategories",
                        "itemListElement": subCategories?.map((item, index) => ({
                            "@type": "ListItem",
                            "position": index + 1,
                            "name": item.label,
                            "url": `${environment.clintURL}/${this.currentLang}/category/${categoryId}/${item.id}/` + this.slug(item.label)
                        }))

                    }] : []),
            ]
        };


        this.addSchema(schema);
    }

    /**
     * Generate Product Page Schema Markup
     * @param listingDetails The listing details object
     * @param images List of image URLs for the product
     * @param breadcrumbList List of breadcrumb items for navigation
     */
    generateProductSchema(listingDetails: ListingDetails, images: string[], breadcrumbList: any[]): void {
        const isArabic = this.currentLang === 'ar-eg';
        let title = "", description = "", url = "", image = "";
        image = this.listingImage(listingDetails.imageURL, 'main');
        if (listingDetails.metaTag) {
            title = (listingDetails.metaTag!.title!.length > 0 ? listingDetails.metaTag.title : listingDetails.name);
            description = listingDetails.metaTag!.description!.length > 0 ? listingDetails.metaTag.description : listingDetails.description;
            url = this.commonService.getSiteURL() + '/advertisement/' + listingDetails.listingID + '-' + this.listingSlug(listingDetails);
        } else {
            title = listingDetails.name;
            description = listingDetails.description;
            url = this.commonService.getSiteURL() + '/advertisement/' + listingDetails.listingID + '-' + this.listingSlug(listingDetails);
        }
        const schema =
        {
            "@context": "https://schema.org",
            "@graph": [
                {
                    "@type": "WebPage",
                    "@id": `${url}#webpage`,
                    "url": `${url}`,
                    "name": title,
                    "description": description,
                    "isPartOf": {
                        "@id": `${environment.clintURL}/${this.currentLang}/home#website`
                    },
                    "inLanguage": this.currentLang == "ar-eg" ? "ar" : "en",
                    "breadcrumb": {
                        "@id": `${url}#breadcrumb`
                    },
                    "primaryImageOfPage": {
                        "@id": `${url}#primaryimage`
                    },
                    "mainEntity": {
                        "@id": `${url}#product`
                    }
                },
                {
                    "@type": "BreadcrumbList",
                    "@id": `${url}#breadcrumb`,
                    "itemListElement": breadcrumbList
                },
                {
                    "@type": "ImageObject",
                    "@id": `${url}#primaryimage`,
                    "inLanguage": this.currentLang == "ar-eg" ? "ar" : "en",
                    "url": url,
                    "contentUrl": image,
                    "width": 800,
                    "height": 800,
                    "caption": title
                },
                {
                    "@type": "Product",
                    "@id": `${url}#product`,
                    "name": title,
                    "description": description,
                    "image": images.map((item, index) => {
                        return {
                            "@type": "ImageObject",
                            "url": this.listingImage(item, 'main'),
                            "width": 800,
                            "height": 800
                        }
                    }),


                    "offers": {
                        "@type": "Offer",
                        "url": url,
                        "priceCurrency": "EGP",
                        "price": listingDetails.price,
                        "itemCondition": listingDetails.conditionName == "old" ? "https://schema.org/UsedCondition" : "https://schema.org/NewCondition",
                        "availability": "https://schema.org/InStock",
                        "seller": {
                            "@type": "Person",
                            "name": listingDetails.userName
                        }
                    },

                }
            ]
        };


        this.addSchema(schema);
        //this.addLanguageAlternates(`products/${productPath}`);
    }

    /**
     * Generate Seller Page Schema Markup
     * @param seller The seller object
     * @param products List of products from this seller
     */
    generateSellerSchema(seller: any, products: any[]): void {
        const isArabic = this.currentLang === 'ar-eg';
        const alternateLang = isArabic ? 'en-us' : 'ar-eg';
        const sellerPath = seller.slug || seller.id;

        const schema = {
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": seller.name,
            "description": seller.description || (isArabic
                ? `Information about ${seller.name}`
                : `معلومات عن ${seller.name}`),
            "url": `${environment.clintURL}/${this.currentLang}/sellers/${sellerPath}`,
            "inLanguage": this.currentLang,
            "makesOffer": products.slice(0, 10).map(product => ({
                "@type": "Offer",
                "itemOffered": {
                    "@type": "Product",
                    "name": product.name,
                    "url": `${environment.clintURL}/${this.currentLang}/products/${product.slug || product.id}`
                }
            })),
            "mainEntityOfPage": {
                "@type": "WebPage",
                "alternateName": seller.translatedName || seller.name,
                "@id": `${environment.clintURL}/${alternateLang}/sellers/${sellerPath}`
            }
        };

        this.addSchema(schema);
        //this.addLanguageAlternates(`sellers/${sellerPath}`);
    }

    /**
     * Generate FAQ Page Schema Markup
     * @param faqItems List of FAQ items (question/answer pairs)
     */
    generateFAQSchema(faqItems: { question: string, answer: string }[]): void {
        const isArabic = this.currentLang === 'ar-eg';
        const alternateLang = isArabic ? 'en-us' : 'ar-eg';

        const schema =
        {
            "@context": "https://schema.org",
            "@graph": [
                {
                    "@type": "WebPage",
                    "@id": `${environment.clintURL}/${this.currentLang}/page/faqs#webpage`,
                    "url": `${environment.clintURL}/${this.currentLang}/page/faqs`,
                    "name": isArabic ? "الأسئلة الشائعة" : "FAQs",
                    "description": isArabic ? "الأسئلة الشائعة حول فورسواب" : "Frequently asked questions about 4Swapp",
                    "isPartOf": {
                        "@id": `${environment.clintURL}/${this.currentLang}#website`
                    },
                    "inLanguage": this.currentLang == "ar-eg" ? "ar" : "en",
                    "mainEntity": {
                        "@id": `${environment.clintURL}/${this.currentLang}/page/faqs#faqpage`
                    }
                },
                {
                    "@type": "FAQPage",
                    "@id": `${environment.clintURL}/${this.currentLang}/page/faqs#faqpage`,
                    "mainEntity":
                        faqItems.map((item, index) => ({
                            "@type": "Question",
                            "name": item.question,
                            "acceptedAnswer": {
                                "@type": "Answer",
                                "text": item.answer
                            }
                        })),

                },
                {
                    "@type": "BreadcrumbList",
                    "@id": `${environment.clintURL}/${this.currentLang}/page/faqs#breadcrumb`,
                    "itemListElement": [
                        {
                            "@type": "ListItem",
                            "position": 1,
                            "name": "الرئيسية",
                            "item": `${environment.clintURL}/${this.currentLang}/home`
                        },
                        {
                            "@type": "ListItem",
                            "position": 2,
                            "name": "الأسئلة المتكررة"
                        }
                    ]
                }
            ]
        }
            ;

        this.addSchema(schema);
        //this.addLanguageAlternates("faq");
    }

    private slug(value: string) {
        return AppUtils.titleCleaner(value).toString().toLowerCase().replace(/ /g, '-')
    }

    /**
     * Generate a slug for the listing
     * @param value The listing details object
     * @returns The generated slug
     */
    private listingSlug(value: ListingDetails) {
        const name = value['slugName'] ?? value['name'];
        return name.trim().toLowerCase().replace(/ /g, '_');
    }

    /**
     * Generate a listing image path
     * @param value The image URL or object
     * @param type The type of image (e.g., 'main', 'thumbnail')
     * @param ext The image extension (default: 'webp')
     * @returns The generated image path
     */
    private listingImage(value: any, type: string, ext: string = 'webp'): string {
        if (!value) return '';
        if (!value['imageURL'] && !value['images']) {
            return value.indexOf('https') >= 0 ? (value + `/${type}.${ext}`) : value;
        } else {
            return (value ?? '') + '' + `/${type}.${ext}`;

        }
    }

    /**
     * Clear all schema markup
     * Used when navigating between pages in SPA mode
     */
    clearSchemaMarkup(): void {
        if (isPlatformServer(this.platformId)) {
            const scripts = this.document.querySelectorAll('script[type="application/ld+json"]');
            scripts.forEach(script => script.remove());
        }
    }
}