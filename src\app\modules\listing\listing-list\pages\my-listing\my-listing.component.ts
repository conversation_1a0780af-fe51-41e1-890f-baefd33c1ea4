import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/common';
import { afterNextRender, Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { MetaService } from '@src/app/modules/core/service/meta.service';
import { PrimeTemplate } from 'primeng/api';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { PaginatorModule } from 'primeng/paginator';
import { RadioButtonModule } from 'primeng/radiobutton';
import { debounceTime, map, Observable, Subject } from 'rxjs';
import { AlertHandlerService } from 'src/app/modules/core/alerts/alert-handler.service';
import { SortBy } from 'src/app/modules/core/enums';
import { BrowserService } from 'src/app/modules/core/service/browser.service';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { Pagination } from 'src/app/shared/models/base.response.model';
import { ListingStatusType } from 'src/app/shared/models/listing-status-type';
import { ListingFilters, ListingStatus, ListingView } from 'src/app/shared/models/listing.model';
import { LookupDTO } from "src/app/shared/models/lookup.model";
import { DeviceDetectionService } from 'src/app/shared/services/device-detection.service';
import { ListingService } from 'src/app/shared/services/listing.service';
import { LookupService } from 'src/app/shared/services/lookup.service';
import { FormUtils } from 'src/utils/form-utils';
import { DarkBtnComponent } from '../../../../../shared/components/dark-btn/dark-btn.component';
import { ItemCardRowComponent } from '../../../../../shared/components/item-card-row/item-card-row.component';
import { NoResultComponent } from '../../../../../shared/components/no-result/no-result.component';
import { SideFilterComponent } from '../../../../../shared/components/side-filter/side-filter.component';
import { NtranslatePipe } from '../../../../../shared/pipes/ntranslate.pipe';



@Component({
  selector: 'app-my-listing',
  templateUrl: './my-listing.component.html',
  styleUrls: ['./my-listing.component.scss'],
  standalone: true,
  imports: [NgIf, DialogModule, SideFilterComponent, DropdownModule, FormsModule, PrimeTemplate, NgClass, RouterLink, NgFor, ItemCardRowComponent, NoResultComponent, PaginatorModule, ReactiveFormsModule, RadioButtonModule, DarkBtnComponent, AsyncPipe, NtranslatePipe]
})
export class MyListingComponent implements OnInit {

  types: ListingStatusType[];
  selectedType?: ListingStatusType;

  listingStatus = ListingStatus;
  pagination: Pagination = {
    currentPage: 1,
    pageSize: 12,
    totalItems: 0,
    totalPages: 0,
  };

  userParams: any = {
    sortBy: 'createdDate',
    isSortAscending: false,
    name: '',
  };

  blockedStatuses = [this.listingStatus.expired, this.listingStatus.sold, this.listingStatus.DealInProgress, this.listingStatus.blocked];

  currentStatus = 0;

  listings$: Observable<ListingView[]>;

  sortBy: SortBy[] = [
    { value: 'createdDate', viewValue: 'Newest', isSortAscending: false },
    { value: 'price', viewValue: 'HighPrice', isSortAscending: false },
    { value: 'price', viewValue: 'LowPrice', isSortAscending: true },
  ];
  selectedSortBy?: SortBy;

  filterVisible: boolean = false;

  filter: ListingFilters = {
  };

  searchString: string = '';

  filterResults$ = new Subject();

  reasons$: Observable<LookupDTO[]>;

  reasonVisible: boolean = false;

  form: FormGroup;

  activeListingId: number = 0;



  constructor(
    private listingService: ListingService,
    private activatedRoute: ActivatedRoute,
    private translateService: TranslationService,
    private router: Router,
    public deviceDetection: DeviceDetectionService,
    private fb: FormBuilder,
    private lookupService: LookupService,
    private alertService: AlertHandlerService,
    private browser: BrowserService,
    private metaService: MetaService,


  ) {

    this.metaService.set({
      title: this.translateService.instant('Listing'),
    });
    afterNextRender(() => {
      this.filterResults$.pipe(
        debounceTime(500),
      )
        .subscribe(() => {
          this.getListing();
        });


      this.types = [
        { id: this.listingStatus.approved, name: this.translateService.instant('Active'), url: '/listing/list/active' },
        { id: this.listingStatus.created, name: this.translateService.instant('Waitingforreview'), url: '/listing/list/waiting' },
        { id: this.listingStatus.rejected, name: this.translateService.instant('Rejected'), url: '/listing/list/rejected' },
        { id: this.listingStatus.sold, name: this.translateService.instant('Sold'), url: '/listing/list/sold' },
      ];

      this.activatedRoute.data.subscribe(res => {
        if (res['type']) {
          this.currentStatus = +res['type'];
          this.selectedType = this.types.find(item => item.id == this.currentStatus);
        }

        this.filterResults$.next(true);


      });




      this.activatedRoute.queryParams.subscribe(params => {


        if (params['pageNumber']) {

          for (let obj in params) {

            if (obj == 'selectedProperties') {
              this.filter[obj] = JSON.parse(atob(params[obj]));
            } else {
              this.filter[obj] = obj == 'conditions' ? +params[obj] > 0 ? [+params[obj]] : [] : FormUtils.parseValue(params[obj]);
            }


          }

          this.pagination.currentPage = +params['pageNumber'];

          setTimeout(() => {
            this.browser.scrollTo({
              behavior: 'smooth',
              top: 0,
            });
          }, 0);

        } else {
          this.filter = {
            ...this.filter,
            sortBy: this.userParams.sortBy,
            isSortAscending: this.userParams.isSortAscending
          };
        }

        this.selectedSortBy = this.sortBy.find(item => item.value == this.filter.sortBy && item.isSortAscending == this.filter.isSortAscending);

        this.filterResults$.next(true);

      });

      this.form = this.fb.group({
        RemoveReason: new FormControl('', [Validators.required]),
      });
    });
  }

  ngOnInit(): void {




  }

  onSortingChanged() {

    this.filter = {
      ...this.filter,
      sortBy: this.selectedSortBy?.value ?? this.userParams.sortBy,
      isSortAscending: this.selectedSortBy?.isSortAscending ?? this.userParams.isSortAscending
    };

    if (this.pagination.currentPage! > 1) {
      this.filter.pageNumber = 1;
      this.changeRoute([]);
    } else {
      this.changeRoute([]);
    }


  }

  onSubmitFilter(e) {
    this.filterVisible = false;
    if (e.clear) {
      this.pagination.currentPage = 1;
    }

    this.filter = {
      ...this.filter,
      pageNumber: this.pagination.currentPage,
      pageSize: this.pagination.pageSize,
      categories: e.categories,
      priceFrom: e.priceFrom,
      priceTo: e.priceTo,
      search: this.searchString,
      location: 0,
      area: 0,
      sortBy: this.selectedSortBy?.value ?? this.userParams.sortBy,
      isSortAscending: this.selectedSortBy?.isSortAscending ?? this.userParams.isSortAscending,
    };




    if (e.conditions) {
      this.filter.conditions = e.conditions;
    }
    if (e.payments) {
      this.filter.payments = e.payments;
    }

    if (e.location && e.location > 0) this.filter.location = e.location;
    if (e.area && e.area > 0) this.filter.area = e.area;

    if (this.pagination.currentPage! > 1) {
      this.filter.pageNumber = 1;

    }
    this.changeRoute([]);


  }

  changeRoute(url) {
    setTimeout(() => {
      this.browser.scrollTo({
        behavior: 'smooth',
        top: 0,
      });
    }, 0);
    this.router.navigate(url, { queryParams: this.filter });
  }

  onChangeType(e) {
    if (e.value) {
      this.router.navigate([e.value.url]);
    }
  }

  getModeType(item: ListingView) {

    if (this.blockedStatuses.indexOf(item.status!) >= 0) {
      return 'blocked';
    } else {
      return 'edit';
    }
  }


  getListing() {

    this.filter = {
      ...this.filter,
      pageNumber: this.pagination.currentPage,
      pageSize: this.pagination.pageSize,
      search: this.searchString,
      sortBy: this.filter.sortBy ?? this.userParams.sortBy,
      isSortAscending: this.filter.isSortAscending ?? this.userParams.isSortAscending,
      status: this.currentStatus
    };


    this.listings$ = this.listingService.getUserListing(this.filter).pipe(map(res => {
      return res.body?.data;
    }), map(res => {
      this.pagination.totalItems = res?.totalCount;
      this.pagination.totalPages = res?.totalPages;

      return res!.items;
    }));

  }

  onkeyup(e) {

    if (e.keyCode == 13) {
      this.gotoSearch();
    }
  }
  gotoSearch() {
    this.pagination.currentPage = 1;
    this.filter = {
      ...this.filter,
      search: this.searchString,
      pageNumber: this.pagination.currentPage,

    };

    this.changeRoute([]);

  }

  pageChanged(event: any): void {
    this.filter.pageNumber = (parseInt(event.page) + 1);
    this.changeRoute([]);
  }

  onEdit(id) {
    this.listingService.editAd(id, this.currentStatus == this.listingStatus.approved);

  }
  onDelete(id) {
    this.activeListingId = id;

    const alertOptions: any = {
      message: this.translateService.instant("Are you sure you want to remove this listing?"),
      buttons: [
        { title: this.translateService.instant("Yes, Remove"), value: true },
        { title: this.translateService.instant("No, Keep"), value: false },
      ]
    };

    if (this.currentStatus == this.listingStatus.approved) {
      alertOptions.description = this.translateService.instant("my_listing_delete_listing_has_offer");
    }


    this.alertService.warn(alertOptions, (e) => {
      if (e) {
        this.reasons$ = this.lookupService.getremovereasons().pipe(map(res => res.data));
        this.reasonVisible = true;

      }
    });

  }

  delete() {

    this.listingService.deleteAd(this.activeListingId, () => {
      this.getListing();
    }, this.form.value.RemoveReason);

    this.reasonVisible = false;


  }

  openDeleteReasons() {
  }

}
