.rating_box{
    h2{
        font-size: 18px;
        margin: 25px 0px 20px;
    }
}

.rating_list{
    max-height: 60vh;
    overflow: auto;
}

.rate_item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #fff;
    border-radius: 40px;
    margin-bottom: 10px;

    &:last-of-type{
        margin-bottom: 0px;
    }

    img {
        width: 40px;
        height: 40px;
        object-fit: cover;
        border-radius: 50%;
        box-shadow: 0px 5px 12px 0px rgba(45, 49, 66, 0.08);
    }
    
    &>div {
        display: flex;
        align-items: center;
        gap: 15px;
        font-size: 14px;
    }
    
    b {
        display: block;
    }
}


star-rating{
    pointer-events: none;
}