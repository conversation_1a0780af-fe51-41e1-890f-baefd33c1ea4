<ng-template #filterBtn>
  <button type="button" class="btn btn-outline-primary filters_btn" [attr.aria-expanded]="!isCollapsed2"
    aria-controls="collapseExample" (click)="filterVisible = true">
    <svg width="15" height="14" viewBox="0 0 15 14" fill="none" data-id="filter">
      <path
        d="M8.68282 13C8.55492 13 8.43048 12.9579 8.32816 12.8799L5.96377 12.187C5.89036 12.131 5.83077 12.0585 5.78973 11.9751C5.7487 11.8917 5.72733 11.7998 5.72733 11.7065V9.63582L1.58019 4.89573C1.28565 4.55815 1.09335 4.14133 1.02637 3.69538C0.959403 3.24943 1.02062 2.79331 1.20267 2.38186C1.38471 1.9704 1.67984 1.62112 2.05258 1.37598C2.42532 1.13084 2.85981 1.00028 3.30383 1H11.6974C12.1414 1.00053 12.5758 1.1313 12.9484 1.3766C13.321 1.62191 13.6159 1.9713 13.7978 2.3828C13.9796 2.7943 14.0406 3.2504 13.9735 3.69629C13.9064 4.14218 13.7139 4.55888 13.4193 4.89633L9.27392 9.63582V12.3995C9.27392 12.5587 9.21164 12.7115 9.10079 12.8241C8.98994 12.9367 8.83959 13 8.68282 13Z"
        fill="#2D3142" stroke="white" stroke-width="2" stroke-linecap="round" />
    </svg>

    {{ 'Filters' | translate}}
  </button>
</ng-template>

<ng-template #sortDropDown>
  <p-dropdown [className]="'sortDropDown'" [options]="sortBy" [(ngModel)]="selectedSortBy" optionLabel="viewValue"
    optionLabel="viewValue" [placeholder]="'Sortby' | translate" (onChange)="onSortingChanged()" [appendTo]="'body'">
    <ng-template let-item pTemplate="selectedItem">
      <div class="d-flex align-items-center gap-2">
        <svg width="16" height="17" viewBox="0 0 16 17" fill="none">
          <path d="M10.7143 4.00167V14.3945L15 10.1088" stroke="#2D3142" stroke-width="2" stroke-linecap="round" />
          <path d="M5.28571 13.4651V3.07227L1 7.35798" stroke="#2D3142" stroke-width="2" stroke-linecap="round" />
        </svg>

        {{ item.viewValue }}
      </div>
    </ng-template>

    <ng-template let-item pTemplate="item">
      <div class="d-flex align-items-center gap-2">
        <div>{{ item.viewValue }}</div>
      </div>
    </ng-template>
  </p-dropdown>
</ng-template>

<ng-container *ngIf="category">
    <app-new-category-header [category]="category"></app-new-category-header>
    <!-- <app-category-header [category]="category" [subCategoryId]="subCategoryId"></app-category-header> -->

  <p-dialog *ngIf="deviceDetection.isMobile || deviceDetection.isTablet" header="" [className]="'filterPopup'"
    [modal]="true" [(visible)]="filterVisible" [style]="{width: '50vw'}">
    <app-side-filter [filter]="filter" [subCategoryId]="subCategoryId"
      (onSubmit)="onSubmitFilter($event)"></app-side-filter>
  </p-dialog>

  <!-- Filter Section  -->


  <div class="filter_section">
    <div class="container">
      <div class="row">
        <div class="col-lg-5 col-xl-4 listing_side_filter">
          <app-side-filter [filter]="filter" [subCategoryId]="subCategoryId"
            *ngIf="!deviceDetection.isMobile && !deviceDetection.isTablet"
            (onSubmit)="onSubmitFilter($event)"></app-side-filter>
        </div>
        <div class="col-lg-7 col-xl-8 d-flex flex-column listing_result">
          <div class="new_listing" #listingList>
            <div class="new_listing_head">
              <h1 class="d-lg-block">{{ categoryPageTitle | translate }}</h1>
              <div class="d-block d-lg-none">
                <app-layout-toggle-btn (onUpdate)="changeGridMode($event)"></app-layout-toggle-btn>
              </div>

              <div class="head_right">
                @defer () {
                <div class="layout_btns">
                  <div class="d-none d-lg-block">
                    <app-layout-toggle-btn (onUpdate)="changeGridMode($event)"></app-layout-toggle-btn>
                  </div>
                  <ng-container *ngTemplateOutlet="sortDropDown"></ng-container>
                  <div class="d-block d-lg-none">
                    <ng-container *ngTemplateOutlet="filterBtn"></ng-container>
                  </div>
                </div>
                }
              </div>
            </div>

            <div *ngIf="listings$ | async as listings; else loader">
              <!-- Grid Layout -->
              <div class="grid_layout" *ngIf="isGrid">
                <app-item-card *ngFor="let item of listings;trackBy : trackby" [itemData]="item"></app-item-card>
              </div>
              <!-- Row Layout -->
              <div class="row_layout" *ngIf="!isGrid">
                <app-item-card-row *ngFor="let item of listings;trackBy : trackby" [itemData]="item">
                </app-item-card-row>
              </div>

              <app-no-result [type]="1" *ngIf="listings && listings.length == 0"
                [subTitle]="'Try_another_keyword'"></app-no-result>


              <!-- <app-bottom-banner></app-bottom-banner> -->

              <p-paginator *ngIf="listings && pagination.totalPages! > 1" (onPageChange)="pageChanged($event)"
                [first]="(this.filter.pageNumber! -1) * pagination.pageSize!" [rows]="pagination.pageSize"
                [totalRecords]="pagination.totalItems" [pageLinkSize]="3" [showFirstLastIcon]="false"></p-paginator>

            </div>
          </div>

        </div>
        <app-category-footer [category]="category" [subCategoryId]="subCategoryId"></app-category-footer>

      </div>

    </div>
  </div>



</ng-container>


<ng-template #loader>
  <app-item-card-row-skeleton [grid]="isGrid"></app-item-card-row-skeleton>
</ng-template>