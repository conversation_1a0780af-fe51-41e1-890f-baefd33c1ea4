@import 'variables';
@import 'mixins';

.main-title {
    margin-bottom: 0px !important;
}

.desktop-menu {
    height: 100%;
}

.menu-list {
    display: flex;
    background: #F7F7FB;
    margin-left: -20px;
    margin-right: -20px;
    padding: 15px 0px;

}

.main-menu {
    width: 100%;
    margin: 0px;
}

.menu-list.active {

    .main-menu {
        display: none;
    }


}

.sub-menu {
    width: 100%;
}

.sub-title {
    img {
        display: none;
    }
}


ul {
    min-width: 300px;
    list-style: none;
    padding: 0px !important;

    li {
        a {
            padding: 12px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .link_title {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                gap: 20px;


            }

            .pi {
                color: rgba($color: $text-color, $alpha: 0.4);

                @include rtl2(transform, rotate(180deg));
            }
        }

        .icon {
            width: 24px;
            height: 22px;
            object-fit: contain;
        }

        .label {
            font-size: 15px;
            font-weight: 400;
            color: #2d3142;
        }

        .arrow {
            font-size: 20px;
        }

        &.title {
            .label {
                font-size: 1.1rem;
                font-weight: 600;
            }

            a {
                justify-content: flex-start;
                gap: 15px;

                .pi {
                    transform: rotate(180deg);

                    @include rtl2(transform, rotate(0deg));
                }
            }

        }

        &:not(.title) {

            &:hover,
            &.active {
                background: linear-gradient(90deg, rgba(250, 175, 64, 0) 0%, rgb(242 101 34 / 11%) 100%);
                position: relative;

                &:after {
                    content: '';
                    position: absolute;
                    width: 4px;
                    height: 50%;
                    background: var(--primary-color);
                    right: 0px;
                    top: 50%;
                    transform: translateY(-50%);
                    border-top-left-radius: 3px;
                    border-bottom-left-radius: 3px;
                }
            }
        }

        &.all_link {
            .label {
                color: var(--primary-color);
                font-weight: 600;

            }

            &:hover {
                background: transparent;

                &::after {
                    display: none;
                }
            }
        }
    }

    &:last-of-type {
        border: none;
    }
}