<div class="container">
  <div class="mobile-app-banner" *ngIf="showBanner && !ds.isDesktop">
    <div class="banner-content">
      <img src="assets/icons/logo.png" alt="App Icon" class="app-icon" />
      <div class="text-info">
        <div class="app-title">
          {{ "4Swapp | Swap .. Sell .. Buy" | translate }}
        </div>
        <div class="app-subtitle">
          {{ "4Swapp The Ultimate Swapping Platform!" | translate }}
        </div>
      </div>
      <button class="open-app-btn" (click)="openApp()">
        {{ "OPEN APP" | translate }}
      </button>
      <button class="close-btn" (click)="closeBanner()">×</button>
    </div>
  </div>
</div>
