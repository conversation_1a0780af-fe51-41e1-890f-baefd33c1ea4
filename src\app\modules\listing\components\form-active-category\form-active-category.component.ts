import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MenuItem } from 'primeng/api';

@Component({
  selector: 'app-form-active-category',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './form-active-category.component.html',
  styleUrls: ['./form-active-category.component.scss']
})
export class FormActiveCategoryComponent implements OnInit {
  @Input() mainCategory:MenuItem;
  @Input() subCategory:MenuItem;
  @Input() editMode:boolean;
  @Output() back = new EventEmitter();



  constructor() { }

  ngOnInit(): void {
  }

  backToCategory(){
    this.back.emit(true);
  }

}
