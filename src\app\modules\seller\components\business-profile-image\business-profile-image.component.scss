@import "mixins";
@import "variables";

.profile-img {
    width: 300px;
    height: 100px;
    position: relative;

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 12px;
        position: relative;
        z-index: 3;
    }

    &::before,
    &::after {
        content: '';
        width: 305px;
        height: 105px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate3d(-50%, -50%, 0);
        background: var(--main-membership-color);
        border-radius: 14px;
        z-index: 0;
    }

    &.membership {
        &::after {
            background: #ffffffe8;
            width: 302px;
            height: 102px;
        }
    }


}