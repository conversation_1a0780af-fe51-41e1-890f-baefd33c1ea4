import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { RatingModule } from 'primeng/rating';
import { PrimaryBtnComponent } from 'src/app/shared/components/primary-btn/primary-btn.component';
import { LazyloadDirective } from 'src/app/shared/directives/lazyload.directive';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';

@Component({
  selector: 'app-rating-offer',
  standalone: true,
  imports: [CommonModule, PrimaryBtnComponent, LazyloadDirective, NtranslatePipe, ReactiveFormsModule, RatingModule],
  templateUrl: './rating-offer.component.html',
  styleUrls: ['./rating-offer.component.scss']
})
export class RatingOfferComponent implements OnInit {

  message: string;
  image: string;
  btns = [];

  form = new FormGroup({
    ratingInput: new FormControl(5),
  });

  rating = 5;


  constructor(private ref: DynamicDialogRef, public config: DynamicDialogConfig, private fb: FormBuilder) { }

  ngOnInit(): void {
    this.message = this.config.data.message;
    this.image = this.config.data.image;
    if (this.config.data.buttons) {
      this.btns = this.config.data.buttons;
    }
  }

  close() {
    if (this.ref) {
      this.ref.close(this.form.value.ratingInput);
    }
  }

}
