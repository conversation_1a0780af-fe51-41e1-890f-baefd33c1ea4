<ng-container [ngSwitch]="listing.status">
  <div *ngSwitchCase="1" class="d-flex flex-column gap-3">
    <div class="listing_info">
      {{ "Under review" | translate }}
    </div>

    <ng-container [ngTemplateOutlet]="editMode"></ng-container>

  </div>
  <ng-template *ngSwitchCase="2" [ngTemplateOutlet]="editMode"></ng-template>
  <ng-template *ngSwitchCase="5" [ngTemplateOutlet]="rejectMode"></ng-template>
  <div *ngSwitchDefault class="listing_info" [ngClass]="{
    'mb-3': device.isDesktop,
  }">
    <div class="views_info" *ngIf="device.isDesktop">
      <b>{{ listing.views }}</b> {{ "Views" | translate }}
    </div>

    {{ listing.statusName! | translate }}
  </div>
</ng-container>

<ng-template #editMode>
  <div class="listing_info" [ngClass]="{
    'mb-3': device.isDesktop,
  }" *ngIf="device.isDesktop">
    <div class="views_info">
      <b>{{ listing.views }}</b> {{ "Views" | translate }}
    </div>
    <div class="listing_btns">
      <button (click)="openDeleteReasons()">
        <svg data-id="delete_icon" viewBox="0 0 14 14" fill="none">
          <rect x="11.9543" y="1.00391" width="1.47519" height="15.4895" rx="0.737594"
            transform="rotate(45 11.9543 1.00391)" fill="#2D3142" stroke="#2D3142" stroke-width="2" />
          <rect x="12.9958" y="11.9531" width="1.47519" height="15.4895" rx="0.737594"
            transform="rotate(135 12.9958 11.9531)" fill="#2D3142" stroke="#2D3142" stroke-width="2" />
        </svg>
        {{ 'Delete' | translate}}
      </button>
      <button (click)="edit()">
        <svg data-id="edit_icon" viewBox="0 0 18 18" fill="none">
          <path
            d="M16.8653 1.13829C16.5044 0.777415 16.076 0.491146 15.6046 0.295836C15.1331 0.100526 14.6277 0 14.1174 0C13.607 0 13.1017 0.100526 12.6302 0.295836C12.1587 0.491146 11.7303 0.777415 11.3695 1.13829L0.379457 12.1265C0.259136 12.2467 0.163692 12.3895 0.0985813 12.5466C0.0334711 12.7037 -2.77112e-05 12.8721 1.84413e-07 13.0422V16.7049C-9.06118e-05 16.875 0.0333473 17.0435 0.0984024 17.2006C0.163458 17.3578 0.258854 17.5006 0.379138 17.6209C0.499421 17.7412 0.642233 17.8366 0.799409 17.9016C0.956584 17.9667 1.12504 18.0001 1.29515 18H4.9594C5.12949 18 5.29791 17.9665 5.45503 17.9014C5.61216 17.8363 5.75492 17.7409 5.87514 17.6206L16.8666 6.63236C17.5928 5.90241 18.0003 4.91462 18 3.88504C17.9997 2.85547 17.5918 1.86788 16.8653 1.13829ZM15.0326 2.96968C15.2749 3.21277 15.411 3.5419 15.4113 3.8851C15.4115 4.22829 15.2758 4.55761 15.0339 4.80104L14.118 5.71687L12.2856 3.88518L13.2011 2.96968C13.3214 2.8494 13.4641 2.75399 13.6212 2.6889C13.7784 2.6238 13.9468 2.5903 14.1168 2.5903C14.2869 2.5903 14.4553 2.6238 14.6124 2.6889C14.7695 2.75399 14.9124 2.8494 15.0326 2.96968ZM4.42321 15.4098H2.59058V13.5785L10.4538 5.71639L12.2862 7.54808L4.42321 15.4098Z"
            fill="currentColor" />
        </svg> {{ 'Edit' | translate}}
      </button>
    </div>
  </div>
  <div class="floated_listing_info" *ngIf="!device.isDesktop">
    <div class="floated_listing_btns">
      <button (click)="openDeleteReasons()">
        <svg data-id="delete_icon" viewBox="0 0 14 14" fill="none">
          <rect x="11.9543" y="1.00391" width="1.47519" height="15.4895" rx="0.737594"
            transform="rotate(45 11.9543 1.00391)" fill="#2D3142" stroke="#2D3142" stroke-width="2" />
          <rect x="12.9958" y="11.9531" width="1.47519" height="15.4895" rx="0.737594"
            transform="rotate(135 12.9958 11.9531)" fill="#2D3142" stroke="#2D3142" stroke-width="2" />
        </svg>

      </button>
      <button (click)="edit()">
        <svg data-id="edit_icon" viewBox="0 0 18 18" fill="none">
          <path
            d="M16.8653 1.13829C16.5044 0.777415 16.076 0.491146 15.6046 0.295836C15.1331 0.100526 14.6277 0 14.1174 0C13.607 0 13.1017 0.100526 12.6302 0.295836C12.1587 0.491146 11.7303 0.777415 11.3695 1.13829L0.379457 12.1265C0.259136 12.2467 0.163692 12.3895 0.0985813 12.5466C0.0334711 12.7037 -2.77112e-05 12.8721 1.84413e-07 13.0422V16.7049C-9.06118e-05 16.875 0.0333473 17.0435 0.0984024 17.2006C0.163458 17.3578 0.258854 17.5006 0.379138 17.6209C0.499421 17.7412 0.642233 17.8366 0.799409 17.9016C0.956584 17.9667 1.12504 18.0001 1.29515 18H4.9594C5.12949 18 5.29791 17.9665 5.45503 17.9014C5.61216 17.8363 5.75492 17.7409 5.87514 17.6206L16.8666 6.63236C17.5928 5.90241 18.0003 4.91462 18 3.88504C17.9997 2.85547 17.5918 1.86788 16.8653 1.13829ZM15.0326 2.96968C15.2749 3.21277 15.411 3.5419 15.4113 3.8851C15.4115 4.22829 15.2758 4.55761 15.0339 4.80104L14.118 5.71687L12.2856 3.88518L13.2011 2.96968C13.3214 2.8494 13.4641 2.75399 13.6212 2.6889C13.7784 2.6238 13.9468 2.5903 14.1168 2.5903C14.2869 2.5903 14.4553 2.6238 14.6124 2.6889C14.7695 2.75399 14.9124 2.8494 15.0326 2.96968ZM4.42321 15.4098H2.59058V13.5785L10.4538 5.71639L12.2862 7.54808L4.42321 15.4098Z"
            fill="currentColor" />
        </svg>
      </button>
    </div>
    <app-listing-offers [listingID]="listing.listingID" (click)="offersVisible = true"></app-listing-offers>

  </div>

</ng-template>

<ng-template #rejectMode>
  <div class="rejected_list">
    <h2>{{ "Rejected" | translate }}</h2>
    <ul *ngIf="listing.rejectionReason">
      <li>
        <svg data-id="cross_icon" width="23.761" height="24" viewBox="0 0 23.761 24">
          <g id="Group_1" data-name="Group 1" transform="translate(-11.879 -12)">
            <rect id="Rectangle_1" data-name="Rectangle 1" width="1.714" height="12.121" rx="0.857"
              transform="translate(27.337 19.043) rotate(45)" fill="#dc2d2d" />
            <rect id="Rectangle_2" data-name="Rectangle 2" width="1.714" height="12.121" rx="0.857"
              transform="translate(28.548 27.613) rotate(135)" fill="#dc2d2d" />
            <ellipse id="Ellipse_1" data-name="Ellipse 1" cx="11.881" cy="12" rx="11.881" ry="12"
              transform="translate(11.879 12)" fill="#dc2d2d" opacity="0.15" />
          </g>
        </svg>

        {{ listing.rejectionReason.name | translate }}
      </li>
    </ul>
    <div class="listing_btns" *ngIf="listing.rejectionReason!.userCanRevert">
      <app-red-btn [bigBtn]="true" [btnText]="'Delete' | translate" [btnType]="'button'"
        (click)="openDeleteReasons()"></app-red-btn>
      <app-dark-btn [bigBtn]="true" [btnText]="'Edit' | translate" [btnType]="'button'" (click)="edit()"></app-dark-btn>
    </div>
  </div>
</ng-template>

<p-dialog header="" [closable]="true" [modal]="true" [(visible)]="reasonVisible" [style]="{ width: '50vw' }">
  <form [formGroup]="form" class="reason_form">
    <div class="modelTop reasons_box">
      <h3>{{ "Removing Reason" | translate }}</h3>
      <ng-container *ngIf="reasons$ | async as reasons">
        <div *ngFor="let reason of reasons" class="reason_item">
          <p-radioButton [inputId]="reason.id.toString()" formControlName="RemoveReason"
            [value]="reason.id"></p-radioButton>
          <label [for]="reason.id">{{ reason.name | translate }}</label>
        </div>
      </ng-container>
    </div>
    <div class="model_actions">
      <app-dark-btn [btnDisabled]="!form.valid" [btnText]="'Continue' | translate" btnType="button" [bigBtn]="true"
        (click)="delete()"></app-dark-btn>
    </div>
  </form>
</p-dialog>

<p-dialog header="" [closable]="true" [dismissableMask]="true" [modal]="true" [(visible)]="offersVisible"
  [appendTo]="'body'" [position]="'bottom'" [styleClass]="'suit_for_content_style'">
  <app-listing-offers [listingID]="listing.listingID"></app-listing-offers>

</p-dialog>