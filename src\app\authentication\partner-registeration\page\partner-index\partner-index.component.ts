import { CommonModule, DOCUMENT, KeyValuePipe } from '@angular/common';
import { ChangeDetectorRef, Component, Inject } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, UntypedFormControl, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Store } from '@ngrx/store';
import { HeicConversionService } from '@src/app/core/services/heic.service';
import { BrowserService } from '@src/app/modules/core/service/browser.service';
import { FetchingPageDataComponent } from '@src/app/shared/components/fetching-page-data/fetching-page-data.component';
import { LocationFilterComponent } from '@src/app/shared/components/location-filter/location-filter.component';
import { CategoryMenuDTO } from '@src/app/shared/models/lookup.model';
import { getCategories } from '@src/app/store/app/selectors/app.selector';
import { PrimeTemplate } from 'primeng/api';
import { ButtonDirective } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { Ripple } from 'primeng/ripple';
import { SelectButtonModule } from 'primeng/selectbutton';
import { debounceTime, map, Observable, of, switchMap } from 'rxjs';
import { AlertHandlerService } from 'src/app/modules/core/alerts/alert-handler.service';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { AuthService } from 'src/app/shared/services/auth.service';
import { PatternValidatorService } from 'src/app/shared/services/pattern-validator/pattern-validator.service';
import { FormUtils } from 'src/utils/form-utils';
import { PasswordInputComponent } from '../../../../shared/components/inputs/password-input/password-input.component';
import { PhoneInputComponent } from '../../../../shared/components/inputs/phone-input/phone-input.component';
import { SecondaryBtnComponent } from '../../../../shared/components/secondary-btn/secondary-btn.component';
import { NtranslatePipe } from '../../../../shared/pipes/ntranslate.pipe';
import { PartnerService } from '../../services/partner.service';

@UntilDestroy({ checkProperties: true })

@Component({
  selector: 'app-partner-index',
  standalone: true,
  imports: [CommonModule, ButtonDirective, Ripple, RouterLink, FormsModule, ReactiveFormsModule, PhoneInputComponent, InputTextModule, PasswordInputComponent, DialogModule, SelectButtonModule, PrimeTemplate, CheckboxModule, SecondaryBtnComponent, KeyValuePipe, NtranslatePipe, DropdownModule, LocationFilterComponent, FetchingPageDataComponent],
  templateUrl: './partner-index.component.html',
  styleUrl: './partner-index.component.scss'
})
export class PartnerIndexComponent {
  requiredInput(arg0: string): any {
    return !this.registerForm.controls[arg0].valid;
  }

  showTermsAndConditions: boolean = false;

  countries!: any[];
  selectedCountry!: any;
  public registerForm: FormGroup;
  accepted: boolean = false;
  imageBase: string;
  fileToUpload: any;
  emailExist: boolean = false;
  area: number = 0;
  location: number = 0;

  accountTypeOptions: any[] = [
    { label: 'Business', value: 'business' },
  ];

  url: any;

  categories$!: Observable<{ label: string; id: any; }[]>;


  phonePlaceholder: string = '* ' + this.translateService.instant('Phone Number');
  passwordPlaceholder: string = '* ' + this.translateService.instant('Password');
  emailPlaceholder: string = '* ' + this.translateService.instant('RequiredEmailAddress');
  locationPlaceholder: string = '* ' + this.translateService.instant('Location');
  businessPhonePlaceholder: string = this.translateService.instant('Business Number');
  businessNamePlaceholder: string = '* ' + this.translateService.instant('Business Name');
  firstNamePlaceholder: string = '* ' + this.translateService.instant('First Name');
  lastNamePlaceholder: string = '* ' + this.translateService.instant('Last Name');
  businessTypePlaceholder: string = '* ' + this.translateService.instant('BusinessType');


  constructor(
    private _authService: AuthService,
    private _formBuilder: FormBuilder,
    private translateService: TranslationService,
    private _router: Router,
    private alertService: AlertHandlerService,
    private cdr: ChangeDetectorRef,
    private partnerService: PartnerService,
    private activetedRoute: ActivatedRoute,
    private browser: BrowserService,
    private hcs: HeicConversionService,
    private store: Store,
    @Inject(DOCUMENT) private document: Document

  ) {
    // Countries
    this.countries = [
      { name: '+20', code: 'EG' },
      // { name: '+966', code: 'SA' },
      // { name: '+971', code: 'AE' },
    ];

    this.selectedCountry = this.countries[0];
  }
  ngAfterViewInit(): void {
    this.cdr.detectChanges();

  }

  ngOnInit(): void {

    if (this.document.documentElement.lang === 'ar') {
      this.phonePlaceholder = this.translateService.instant('Phone Number') + ' *';
    }


    // this.partnerService.getContactTypes().subscribe((res) => {
    //   console.log(res);
    // });

    this.activetedRoute.params.subscribe(res => {
      if (res['code']) {
        this.browser.setStorageItem("referralCode", res['code']);
      }
    });

    this.categories$ = this.store.select(getCategories).pipe(
      untilDestroyed(this),
      map(res => res.menu),
      map((categories: CategoryMenuDTO[]) => {
        return categories.map((category: CategoryMenuDTO) => {

          return {
            label: this.translateService.instant(category.name),
            id: category.id,
          };
        })
      }),
      map(categories => [...categories, { label: 'All', id: '' }])


    );


    this.registerForm = this._formBuilder.group({
      phone: new UntypedFormControl(null, [
        Validators.required,
        Validators.pattern(
          /^[\+]?[0-9]+[\s]?,01[0-5]\d{1,8}/
        ),
        Validators.minLength(15)
      ]),
      password: new FormControl('', Validators.compose([
        Validators.required,
        PatternValidatorService.PatternValidator(/^(?=.*[A-Z])(?=.*[a-z])/, { hasUppercaseAndLowercase: true }),
        PatternValidatorService.PatternValidator(/^(?=.*[0-9]).*$/, { hasNumbers: true }),
        PatternValidatorService.PatternValidator(/^(?=.*[^a-zA-Z0-9])(?!.*\s).*$/, { hasSymbols: true }),
        Validators.minLength(8),
        Validators.maxLength(32)
      ])),
      agree: new FormControl(null, Validators.requiredTrue),
      businessName: ['', [
        FormUtils.nameValidator,
        FormUtils.lengthValidator
      ]],
      businessPhone: new UntypedFormControl(null),
      firstName: ['', [
        FormUtils.nameValidator,
        FormUtils.lengthValidator
      ]],
      lastName: ['', [
        FormUtils.nameValidator,
        FormUtils.lengthValidator
      ]],
      email: ['', [Validators.required, this.emailValidator]],
      areaID: new FormControl(0, [Validators.pattern(/^[1-9]\d*$/)]),
      regionId: new FormControl(0, [Validators.pattern(/^[1-9]\d*$/)]),
      accountType: new FormControl("business", Validators.required),
      businessCategoryId: new FormControl("", Validators.required),

      code: new FormControl(""),
    });

    /**
     * 
     */
    const businessPhoneControl = this.registerForm.get('businessPhone');

    this.registerForm.get('businessPhone').valueChanges
      .subscribe(value => {

        if (value === '+20,null' || value === '+20,') {
          businessPhoneControl.setValidators(null);
        } else {
          businessPhoneControl.setValidators([
            Validators.required,
            Validators.pattern(
              /^[\+]?[0-9]+[\s]?,0\d{1,3}\d{6,8}/
            ),
            Validators.minLength(13)
          ]);

        }

        businessPhoneControl.updateValueAndValidity({ onlySelf: true, emitEvent: false });
      });


    this.registerForm
      .get('email')!
      .valueChanges.pipe(
        debounceTime(300),
        switchMap((value) => {

          if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)) {
            return of(false);
          }

          return this._authService.checkEmailExist(value)
        })
      )
      .subscribe((result) => {
        this.emailExist = result ? true : false;
      });

    this.activetedRoute.params.subscribe(res => {
      if (res['code']) {
        this.registerForm.patchValue({
          code: res['code']
        });
      }
    });


  }

  isValidRegion() {
    return this.registerForm.controls['regionId'].valid;
  }
  openTermsAndConditions() {
    this.showTermsAndConditions = true;
  }

  onChangeArea(value: any) {

    this.area = value?.id ?? 0;
    this.location = value?.parentId ?? 0;

    this.registerForm.patchValue({
      areaID: value?.parentId ?? 0,
      regionId: value?.id ?? 0
    });

  }

  emailValidator(control: FormControl,) {
    if (control.value === null || control.value === '') {
      return null;
    }

    if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(control.value)) {
      return { lengthPattern: "InvalidEmail" };
    }
    return null;
  }



  readURL(input: any) {
    if (this.browser.isBrowser()) {
      if (input.target.files && input.target.files[0]) {
        var reader = new FileReader();
        reader.onload = function (e: any) {
          let imgSrc = document.getElementById('uploadedImg');
          imgSrc?.setAttribute('src', e.target?.result);
        };
        reader.readAsDataURL(input.target.files[0]);
      }
    }
  }


  get registerFormControl() {
    return this.registerForm.controls;
  }

  validateValue(controlName: string) {
    return this.registerForm.controls[controlName].valid &&
      this.registerForm.controls[controlName].touched;
  }

  getValue(controlName: string) {
    return this.registerForm.controls[controlName].value;
  }

  validateEmail = () => {


    return (
      this.registerForm.controls['email'].invalid &&
      this.registerForm.controls['email'].dirty
    );
  };


  public validateControl = (controlName: string) => {

    return (
      this.registerForm.controls[controlName].invalid &&
      this.registerForm.controls[controlName].dirty
    );
  };

  public hasError = (controlName: string, errorName: string) => {
    return this.registerForm.controls[controlName].hasError(errorName);
  };

  submit() {
    if (!this.registerForm.valid) { return }
    const formData = { ...this.registerForm.value, businessCategoryId: this.registerForm.value.businessCategoryId.id || null };
    const phoneDetails = this.registerForm.controls['phone'].value.split(',');
    const businessPhoneDetails = this.registerForm.controls['businessPhone'].value.split(',');

    formData.ContactInfos = [
      {
        ContactValue: businessPhoneDetails[1],
        ContactTypeId: 2,
        IsPrimary: false,
        IsVisible: true
      }
    ];



    this.partnerService.setData(formData);
    this.partnerService.update('phone', phoneDetails[1]);
    if (this.fileToUpload) {
      const reader = new FileReader();
      reader.readAsDataURL(this.fileToUpload);
      reader.onload = () => {
        let result = reader.result?.toString() || '';
        this.partnerService.update("image", result);
      };
    }

    this._authService
      .phoneNumberExists(phoneDetails[1])
      .subscribe((x) => {
        if (!x) {
          this._router.navigate(['/authentication/register-partner/verification']);
        }
        else {
          this.alertService.error({ message: this.translateService.instant('PhoneNumberExists') })
        }
      });
  }

  async handleFileInput($event: any) {

    if (this.browser.isBrowser()) {

      let file = $event.target.files

      let imageFile = file.item(0);

      let convertedFile;

      if (!FormUtils.validImageFile(imageFile)) {
        this.alertService.warn({ message: this.translateService.instant('Invalid_Extension') });
        return;
      }

      var validHic = FormUtils.validHicFile(imageFile);

      if (validHic) {
        convertedFile = await this.hcs.convertIfHeic(imageFile);
      }





      var reader = new FileReader();

      reader.onload = (event: any) => {
        this.fileToUpload = validHic ? convertedFile : imageFile;
        this.url = event.target.result
      };

      reader.readAsDataURL(validHic ? convertedFile : imageFile);

    }

  }

  clearImage(e) {
    e.preventDefault();
    e.stopPropagation();
    this.url = null;
    this.fileToUpload = null;
    const imgSrc = document.getElementById('uploadedImg');
    if (imgSrc) {
      imgSrc.setAttribute('src', '');
    }
  }

  onChangeAccept(event: any) {
    this.partnerService.acceptRegistration = event.checked;
  }

}
