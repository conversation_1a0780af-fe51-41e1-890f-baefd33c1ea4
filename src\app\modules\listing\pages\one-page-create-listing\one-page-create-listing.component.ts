import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>I<PERSON> } from '@angular/common';
import { HttpEvent, HttpEventType, HttpResponse } from '@angular/common/http';
import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, HostListener, Input, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Store } from '@ngrx/store';
import { MetaService } from '@src/app/modules/core/service/meta.service';
import { ConversionEvents } from '@src/app/shared/constants/conversion-events';
import { AdsRoutes, StripAdsType } from '@src/app/shared/models/lookup.model';
import { AdsService } from '@src/app/shared/services/ads.service';
import { MenuItem } from 'primeng/api/menuitem';
import { ButtonDirective } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { Ripple } from 'primeng/ripple';
import { Observable, combineLatest, map, take, tap } from 'rxjs';
import { User } from 'src/app/authentication/models/dto/user.dto.model';
import { AlertHandlerService } from 'src/app/modules/core/alerts/alert-handler.service';
import { BrowserService } from 'src/app/modules/core/service/browser.service';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { itemDetails, itemImages } from 'src/app/modules/listing/models/item.details.model';
import { LocationForm } from 'src/app/modules/listing/models/location.form.model';
import { InnerListingService } from 'src/app/modules/listing/services/inner-listing.service';
import { ComponentCanDeactivate } from 'src/app/shared/guards/prevent-leave.guard';
import { CreateListingModel, ListingDetails, ListingPaymentMethod, ListingPropsFormPosition, SelectedProperty, TempSelectedProperty } from 'src/app/shared/models/listing.model';
import { CommonService } from 'src/app/shared/services/common.service';
import { DeviceDetectionService } from 'src/app/shared/services/device-detection.service';
import { ListingService } from 'src/app/shared/services/listing.service';
import { LookupService } from 'src/app/shared/services/lookup.service';
import { getCategories, getSettings, getUser } from 'src/app/store/app/selectors/app.selector';
import { DarkBtnComponent } from '../../../../shared/components/dark-btn/dark-btn.component';
import { SecondaryBtnComponent } from '../../../../shared/components/secondary-btn/secondary-btn.component';
import { SuccessIconComponent } from '../../../../shared/components/success-icon/success-icon.component';
import { NtranslatePipe } from '../../../../shared/pipes/ntranslate.pipe';
import { FormActiveCategoryComponent } from '../../components/form-active-category/form-active-category.component';
import { FormImagesComponent } from '../../components/form-images/form-images.component';
import { FormListingDetailsLocationComponent } from '../../components/form-listing-details-location/form-listing-details-location.component';
import { FormListingDetailsPropsComponent } from '../../components/form-listing-details-props/form-listing-details-props.component';
import { FormListingDetailsReviewComponent } from '../../components/form-listing-details-review/form-listing-details-review.component';
import { FormListingDetailsSwappInterestComponent } from '../../components/form-listing-details-swapp-interest/form-listing-details-swapp-interest.component';
import { FormListingDetailsComponent } from '../../components/form-listing-details/form-listing-details.component';
import { AdoptionValidatorService } from '../../validator/adoption.validator';
@UntilDestroy({ checkProperties: true })
@Component({
  selector: 'app-one-page-create-listing',
  templateUrl: './one-page-create-listing.component.html',
  styleUrls: ['./one-page-create-listing.component.scss'],
  providers: [InnerListingService, AdoptionValidatorService],
  standalone: true,
  imports: [DialogModule, SuccessIconComponent, DarkBtnComponent, RouterLink, NgIf, NgClass, ButtonDirective, Ripple, FormListingDetailsReviewComponent, FormActiveCategoryComponent, FormListingDetailsPropsComponent, FormListingDetailsComponent, FormListingDetailsLocationComponent, FormListingDetailsSwappInterestComponent, FormImagesComponent, SecondaryBtnComponent, NgFor, AsyncPipe, NtranslatePipe]
})
export class OnePageCreateListingComponent implements OnInit, AfterViewInit, ComponentCanDeactivate {
  @ViewChild('maintarget') targetHost: ElementRef<HTMLInputElement>;

  activeSection: boolean = false;
  isEdit: boolean = false;
  id: number = 0;
  categories$: Observable<MenuItem[]>;
  categoriesList: MenuItem[];
  selectedCategory?: MenuItem | null;
  selectedSubCategory?: MenuItem | null;
  tabIndex: number = 1;
  modalVisible = false;
  uploadProgress = 0;
  isUploading = false;
  headerTitles!: string[];

  props: SelectedProperty[] = [];
  tempProps: TempSelectedProperty[] = [];
  details: itemDetails;
  detailImages: itemImages;
  intersetedCategories: number[] | null;
  selectedSwappOption: number;
  loading = false;

  locationForm: LocationForm;
  userID: string = '';
  maxWidth: number = 1000;
  locationName: string = '';
  ListingPropsFormPosition = ListingPropsFormPosition;

  user: User;

  categoryHelpText!: string;

  @Input() mainCategory: MenuItem;
  @Input() subCategory: MenuItem;




  constructor(
    private _lookupService: LookupService,
    private _listingService: ListingService,
    private router: Router,
    private alertService: AlertHandlerService,
    private activatedRoute: ActivatedRoute,
    public device: DeviceDetectionService,
    private innerService: InnerListingService,
    private cdr: ChangeDetectorRef,
    private translateService: TranslationService,
    private store: Store,
    private commonService: CommonService,
    private browser: BrowserService,
    private adsService: AdsService,
    private metaService: MetaService,


  ) {

    this.innerService.onePageMode = true;
    this.headerTitles = [
      this.translateService.instant('Select Category'),
      this.translateService.instant('Listing Details'),
      this.translateService.instant('Listing Details'),
      this.translateService.instant('Review your ad')
    ];

    this.adsService.setAdsRoute(AdsRoutes.MyListings);
    this.adsService.setAdsStripType(StripAdsType.MyListing);

  }

  canDeactivate(): boolean {
    if (this.browser.isBrowser()) {
      return window.confirm('Do you really want to leave?');
    }
    return false;
  }


  @HostListener('window:beforeunload', ['$event'])
  yourfunction($event) {
    return $event.returnValue = 'Your changes will not be saved';
  }


  gotoPreview() {
    this.innerService.fetchData();
    this.innerService.noError().subscribe(res => {
      if (res) {
        this.tabIndex = 3;
        this.moveTop();
      }

    });
  }

  gotoHome() {
    this.router.navigate(['/home']);
  }

  restart() {
    this.selectedCategory = null;
    this.selectedSubCategory = null;
    this.tabIndex = 1;
  }

  moveNext() {
    if (this.selectedCategory && !this.selectedSubCategory && this.selectedCategory.items!.length > 0) {
      this.alertService.message({ message: this.translateService.instant("Please Select a Subcategory") });
    } else if (this.selectedCategory && this.selectedSubCategory) {
      this.tabIndex++;
      this.moveTop();
    } else if (!this.selectedCategory) {
      this.alertService.message({ message: this.translateService.instant("Please Select a Category") });
    } else {
      this.tabIndex++;
      this.moveTop();
    }
  }

  moveTop() {
    setTimeout(() => {
      this.browser.scrollTo({
        behavior: 'smooth',
        top: 0,
      });
    }, 10);
  }

  selectMainCategory(item: MenuItem) {
    this.categoryHelpText = this.translateService.instant('Please Select a Subcategory');
    this.selectedCategory = item;
    this.selectedSubCategory = null;
    if (this.selectedCategory.items!.length > 0) {
      this.activeSection = true;
      this.tabIndex = 1;
      //this.headerTitles[1] = item.label;
    } else {
      this.activeSection = false;
      this.headerTitles[1] = '';
      this.tabIndex = 1;
    }
    this.moveTop();
  }

  selectSubCategory(item: MenuItem) {

    this.selectedSubCategory = item;
    this.categoryHelpText = '';
  }

  saveSwappOption(event) {
    this.selectedSwappOption = event;
    if (this.selectedSwappOption == 1) {
      this.intersetedCategories = null;
    }
  }

  saveInterest(event) {
    this.details.treadBy = event.treadBy;
    this.intersetedCategories = event.intersetedCategories;
  }

  saveLocation(event) {
    this.locationName = event.location.name;
    this.locationForm = { location: event.location.id, reachOutType: 0 };
  }


  saveProps(event) {


    this.updatePropsArray(this.props, event.items);
    this.updatePropsArray(this.tempProps, event.tempItems);
  }

  updatePropsArray(original, items) {

    items.forEach(itemToUpdate => {
      const existingIndex = original.findIndex(item => item.catgoryPropertyID === itemToUpdate.catgoryPropertyID);
      if (existingIndex !== -1) {
        original[existingIndex] = itemToUpdate;
      } else {
        original.push(itemToUpdate);
      }
    });


  }


  saveDetails(event) {
    this.details = event;
  }
  saveImages(event) {

    this.detailImages = event;
  }

  back() {
    if (this.tabIndex == 7 && !this.intersetedCategories) {
      this.tabIndex = 5;
      return;
    }

    if (this.headerTitles[this.tabIndex] == '') {
      this.tabIndex--;
    } else {
      this.tabIndex--;
    }

    if (this.tabIndex < 0) {
      this.tabIndex = 0;
    }

    if (this.tabIndex == 0) {
      this.router.navigate(['/home']);
    }

  }

  ngAfterViewInit(): void {

  }

  ngOnInit(): void {

    this.metaService.set({});

    this.store.select(getUser).pipe(untilDestroyed(this)).subscribe(res => {
      if (res) {
        this.user = res;
      } else {
        //this.router.navigate(['/']);
      }
    });

    this.categoryHelpText = this.translateService.instant('Select Category');

    this.categories$ = this.store.select(getCategories).pipe(untilDestroyed(this), map(res => res.menu), map(res => this._lookupService.convertToMenuItems(res)), tap(res => {
      this.categoriesList = res;
      this.checkEdit();
    }));

  }

  checkEdit() {
    this.activatedRoute.params.subscribe(res => {
      if (res['id']) {
        this.id = res['id'];

        this.cdr.detectChanges();


        combineLatest([this._listingService.getListingDetails(this.id), this.store.select(getSettings)]).pipe(take(1)).subscribe(([x, setting]) => {

          if (!x.succeded) return;

          this.isEdit = true;
          let data = x.data;

          this.userID = data.userID!;


          if (this.user && this.userID != this.user.id) {
            this.router.navigate(['/advertisement/' + this.id]);
          }

          //adding category
          const { category, parent } = this._lookupService.findCategoryById(data.categoryID, this.categoriesList);


          if (!parent && category) {
            this.selectedCategory = category;
          } else if (parent && category) {
            this.selectedCategory = parent;
            this.selectedSubCategory = category;
            this.tabIndex = 1;
            this.activeSection = true;

          }

          //this.moveNext();

          // adding properties
          const _props: any = [];
          const _tempProps: any = [];
          if (data.properties && data.properties.length > 0) {
            for (let index = 0; index < data.properties.length; index++) {
              const element = data.properties[index];

              const value = element.propTypeName == 'Boolean' ? Boolean(element.value) : element.value;
              _tempProps.push({ id: element.id, catgoryPropertyID: element.catgoryPropertyID, name: element.name, value: value, propTypeName: element.propTypeName });
              _props.push({ id: element.id, catgoryPropertyID: element.catgoryPropertyID, name: element.name, value: value, propTypeName: element.propTypeName });

              if (setting['adoption_category_property'] && element.name == setting['adoption_category_property'].Value && element.value == "true") {
                this.innerService.updateIsForAdoption(true);
              }
            }
            this.props = _props;
            this.tempProps = _tempProps;
          }



          // save details
          const _details: itemDetails = {
            form: {
              name: data.name,
              description: data.description,
              price: data.price,
              condition: { id: data.condtionID, name: data.conditionName },
              paymentMethod: data.paymentMethod,
              keywords: data.keyWords,
            }
          };

          const _detailImages: itemImages = {
            urls: [],
            coverURL: [],
            filesUploaded: []
          };



          //save location 
          const location: LocationForm = {
            location: data.areaID,
            reachOutType: 1
          }
          this.locationForm = location;

          //save swapp option and interests
          if (data.interedtedCategoris.length > 0) {
            this.selectedSwappOption = 2;
            this.intersetedCategories = data.interedtedCategoris;
          } else {
            this.selectedSwappOption = 1;
            this.intersetedCategories = [];
          }

          //save treadBy

          const uid = this.generateUid();


          this._listingService.getlistingimages(this.id).subscribe((x) => {
            let coverurl: any = {};
            coverurl.url = x.data.coverImage?.imageURL;
            coverurl.id = x.data.coverImage?.imageID;
            coverurl.uid = uid;
            _detailImages.urls = x.data.listingImages.map((y) => {

              const nuid = this.generateUid();

              return ({
                id: y.imageID,
                url: y.imageURL,
                uid: nuid
              })
            });


            if (coverurl.url) {
              _detailImages.urls.unshift(coverurl);
              _detailImages.coverURL = coverurl;
            }

            this.details = _details;
            this.detailImages = _detailImages;
            this.details.treadBy = data.treadBy ?? '';

            this.moveNext();


          });

          this.cdr.detectChanges();

        });



      }
    });
  }

  generateUid() {
    return new Date().getTime() + '-' + Math.floor(Math.random() * 1000000);
  }

  createListing(e) {
    this.loading = true;

    this.innerService.setLoading(true);



    if (this.id) {

      this.trySaveImage().then((res: any) => {


        let model: ListingDetails = {
          name: this.details.form.name,
          categoryID: this.selectedSubCategory ? +this.selectedSubCategory.id! : +this.selectedCategory!.id!,
          areaID: this.locationForm.location,
          condtionID: this.details.form.condition.id,
          description: this.details.form.description,
          paymentMethod: this.details.form.paymentMethod,
          price: this.details.form.price ?? 0,
          listingID: parseInt(this.id.toString()),
          treadBy: this.details.form.paymentMethod == ListingPaymentMethod.cash ? '' : this.details.treadBy,
          keyWords: this.details.form.keywords!.map(e => e.id),
          SelectedProperties: this.props.filter(item => {
            if (item.id) {
              return true;
            }
            return item.value ? true : false
          }),
          interedtedCategoris: this.details.form.paymentMethod == ListingPaymentMethod.cash ? [] : (this.intersetedCategories ?? []),
          coverID: this.detailImages.coverURL.id ?? 0,
          images: res.body?.data ?? []

        };

        this._listingService.editListing(model).subscribe({
          next: (x) => {
            this.innerService.setLoading(false);
            this.modalVisible = true;

          }, error: () => {
            this.showError();
          }
        });

      }).catch(e => {

        this.showError(e);

      });

    } else {


      this.commonService.pushDataLayer({
        event: ConversionEvents.Post_Listing,
      });


      this.trySaveImage().then((res: any) => {


        let model: CreateListingModel = {
          name: this.details.form.name,
          categoryID: this.selectedSubCategory ? +this.selectedSubCategory.id! : +this.selectedCategory!.id!,
          areaID: this.locationForm.location,
          condtionID: this.details.form.condition.id,
          description: this.details.form.description,
          paymentMethod: this.details.form.paymentMethod,
          selectedProperties: this.props.filter(item => item.value),
          price: this.details.form.price ?? 0,
          userID: this.userID,
          Images: res.body?.data,
          treadBy: this.details.form.paymentMethod == ListingPaymentMethod.cash ? '' : this.details.treadBy,
          interedtedCategoris: this.details.form.paymentMethod == ListingPaymentMethod.cash ? [] : (this.intersetedCategories ?? []),
          keyWords: this.details.form.keywords!.map(e => e.id),
        };



        this._listingService.createListing(model).subscribe({
          next: (x) => {
            this.id = x.data;
            this.innerService.setLoading(false);
            this.modalVisible = true;

            this.commonService.pushDataLayer({
              event: ConversionEvents.Post_Listing_Success,
              listing_id: x.data,
              listing_name: this.details.form.name,
              category_name: this.selectedSubCategory ? this.selectedSubCategory.label : this.selectedCategory!.label,
              seller_id: this.userID,
            });

            this.commonService.pushDataLayer({
              'event': ConversionEvents.Add_Listing,
              'listing_id': x.data,
              'seller_id': this.userID,
            });

          },
          error: () => {
            this.showError();
          }
        });

      }).catch(e => {

        this.showError(e);

      });




    }





  }

  showError(reason?) {

    this.innerService.setLoading(false);
    this.alertService.error({ message: (reason && typeof reason == 'string') ? this.translateService.instant(reason) : this.translateService.instant('Something wrong happen') }, (e) => {

    });
  }

  trySaveImage() {

    return new Promise(async (res, rej) => {

      if (this.detailImages.deletedImages && this.detailImages.deletedImages.length > 0) {
        for (let i = 0; i < this.detailImages.deletedImages.length; i++) {
          await this._listingService.deleteLisintgImage(this.detailImages.deletedImages[i]).subscribe();
        }
      }

      if (this.detailImages.filesUploaded && this.detailImages.filesUploaded.length > 0) {
        let uploadData = new FormData();
        let foundCover = false;



        const imageProcessingPromises = this.detailImages.filesUploaded.map(file => this.resizeAndConvertImage(file));

        Promise.all(imageProcessingPromises)
          .then(blobs => {


            for (let index = 0; index < blobs.length; index++) {


              const element = blobs[index];

              if (element) {




                if (element.uid == this.detailImages.coverURL.uid) {
                  if (foundCover) continue;
                  foundCover = true;
                  uploadData.append('cover', element.blob, element.name);

                } else {
                  uploadData.append('listing', element.blob, element.name);
                }
              }
            }

            if (this.id) {
              uploadData.append('id', this.id.toString());
            }

            this.isUploading = false;




            this._listingService.tryUploadImgs(uploadData).subscribe({
              next: (event: HttpEvent<any>) => {


                switch (event.type) {
                  case HttpEventType.UploadProgress:
                    this.uploadProgress = Math.round(100 * event.loaded / event.total!);

                    break;
                  case HttpEventType.Response:
                    if (event instanceof HttpResponse) {
                      this.isUploading = false;
                      res(event);
                    }
                    break;
                  default:
                    break;
                }
              },
              error: (e) => {
                rej(e);
              }
            });





          }).catch(error => {
            console.error('Error processing images:', error);
            rej(error);
          });






      } else {
        res([]);
      }

    });


    //this.router.navigate(['mylisting']);
  }

  resizeAndConvertImage(data): Promise<any> {
    return new Promise((resolve, reject) => {
      const file: File = data.element;
      const reader = new FileReader();


      reader.readAsDataURL(file);
      reader.onload = (event: any) => {



        const img = new Image();
        img.src = event.target.result;
        img.onload = () => {
          const canvas = document.createElement('canvas');
          const scaleSize = this.maxWidth / img.width;
          canvas.width = this.maxWidth;
          canvas.height = img.height * scaleSize;

          const ctx = canvas.getContext('2d');
          ctx!.drawImage(img, 0, 0, canvas.width, canvas.height);

          canvas.toBlob(blob => {
            if (blob) {
              resolve({ name: file.name, blob, uid: data.uid });
            } else {
              reject('Blob conversion failed');
            }
          }, 'image/jpeg');
        };
      };
      reader.onerror = error => reject(error);
    });
  }



}
