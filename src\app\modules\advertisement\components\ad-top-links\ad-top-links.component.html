<div class="top_details">
  <div class="container">
    <nav aria-label="breadcrumb" *ngIf="parentCategory">
      <ol class="breadcrumb">
        <li class="breadcrumb-item active" aria-current="page">
          <a [routerLink]="['/category/' + parentCategory.id + '/' + (parentCategory!.label | slug)]" class="p-name">{{
            parentCategory!.label }}</a>
        </li>
        <li class="breadcrumb-item active" aria-current="page"
          *ngIf="selectedCategory && selectedCategory.id !== parentCategory.id">
          <i class="pi pi-chevron-right"></i>
          <a [routerLink]="['/category/' + parentCategory.id + '/' + selectedCategory.id + '/' + (selectedCategory!.label | slug)]"
            class="p-name">{{
            selectedCategory!.label }}</a>
        </li>
      </ol>
    </nav>


    <div class="top_btns" *ngIf="viewShareBtns">
      <span (click)="shareVisible = true">
        <svg data-id="share_icon" viewBox="0 0 21 22" fill="none">
          <path
            d="M12.9225 17.1029L6.96045 13.876C6.38816 14.4398 5.66048 14.8228 4.86917 14.9767C4.07786 15.1306 3.25833 15.0486 2.51391 14.741C1.76948 14.4334 1.13349 13.914 0.686116 13.2483C0.238738 12.5825 0 11.8002 0 11C0 10.1998 0.238738 9.41749 0.686116 8.75173C1.13349 8.08597 1.76948 7.56656 2.51391 7.25896C3.25833 6.95137 4.07786 6.86938 4.86917 7.02331C5.66048 7.17725 6.38816 7.56023 6.96045 8.12397L12.9225 4.89712C12.718 3.94509 12.8657 2.95182 13.3386 2.09896C13.8114 1.2461 14.5778 0.59064 15.4976 0.252442C16.4174 -0.085755 17.4292 -0.0840893 18.3479 0.257135C19.2665 0.598359 20.0308 1.25634 20.5007 2.11075C20.9707 2.96516 21.1151 3.95891 20.9074 4.91027C20.6998 5.86162 20.154 6.70702 19.3698 7.29185C18.5857 7.87668 17.6156 8.16186 16.637 8.09525C15.6583 8.02865 14.7366 7.6147 14.0402 6.9291L8.07819 10.1559C8.19712 10.7125 8.19712 11.2875 8.07819 11.8441L14.0402 15.0709C14.7366 14.3853 15.6583 13.9714 16.637 13.9047C17.6156 13.8381 18.5857 14.1233 19.3698 14.7082C20.154 15.293 20.6998 16.1384 20.9074 17.0897C21.1151 18.0411 20.9707 19.0348 20.5007 19.8893C20.0308 20.7437 19.2665 21.4016 18.3479 21.7429C17.4292 22.0841 16.4174 22.0858 15.4976 21.7476C14.5778 21.4094 13.8114 20.7539 13.3386 19.901C12.8657 19.0482 12.718 18.0549 12.9225 17.1029Z"
            fill="currentColor" />
        </svg>
      </span>
      <app-love-button VerifiedClick [listingId]="listing.listingID"></app-love-button>
    </div>
  </div>
</div>


<p-dialog header="" [closable]="true" [modal]="true" [(visible)]="shareVisible" [style]="{width: '40vw'}">
  <div class="modelTop share_box">
    <h2>{{ 'Share Listing' | translate}}</h2>
    <div class="share_list">
      <img src="/assets/img/icons/facebook_btn.svg" (click)="shareOnFacebook()" />
      <img src="/assets/img/icons/messenger_btn.svg" (click)="shareOnMessenger()" />
      <img *ngIf="dv.isMobile" src="/assets/img/icons/whatsapp_btn.svg" (click)="shareOnWhatsApp()" />
      <img src="/assets/img/icons/twitter_btn.svg" (click)="shareOnTwitter()" />
      <img src="/assets/img/icons/share_btn.svg" (click)="shareOnOthers()" />

    </div>
    <div class="copy_link">
      <div>{{ currentLink }}</div>
      <app-dark-btn [btnText]="copyText" btnType="button" (click)="copy()"></app-dark-btn>
    </div>
  </div>
  <div class="model_actions"></div>
</p-dialog>