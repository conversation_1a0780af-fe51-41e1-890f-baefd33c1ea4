import { Injectable } from '@angular/core';
import { DefaultUrlSerializer, UrlTree } from '@angular/router';
@Injectable()
export class UrlSerializerService extends DefaultUrlSerializer {


    override parse(url: string): UrlTree {

        const lastSlashIndex = url.lastIndexOf('/');
        const eurl = url.split('?');

        const pureUrl = eurl[0];
        if (lastSlashIndex !== -1) {
            const valueAfterLastSlash = pureUrl.substring(lastSlashIndex + 1);
            const encodedValue = this.fixedEncodeURIComponent(valueAfterLastSlash);
            url = pureUrl.substring(0, lastSlashIndex + 1) + encodedValue + (eurl.length > 1 ? ('?' + eurl[1]) : '');
        }

        return super.parse(url);
    }

    fixedEncodeURIComponent(str) {

        var isEncoded = decodeURIComponent(str) !== encodeURIComponent(str);

        if (isEncoded) {
            return str.replace(/[!'()*]/g, function (c) {
                return '%' + c.charCodeAt(0).toString(16);
            });
        }


        return (str.indexOf('%') != -1) ? str : encodeURIComponent(str).replace(/[!'()*]/g, function (c) {
            return '%' + c.charCodeAt(0).toString(16);
        });
    }

    extraReplacePraces(str: string): string {
        return str.replace(/[!'()*]/g, function (c) {
            return '%' + c.charCodeAt(0).toString(16);
        });
    }

    withoutQueryParams(url: string): string {
        return url.split('?')[0];
    }






}