import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgI<PERSON> } from '@angular/common';
import { Component } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { DarkBtnComponent } from '@src/app/shared/components/dark-btn/dark-btn.component';
import { LookupDTO } from '@src/app/shared/models/lookup.model';
import { NtranslatePipe } from '@src/app/shared/pipes/ntranslate.pipe';
import { LookupService } from '@src/app/shared/services/lookup.service';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { RadioButtonModule } from 'primeng/radiobutton';
import { map, Observable } from 'rxjs';

@Component({
  selector: 'app-reject-reason-popup',
  standalone: true,
  imports: [NgFor, NgIf, AsyncPipe, ReactiveFormsModule, NtranslatePipe, RadioButtonModule, DarkBtnComponent],
  templateUrl: './reject-reason-popup.component.html',
  styleUrl: './reject-reason-popup.component.scss'
})
export class RejectReasonPopupComponent {

  form: FormGroup;
  reasons$: Observable<LookupDTO[]>;

  constructor(private ref: DynamicDialogRef,
    public config: DynamicDialogConfig,
    private fb: FormBuilder,
    private lookupService: LookupService) { }

  ngOnInit(): void {

    this.reasons$ = this.lookupService.getoffercancellationreasons().pipe(map(res => res.data));
    this.form = this.fb.group({
      reasonId: new FormControl('')
    });
  }

  reason() {
    if (this.ref) {
      this.ref.close(this.form.value);
    }
  }

  cancel() {
    if (this.ref) {
      this.ref.close('');
    }
  }
}
