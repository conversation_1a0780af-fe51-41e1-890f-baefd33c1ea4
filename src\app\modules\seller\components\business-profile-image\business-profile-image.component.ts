import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, ElementRef, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { ProfileModel } from '@src/app/shared/models/profile.model';
import { SellerMemebershipIconComponent } from 'src/app/modules/seller/components/seller-memebership-icon/seller-memebership-icon.component';
import { LazyloadDirective } from 'src/app/shared/directives/lazyload.directive';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-business-profile-image',
  standalone: true,
  imports: [CommonModule, LazyloadDirective, SellerMemebershipIconComponent],
  templateUrl: './business-profile-image.component.html',
  styleUrl: './business-profile-image.component.scss'
})
export class BusinessProfileImageComponent implements OnInit, AfterViewInit, OnChanges {

  @Input() business: ProfileModel;

  sellerImage: string;

  constructor(private elementRef: ElementRef) { }
  ngOnChanges(changes: SimpleChanges): void {
    this.sellerImage = environment.userMediaPath + this.business.userID + '.jpg?v=' + new Date().getTime();
  }

  ngOnInit(): void {
    this.sellerImage = environment.userMediaPath + this.business.userID + '.jpg?v=' + new Date().getTime();
  }

  ngAfterViewInit(): void {
    if (this.business.setting) {
      this.elementRef.nativeElement.style.setProperty(
        '--main-membership-color',
        this.business.setting?.membership.color
      );
    }

  }

}