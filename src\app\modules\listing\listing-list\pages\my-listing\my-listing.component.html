@defer () {
<p-dialog *ngIf="deviceDetection.isMobile || deviceDetection.isTablet" header="" [className]="'filterPopup'"
  [modal]="true" [(visible)]="filterVisible" [style]="{ width: '50vw' }">
  <app-side-filter [hideArea]="true" [hideCategory]="true" [filter]="filter"
    (onSubmit)="onSubmitFilter($event)"></app-side-filter>
</p-dialog>

<div class="container">
  <div class="row">
    <div class="list-col">
      <div class="list-start">
        <h1>{{ "MyListing" | translate }}</h1>
        <div class="listing_dropdown" *ngIf="deviceDetection.isMobile || deviceDetection.isTablet">
          <p-dropdown [options]="types" [(ngModel)]="selectedType" optionLabel="name" (onChange)="onChangeType($event)">
            <ng-template pTemplate="selectedItem">
              <div class="selected-filter-value" *ngIf="selectedType">
                <span>{{ pagination.totalItems }}</span>

                <div class="selected_name">
                  {{ selectedType.name }}
                </div>
              </div>
            </ng-template>
          </p-dropdown>
        </div>
        <ul class="tabs" *ngIf="!deviceDetection.isMobile && !deviceDetection.isTablet">
          <li [ngClass]="{ active: currentStatus == listingStatus.approved }" [routerLink]="['/listing/list/active']">
            {{ "Active" | translate }}
          </li>
          <li [ngClass]="{ active: currentStatus == listingStatus.created }" [routerLink]="['/listing/list/waiting']">
            {{ "Waitingforreview" | translate }}
          </li>
          <li [ngClass]="{ active: currentStatus == listingStatus.rejected }" [routerLink]="['/listing/list/rejected']">
            {{ "Rejected" | translate }}
          </li>
          <li [ngClass]="{ active: currentStatus == listingStatus.sold }" [routerLink]="['/listing/list/sold']">
            {{ "Sold" | translate }}
          </li>
        </ul>
      </div>

      <div class="top_filter_actions">
        <div class="mylisting_search_box">

          <svg class="srch_icon" data-id="search_icon" viewBox="0 0 23 22" fill="none">
            <g>
              <circle cx="7.79784" cy="7.79784" r="7.04784"
                transform="matrix(0.708886 -0.705323 0.708886 0.705323 0 11)" stroke="currentColor"
                stroke-width="1.5" />
              <rect width="1.41779" height="5.23068" rx="0.708895"
                transform="matrix(0.708886 -0.705323 0.708886 0.705323 14.8516 16.6172)" fill="currentColor" />
            </g>
          </svg>
          <input type="text" (keyup)="onkeyup($event)" [(ngModel)]="searchString"
            [placeholder]="'Search_in_my_listing' | translate" />
          <button pButton pRipple type="button" class="p-button-rounded srch_btn" (click)="gotoSearch()">
            <span>
              <svg class="srch_icon" data-id="search_icon" viewBox="0 0 23 22" fill="none">
                <g>
                  <circle cx="7.79784" cy="7.79784" r="7.04784"
                    transform="matrix(0.708886 -0.705323 0.708886 0.705323 0 11)" stroke="currentColor"
                    stroke-width="1.5" />
                  <rect width="1.41779" height="5.23068" rx="0.708895"
                    transform="matrix(0.708886 -0.705323 0.708886 0.705323 14.8516 16.6172)" fill="currentColor" />
                </g>
              </svg>
            </span>
          </button>
        </div>
        <div class="mobile_filter_actions" *ngIf="deviceDetection.isMobile || deviceDetection.isTablet">
          <p-dropdown [className]="'sortDropDown'" [options]="sortBy" [(ngModel)]="selectedSortBy"
            optionLabel="viewValue" optionLabel="viewValue" [placeholder]="'Sortby' | translate"
            (onChange)="onSortingChanged()">
            <ng-template pTemplate="selectedItem">
              <div class="flex align-items-center gap-2" *ngIf="selectedSortBy">
                <div>{{ selectedSortBy.viewValue | translate }}</div>
              </div>
            </ng-template>
            <ng-template let-item pTemplate="item">
              <div class="flex align-items-center gap-2">
                <div>{{ item.viewValue | translate }}</div>
              </div>
            </ng-template>
          </p-dropdown>

          <span class="filter_btn" (click)="filterVisible = true">
            <svg data-id="filter_btn" viewBox="0 0 50 50" fill="none">
              <rect width="50" height="50" rx="25" fill="#3B4158" />
              <circle cx="30.2229" cy="19.7776" r="2.77761" stroke="white" stroke-width="2" />
              <circle cx="20.7776" cy="30.7024" r="2.77761" stroke="white" stroke-width="2" />
              <rect x="17" y="18.832" width="11.3328" height="1.88881" rx="0.944403" fill="white" />
              <rect x="22.6641" y="29.7588" width="11.3328" height="1.88881" rx="0.944403" fill="white" />
            </svg>

          </span>
        </div>
      </div>
    </div>
    <div class="col-lg-5 col-xl-4 listing_side_filter" *ngIf="!deviceDetection.isMobile && !deviceDetection.isTablet">
      <app-side-filter [filter]="filter" [hideArea]="true" [hideCategory]="true"
        (onSubmit)="onSubmitFilter($event)"></app-side-filter>
    </div>
    <div class="col-lg-7 col-xl-8 d-flex flex-column listing_result">
      <div class="items_list">
        <div class="title_box">
          <h2 class="list_title">
            <b>{{ pagination.totalItems }}</b> {{ "MyListing" | translate }}
          </h2>

          <p-dropdown *ngIf="!deviceDetection.isMobile && !deviceDetection.isTablet" [className]="'sortDropDown'"
            [options]="sortBy" [(ngModel)]="selectedSortBy" optionLabel="viewValue" optionLabel="viewValue"
            [placeholder]="'Sortby' | translate" (onChange)="onSortingChanged()">
            <ng-template pTemplate="selectedItem">
              <div class="flex align-items-center gap-2" *ngIf="selectedSortBy">
                <div>{{ selectedSortBy.viewValue | translate }}</div>
              </div>
            </ng-template>
            <ng-template let-item pTemplate="item">
              <div class="flex align-items-center gap-2">
                <div>{{ item.viewValue | translate }}</div>
              </div>
            </ng-template>
          </p-dropdown>
        </div>
        <div class="list_content" *ngIf="listings$ | async as listings">
          <app-item-card-row (onDelete)="onDelete($event)" (onEdit)="onEdit($event)" *ngFor="let item of listings"
            [itemData]="item" [mode]="getModeType(item)">
          </app-item-card-row>

          <app-no-result *ngIf="pagination.totalItems == 0" [type]="2" [showAddListing]="true"
            [subTitle]="'no_result_found_description'"></app-no-result>

          <p-paginator *ngIf="pagination.totalPages! > 1" (onPageChange)="pageChanged($event)"
            [first]="(this.filter.pageNumber! - 1) * pagination.pageSize!" [rows]="pagination.pageSize"
            [totalRecords]="pagination.totalItems" [pageLinkSize]="3" [showFirstLastIcon]="false"></p-paginator>
        </div>
        <div></div>
      </div>
    </div>
  </div>
</div>

<p-dialog header="" [closable]="true" [modal]="true" [(visible)]="reasonVisible" [style]="{ width: '50vw' }">
  <form [formGroup]="form" class="reason_form">
    <div class="modelTop reasons_box">
      <h3>{{ "Removing Reason" | translate }}</h3>
      <ng-container *ngIf="reasons$ | async as reasons">
        <div *ngFor="let reason of reasons" class="reason_item">
          <p-radioButton [inputId]="reason.id.toString()" formControlName="RemoveReason"
            [value]="reason.id"></p-radioButton>
          <label [for]="reason.id">{{ reason.name | translate }}</label>
        </div>
      </ng-container>
    </div>
    <div class="model_actions">
      <app-dark-btn [btnDisabled]="!form.valid" [btnText]="'Continue' | translate" btnType="button" [bigBtn]="true"
        (click)="delete()"></app-dark-btn>
    </div>
  </form>
</p-dialog>
}

@loading {
<div></div>
}

@placeholder {
<div></div>
}