import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FilterResultsService } from 'src/app/modules/core/service/filter-results.service';
import { FeaturedCategoriesComponent } from 'src/app/modules/home/<USER>/featured-categories/featured-categories.component';
import { HeroSectionComponent } from 'src/app/modules/home/<USER>/hero-section/hero-section.component';
import { SmartBannerComponent } from 'src/app/modules/home/<USER>/smart-banner/smart-banner.component';
import { CustomlinkDirective } from 'src/app/modules/home/<USER>/customlink.directive';
import { HomeRoutingModule } from 'src/app/modules/home/<USER>';
import { HomeComponent } from 'src/app/modules/home/<USER>/home.component';
import { CategoriesTabsComponent } from 'src/app/shared/components/categories-tabs/categories-tabs.component';
import { ItemsSliderComponent } from 'src/app/shared/components/items-slider/items-slider.component';
import { ListingSliderSkeletonComponent } from 'src/app/shared/components/skeleton/listing-slider-skeleton/listing-slider-skeleton.component';
import { YouMayLikeComponent } from 'src/app/shared/components/you-may-like/you-may-like.component';
import { IsAuthorizedDirective } from 'src/app/shared/directives/isauthorized.directive';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { MiddleSectionPipe } from './pipes/middle-section.pipe';



@NgModule({
    imports: [
        CommonModule,
        HomeRoutingModule,
        CategoriesTabsComponent,
        HeroSectionComponent,
        FeaturedCategoriesComponent,
        SmartBannerComponent,
        ItemsSliderComponent,
        NtranslatePipe,
        ListingSliderSkeletonComponent,
        YouMayLikeComponent,
        IsAuthorizedDirective,
        CustomlinkDirective,
        MiddleSectionPipe,
        HomeComponent
    ],
    providers: [FilterResultsService]
})
export class HomeModule { }
