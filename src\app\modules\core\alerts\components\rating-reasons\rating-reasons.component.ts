import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { DarkBtnComponent } from '@src/app/shared/components/dark-btn/dark-btn.component';
import { UserRateReason } from '@src/app/shared/models/profile.model';
import { NtranslatePipe } from '@src/app/shared/pipes/ntranslate.pipe';
import { LookupService } from '@src/app/shared/services/lookup.service';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { RadioButtonModule } from 'primeng/radiobutton';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-rating-reasons',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, NtranslatePipe, RadioButtonModule, DarkBtnComponent],
  templateUrl: './rating-reasons.component.html',
  styleUrl: './rating-reasons.component.scss'
})
export class RatingReasonsComponent implements OnInit {

  form: FormGroup;
  reasons$: Observable<UserRateReason[]>;
  message!: string;

  constructor(private ref: DynamicDialogRef, public config: DynamicDialogConfig, private fb: FormBuilder, private lookupService: LookupService) { }
  ngOnInit(): void {
    this.message = this.config.data.message;

    this.reasons$ = this.lookupService.getUserRatesReasons();
    this.form = this.fb.group({
      reasonId: new FormControl(''),
      reasonNote: new FormControl('')
    });
  }

  rate() {
    if (this.ref) {
      this.ref.close(this.form.value);
    }
  }


}
