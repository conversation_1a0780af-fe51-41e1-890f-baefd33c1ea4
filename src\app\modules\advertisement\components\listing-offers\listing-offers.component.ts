import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input, OnInit, signal, WritableSignal } from '@angular/core';
import { Router } from '@angular/router';
import { OfferCreateSuggestedComponent } from '@src/app/modules/offers/components/offer-create-suggested/offer-create-suggested.component';
import { OfferSuggestedCardComponent } from '@src/app/modules/offers/components/offer-suggested-card/offer-suggested-card.component';
import { DialogModule } from 'primeng/dialog';
import { PaginatorModule } from 'primeng/paginator';
import { map, Observable } from 'rxjs';
import { OfferCardComponent } from 'src/app/modules/offers/components/offer-card/offer-card.component';
import { NoResultComponent } from 'src/app/shared/components/no-result/no-result.component';
import { Pagination } from 'src/app/shared/models/base.response.model';
import { GetOfferFilters, OfferFilters, OfferStatus, OffersView } from 'src/app/shared/models/offer.model';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { OfferService } from 'src/app/shared/services/offer.service';

@Component({
  selector: 'app-listing-offers',
  templateUrl: './listing-offers.component.html',
  styleUrls: ['./listing-offers.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    OfferCardComponent,
    NoResultComponent,
    PaginatorModule,
    NtranslatePipe,
    DialogModule,
    OfferCreateSuggestedComponent,
    OfferSuggestedCardComponent

  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ListingOffersComponent implements OnInit {

  @Input() viewMode: boolean;
  @Input() listingID: number;

  offers$: Observable<OffersView[]>;
  offer!: OffersView;

  currentIndex: WritableSignal<number> = signal(0);
  makeOfferVisible: boolean = false;

  suggestedList$: Observable<OffersView[]>;


  pagination: Pagination = {
    currentPage: 1,
    pageSize: 12,
    totalItems: 0,
    totalPages: 0,
  };

  suggestedPagination: Pagination = {
    currentPage: 1,
    pageSize: 12,
    totalItems: 0,
    totalPages: 0,
  };

  filter: OfferFilters = {
    pageNumber: 1,
  };

  constructor(
    private _offersService: OfferService,
    private router: Router
  ) {


  }




  ngOnInit(): void {
    let filters: GetOfferFilters = {
      listingID: this.listingID,
      search: '',
      pageNumber: 1,
      pageSize: 100,
      StatusIds: [OfferStatus.Accepted, OfferStatus.Created, OfferStatus.Declinded, OfferStatus.Withdraw, OfferStatus.Swapped]
    };

    this.offers$ = this._offersService.getAll(filters).pipe(map(res => res.body.data), map(res => {
      this.pagination.totalItems = res?.totalCount;
      this.pagination.totalPages = res?.totalPages;

      return res.items;
    }));

    let suggestedFilters: GetOfferFilters = {
      listingID: this.listingID,
      search: '',
      pageNumber: 1,
      pageSize: 100,
    };

    this.suggestedList$ = this._offersService.getSuggested(suggestedFilters).pipe(map(res => res.body.data), map(res => {
      this.suggestedPagination.totalItems = res?.totalCount;
      this.suggestedPagination.totalPages = res?.totalPages;
      return res.items;
    }));
  }

  onChoose(offer) {
    this.offer = offer;
    this.makeOfferVisible = true;
  }

  pageChanged(event: any): void {
    this.filter.pageNumber = (parseInt(event.page) + 1);
    //this.changeRoute([]);
  }

  suggestedPageChanged(event: any): void {
    this.filter.pageNumber = (parseInt(event.page) + 1);
    //this.changeRoute([]);
  }

  changeRoute(url) {
    setTimeout(() => {
      window.scrollTo({
        behavior: 'smooth',
        top: 0,
      });
    }, 0);
    this.router.navigate(url, { queryParams: this.filter });
  }


}
