import { Injectable } from '@angular/core';
import { environment } from '@src/environments/environment';
import { OAuthService } from 'angular-oauth2-oidc';
import { BehaviorSubject, Observable } from 'rxjs';
import { WebSocketSubject, webSocket } from 'rxjs/webSocket';


@Injectable({
    providedIn: 'root',
})
export class AppSignalRService {
    private socket$!: WebSocketSubject<any>;
    private isConnected = false;

    private messageSubject = new BehaviorSubject<any>(null);
    public message$ = this.messageSubject.asObservable(); // Expose message stream



    constructor(private oauthService: OAuthService) {
        console.log("Initializing SignalR service...");

        this.connect();
    }

    private connect() {
        if (this.oauthService.hasValidAccessToken()) {
            const token = this.oauthService.getAccessToken();
            if (!this.isConnected) {
                this.socket$ = webSocket({
                    url: `${environment.signalrHub}?access_token=${token}`,
                    deserializer: (msg) => {
                        return msg.data;
                    },
                    serializer: (msg) => JSON.stringify(msg) + '',
                    openObserver: {
                        next: () => {
                            console.log('WebSocket Connected');
                            this.isConnected = true;
                            this.sendHandshake();
                        }
                    },
                    closeObserver: {
                        next: () => {
                            console.warn('WebSocket Disconnected');
                            this.isConnected = false;
                            setTimeout(() => this.connect(), 5000);
                        }
                    }
                });

            }

        }
    }

    getMessages(): Observable<any> {
        return this.socket$.asObservable();
    }

    private sendHandshake(): void {
        if (this.isConnected && this.socket$) {
            const handshakeMessage = { protocol: "json", version: 1 };
            this.socket$.next(handshakeMessage);
            console.log('Handshake message sent:', handshakeMessage);
        } else {
            console.warn('Cannot send handshake, WebSocket is not connected.');
        }
    }



    public sendMessage(message: any): void {
        if (this.isConnected && this.socket$) {
            this.socket$.next(message);
        } else {
            console.warn('Cannot send message, WebSocket is not connected.');
        }
    }

    public closeConnection(): void {
        if (this.socket$) {
            this.socket$.complete();
            this.isConnected = false;
        }
    }

}