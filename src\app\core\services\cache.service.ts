import { Injectable } from '@angular/core';

@Injectable({
    providedIn: 'root',
})
export class CacheService {
    private cache = new Map<string, any>();
    private maxCacheSize = 50 * 1024 * 1024; // 50 MB in bytes

    constructor() { }

    setCache(key: string, data: any) {
        const size = this.getObjectSize(data);
        this.manageCacheSize(size);// Check current cache size
        this.cache.set(key, { data, size, timestamp: new Date().getTime() });
    }

    getCache(key: string): any {
        const cacheEntry = this.cache.get(key);
        if (cacheEntry) {
            const { data, timestamp, maxAge } = cacheEntry;
            const currentTime = new Date().getTime();
            if (currentTime - timestamp < maxAge) {
                return data;
            } else {
                this.cache.delete(key); // Invalidate expired cache
            }
        }
        return null;
    }

    private manageCacheSize(newEntrySize: number) {
        let currentSize = this.getCacheSize();
        // Clear the amount of new cache size required.
        while (currentSize + newEntrySize > this.maxCacheSize && this.cache.size > 0) {
            const oldestKey = this.getOldestCacheKey();
            if (oldestKey) {
                const entrySize = this.cache.get(oldestKey)?.size || 0;
                this.cache.delete(oldestKey);
                currentSize -= entrySize;
            }
        }
    }

    private getCacheSize(): number {
        let totalSize = 0;
        this.cache.forEach(entry => {
            totalSize += entry.size;
        });
        return totalSize;
    }

    private getOldestCacheKey(): string | undefined {
        let oldestKey: string | undefined;
        let oldestTimestamp = Infinity;

        this.cache.forEach((entry, key) => {
            if (entry.timestamp < oldestTimestamp) {
                oldestTimestamp = entry.timestamp;
                oldestKey = key;
            }
        });

        return oldestKey;
    }
    // Get current cache size
    private getObjectSize(obj: any): number {
        const jsonString = JSON.stringify(obj);
        return new Blob([jsonString]).size;
    }

    deleteCache(key: string) {
        this.cache.delete(key);
    }



}



