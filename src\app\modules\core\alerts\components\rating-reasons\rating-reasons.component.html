<form [formGroup]="form" class="reason_form">
    <div class="modelTop reasons_box">
        <h3>{{ 'What_is_Wrong_With' | translate}} {{ message}} ?</h3>
        <ng-container *ngIf="reasons$ | async as reasons">
            <div *ngFor="let reason of reasons" class="reason_item">
                <p-radioButton [inputId]="reason.id.toString()" formControlName="reasonId"
                    [value]="reason.id"></p-radioButton>
                <label [for]="reason.id">{{ reason.name | translate }}</label>
            </div>
        </ng-container>
        <div class="messageholder">
            <textarea formControlName="reasonNote" [placeholder]="'Let_us_know_what_was_wrong' | translate"></textarea>
        </div>

    </div>
    <div class="model_actions">
        <app-dark-btn [btnDisabled]="!form.valid" [btnText]="'submit_reason' | translate" btnType="button"
            [bigBtn]="true" (click)="rate()"></app-dark-btn>
    </div>
</form>