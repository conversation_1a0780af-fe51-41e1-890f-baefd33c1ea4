@import "variables.scss";
@import "mixins.scss";

.list {
  display: flex;
  flex-flow: row wrap;
  gap: 10px;

  @include Large {
    gap: 20px;
  }

  // @include rtl2(gap, 1px 10px);


}

.image {
  border-radius: 12px;
  background: linear-gradient(0deg, rgba(123, 69, 172, 0.06) 0%, rgba(123, 69, 172, 0.00) 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;

  img {
    width: 73px;
    height: 55px;
    object-fit: contain;

  }

  @include XLarge {
    height: 144px;
    margin-inline: auto;

    img {
      width: 114px;
      height: 80px;
    }
  }

}

.item {
  max-width: calc(100% / 3 - 7px);
  flex: 1;
  cursor: pointer;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;

  span {
    text-align: center;
    display: block;
    margin-top: 9px;
    font-size: 0.87rem;
    font-weight: bold;
    @include rtl2(line-height, 1.6);
  }

  &.banner {
    flex: 1;
    max-width: none;

    >a {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    img {
      width: 100%;
      flex: 1;
      object-fit: contain;
      border-radius: 12px;
    }


  }


}

.featured_categories {
  padding-bottom: 28px;
  display: flex;
  padding: 15px;
}