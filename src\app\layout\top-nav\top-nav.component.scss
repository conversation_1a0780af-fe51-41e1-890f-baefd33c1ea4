@import "variables";
@import "mixins";



.app_header {
  background-color: $secondary;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;

  .container {
    position: relative
  }



  @include rtl2(direction, ltr);

  .header_wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    position: relative;
    z-index: 2;
    background-color: $secondary;
    min-height: 50px;
  }

  @include Large {
    padding: 8px 20px 12px;

    .header_wrapper {
      padding: 0px;
      z-index: 1;
    }
  }


}