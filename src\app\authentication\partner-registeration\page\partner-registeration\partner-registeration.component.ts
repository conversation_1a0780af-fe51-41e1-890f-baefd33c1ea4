import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { MetaService } from '@src/app/modules/core/service/meta.service';
import { AuthImgSideComponent } from '../../../../shared/components/auth-img-side/auth-img-side.component';
import { LanguageButtonComponent } from '../../../../shared/components/language-button/language-button.component';

@Component({
  selector: 'app-partner-registeration',
  standalone: true,
  imports: [AuthImgSideComponent, RouterOutlet, LanguageButtonComponent],
  templateUrl: './partner-registeration.component.html',
  styleUrl: './partner-registeration.component.scss'
})
export class PartnerRegisterationComponent implements OnInit {

  constructor(private meta: MetaService) {
  }
  ngOnInit(): void {
    this.meta.set({
      image: 'https://4sw.app/assets/images/4swapp-business-og.jpg?v=1.1.2',
      title:'منصة واحدة .. لكل حلول البيع، الشراء، و البدل'
      
    });
  }

}
