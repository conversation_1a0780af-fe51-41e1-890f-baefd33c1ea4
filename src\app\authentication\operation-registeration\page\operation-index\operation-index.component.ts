import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, UntypedFormControl, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { HeicConversionService } from '@src/app/core/services/heic.service';
import { BrowserService } from '@src/app/modules/core/service/browser.service';
import { debounceTime, of, switchMap } from 'rxjs';
import { OperationService } from 'src/app/authentication/operation-registeration/services/operation.service';
import { AlertHandlerService } from 'src/app/modules/core/alerts/alert-handler.service';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { AuthService } from 'src/app/shared/services/auth.service';
import { PatternValidatorService } from 'src/app/shared/services/pattern-validator/pattern-validator.service';
import { FormUtils } from 'src/utils/form-utils';
import { ButtonDirective } from 'primeng/button';
import { Ripple } from 'primeng/ripple';
import { PhoneInputComponent } from '../../../../shared/components/inputs/phone-input/phone-input.component';
import { InputTextModule } from 'primeng/inputtext';
import { NgClass, NgIf, NgFor, KeyValuePipe } from '@angular/common';
import { PasswordInputComponent } from '../../../../shared/components/inputs/password-input/password-input.component';
import { LocationInputComponent } from '../../../../shared/components/inputs/location-input/location-input.component';
import { SelectButtonModule } from 'primeng/selectbutton';
import { PrimeTemplate } from 'primeng/api';
import { CheckboxModule } from 'primeng/checkbox';
import { SecondaryBtnComponent } from '../../../../shared/components/secondary-btn/secondary-btn.component';
import { NtranslatePipe } from '../../../../shared/pipes/ntranslate.pipe';

@Component({
    selector: 'app-operation-index',
    templateUrl: './operation-index.component.html',
    styleUrls: ['./operation-index.component.scss'],
    standalone: true,
    imports: [ButtonDirective, Ripple, RouterLink, FormsModule, ReactiveFormsModule, PhoneInputComponent, InputTextModule, NgClass, NgIf, NgFor, PasswordInputComponent, LocationInputComponent, SelectButtonModule, PrimeTemplate, CheckboxModule, SecondaryBtnComponent, KeyValuePipe, NtranslatePipe]
})
export class OperationIndexComponent implements OnInit {

  countries!: any[];
  selectedCountry!: any;
  public registerForm: FormGroup;
  accepted: boolean = false;
  imageBase: string;
  fileToUpload: any;
  emailExist: boolean = false;

  accountTypeOptions: any[] = [
    { label: 'Consumer', value: 'consumer' },
    { label: 'Business', value: 'business' },
  ];

  url: any;

  constructor(
    private _authService: AuthService,
    private _formBuilder: FormBuilder,
    private translateService: TranslationService,
    private _router: Router,
    private alertService: AlertHandlerService,
    private cdr: ChangeDetectorRef,
    private operatorService: OperationService,
    private activetedRoute: ActivatedRoute,
    private browser: BrowserService,
    private hcs: HeicConversionService

  ) {
    // Countries
    this.countries = [
      { name: '+20', code: 'EG' },
      // { name: '+966', code: 'SA' },
      // { name: '+971', code: 'AE' },
    ];

    this.selectedCountry = this.countries[0];
  }
  ngAfterViewInit(): void {
    this.cdr.detectChanges();

  }

  ngOnInit(): void {


    this.registerForm = this._formBuilder.group({
      phone: new UntypedFormControl(null, [
        Validators.required,
        Validators.pattern(
          /^[\+]?[0-9]+[\s]?,01[0-5]\d{1,8}/
        ),
        Validators.minLength(15)
      ]),
      password: new FormControl('', Validators.compose([
        Validators.required,
        PatternValidatorService.PatternValidator(/^(?=.*[A-Z])(?=.*[a-z])/, { hasUppercaseAndLowercase: true }),
        PatternValidatorService.PatternValidator(/^(?=.*[0-9]).*$/, { hasNumbers: true }),
        PatternValidatorService.PatternValidator(/^(?=.*[^a-zA-Z0-9])(?!.*\s).*$/, { hasSymbols: true }),
        Validators.minLength(8),
        Validators.maxLength(32)
      ])),
      agree: new FormControl(null, Validators.required),
      firstName: ['', [
        FormUtils.nameValidator,
        FormUtils.lengthValidator
      ]],
      lastName: ['', [
        FormUtils.nameValidator,
        FormUtils.lengthValidator
      ]],
      email: ['', [this.emailValidator]],
      areaID: new FormControl("", Validators.required),
      AccountType: new FormControl("consumer", Validators.required),
      code: new FormControl(""),
    });

    this.registerForm
      .get('email')!
      .valueChanges.pipe(
        debounceTime(300),
        switchMap((value) => {

          if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)) {
            return of(false);
          }

          return this._authService.checkEmailExist(value)
        })
      )
      .subscribe((result) => {
        this.emailExist = result ? true : false;
      });

    this.activetedRoute.params.subscribe(res => {
      if (res['code']) {
        this.registerForm.patchValue({
          code: res['code']
        });
      }
    });

  }

  emailValidator(control: FormControl,) {
    if (control.value === null || control.value === '') {
      return null;
    }

    if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(control.value)) {
      return { lengthPattern: "InvalidEmail" };
    }
    return null;
  }



  readURL(input: any) {
    if (this.browser.isBrowser()) {
      if (input.target.files && input.target.files[0]) {
        var reader = new FileReader();
        reader.onload = function (e: any) {
          let imgSrc = document.getElementById('uploadedImg');
          imgSrc?.setAttribute('src', e.target?.result);
        };
        reader.readAsDataURL(input.target.files[0]);
      }
    }
  }


  get registerFormControl() {
    return this.registerForm.controls;
  }

  validateValue(controlName: string) {
    return this.registerForm.controls[controlName].valid &&
      this.registerForm.controls[controlName].touched;
  }

  getValue(controlName: string) {
    return this.registerForm.controls[controlName].value;
  }

  validateEmail = () => {

    if (this.registerForm.controls['email'].value === null || this.registerForm.controls['email'].value === '') {
      return null;
    }

    return (
      this.registerForm.controls['email'].invalid &&
      this.registerForm.controls['email'].dirty
    );
  };


  public validateControl = (controlName: string) => {

    return (
      this.registerForm.controls[controlName].invalid &&
      this.registerForm.controls[controlName].dirty
    );
  };

  public hasError = (controlName: string, errorName: string) => {
    return this.registerForm.controls[controlName].hasError(errorName);
  };

  isValidForm() {

  }

  submit() {
    const phoneDetails = this.registerForm.controls['phone'].value.split(',');
    this.operatorService.setData(this.registerForm.value);
    this.operatorService.update('phone', phoneDetails[1]);
    if (this.fileToUpload) {
      const reader = new FileReader();
      reader.readAsDataURL(this.fileToUpload);
      reader.onload = () => {
        let result = reader.result?.toString() || '';
        this.operatorService.update("image", result);
      };
    }
    // this._router.navigate(['/authentication/register-operator/verification']);

    // return;

    this._authService
      .phoneNumberExists(phoneDetails[1])
      .subscribe((x) => {
        if (!x) {
          this._router.navigate(['/authentication/register-operator/verification']);
        }
        else {
          this.alertService.error({ message: this.translateService.instant('PhoneNumberExists') })
        }
      });
  }

  async handleFileInput($event: any) {

    if (this.browser.isBrowser()) {

      let file = $event.target.files

      let imageFile = file.item(0);

      let convertedFile;

      if (!FormUtils.validImageFile(imageFile)) {
        this.alertService.warn({ message: this.translateService.instant('Invalid_Extension') });
        return;
      }

      var validHic = FormUtils.validHicFile(imageFile);

      if (validHic) {
        convertedFile = await this.hcs.convertIfHeic(imageFile);
      }





      var reader = new FileReader();

      reader.onload = (event: any) => {
        this.fileToUpload = validHic ? convertedFile : imageFile;
        this.url = event.target.result
      };

      reader.readAsDataURL(validHic ? convertedFile : imageFile);

    }

  }

}
