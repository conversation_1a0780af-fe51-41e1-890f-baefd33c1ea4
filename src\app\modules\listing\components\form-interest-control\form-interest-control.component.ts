import { CommonModule } from '@angular/common';
import { Component, Input, OnDestroy, OnInit, forwardRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { Observable, map } from 'rxjs';
import { ToggleClassDirective } from 'src/app/shared/directives/toggle-class.directive';
import { LookupDTO } from "src/app/shared/models/lookup.model";
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { LookupService } from 'src/app/shared/services/lookup.service';

@Component({
  selector: 'app-form-interest-control',
  standalone: true,
  imports: [CommonModule, ToggleClassDirective, NtranslatePipe],
  templateUrl: './form-interest-control.component.html',
  styleUrls: ['./form-interest-control.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FormInterestControlComponent),
      multi: true,
    },
  ],
})
export class FormInterestControlComponent implements ControlValueAccessor, OnInit, OnDestroy {
  @Input() profileMode: boolean;
  onChange: any = () => { };
  onTouch: any = () => { };
  SelectedList = new Set<number>();
  items$: Observable<LookupDTO[]>;
  maxSelection: number = 3;

  constructor(private _lookupService: LookupService) { }
  ngOnDestroy(): void {
    // throw new Error('Method not implemented.');`
  }
  setDisabledState?(isDisabled: boolean): void {

  }
  writeValue(obj: any): void {
    if (obj) {
      obj.forEach(Id => {
        this.SelectedList.add(Id);
      });
    }
  }
  registerOnChange(fn: any): void {
    this.onChange = fn;
  }
  registerOnTouched(fn: any): void {
    this.onTouch = fn;
  }

  SelectedItem(Id: number) {
    if (!this.SelectedList.has(Id)) {
      if (this.SelectedList.size == this.maxSelection && !this.profileMode) return;

      this.SelectedList.add(Id);
    } else {
      this.SelectedList.delete(Id);
    }
    this.onChange([...this.SelectedList.values()]);
  }

  ngOnInit(): void {
    this.items$ = this._lookupService.getParentCategories().pipe(map(res => res.data));
  }

}
