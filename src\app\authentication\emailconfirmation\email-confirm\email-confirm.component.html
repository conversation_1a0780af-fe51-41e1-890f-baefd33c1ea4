<div *ngIf="confirmation$ | async as confirmation" class="confirmation_box">
    <div [ngSwitch]="confirmation">
        <div *ngSwitchCase="'error'" class="error_message">
            <svg data-id="error_icon" viewBox="0 0 142 142" fill="none">
                <path
                    d="M56.75 8.80459C65.5679 3.71356 76.4321 3.71356 85.25 8.80459L117.738 27.5614C126.556 32.6525 131.988 42.0611 131.988 52.2432V89.7568C131.988 99.9389 126.556 109.348 117.738 114.439L85.25 133.195C76.4321 138.286 65.5679 138.286 56.75 133.195L24.2622 114.439C15.4443 109.348 10.0122 99.9389 10.0122 89.7568V52.2432C10.0122 42.0611 15.4443 32.6525 24.2622 27.5614L56.75 8.80459Z"
                    stroke="#EA4335" stroke-opacity="0.21" />
                <path opacity="0.15"
                    d="M62 14.1962C67.5692 10.9808 74.4308 10.9808 80 14.1962L115.694 34.8038C121.263 38.0192 124.694 43.9615 124.694 50.3923V91.6077C124.694 98.0385 121.263 103.981 115.694 107.196L80 127.804C74.4308 131.019 67.5692 131.019 62 127.804L26.3064 107.196C20.7372 103.981 17.3064 98.0385 17.3064 91.6077V50.3923C17.3064 43.9615 20.7372 38.0192 26.3064 34.8038L62 14.1962Z"
                    fill="#DC2D2D" />
                <rect x="84.4185" y="54.418" width="4.04056" height="42.4259" rx="2.02028"
                    transform="rotate(45 84.4185 54.418)" fill="#DC2D2D" stroke="#DC2D2D" stroke-width="2" />
                <rect x="87.2754" y="84.418" width="4.04056" height="42.4259" rx="2.02028"
                    transform="rotate(135 87.2754 84.418)" fill="#DC2D2D" stroke="#DC2D2D" stroke-width="2" />
            </svg>



            <h2>
                {{ 'EmailConfirmation' | translate }}
            </h2>

            <a [routerLink]="['/home']" class="continue_btn" rel="nofollow">
                {{ 'Continue' | translate}}
            </a>







        </div>

        <div class="success_page" *ngSwitchDefault>
            <div class="success_content">
                <app-success-icon></app-success-icon>
                <h2>
                    {{ 'EmailConfirmation' | translate }}
                </h2>
                <h3>{{ 'EmailConfirmed' | translate}}</h3>

                <a [routerLink]="['/home']" class="continue_btn" rel="nofollow">
                    {{ 'Continue' | translate}}
                </a>
            </div>


            <img src="/assets/img/success-img.png" class="footer_img" />

        </div>
    </div>
</div>