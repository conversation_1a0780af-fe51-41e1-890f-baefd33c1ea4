import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { Form<PERSON><PERSON>er, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { DialogModule } from 'primeng/dialog';
import { RadioButtonModule } from 'primeng/radiobutton';
import { map, Observable } from 'rxjs';
import { AlertHandlerService } from 'src/app/modules/core/alerts/alert-handler.service';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { DarkBtnComponent } from 'src/app/shared/components/dark-btn/dark-btn.component';
import { IsAuthorizedDirective } from 'src/app/shared/directives/isauthorized.directive';
import { VerifiedClickDirective } from 'src/app/shared/directives/isverifiedclick.directive';
import { LookupDTO } from 'src/app/shared/models/lookup.model';
import { ReportListing } from 'src/app/shared/models/reports.model';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { ReportsService } from 'src/app/shared/services/reports.service';

@Component({
  selector: 'app-listing-instructions',
  standalone: true,
  imports: [CommonModule, NtranslatePipe, DialogModule, IsAuthorizedDirective, RadioButtonModule, VerifiedClickDirective, ReactiveFormsModule, DarkBtnComponent],
  templateUrl: './listing-instructions.component.html',
  styleUrls: ['./listing-instructions.component.scss']
})
export class ListingInstructionsComponent implements OnInit {

  @Input() viewMode: boolean;
  @Input() listingID: number;

  reportVisible: boolean = false;


  reasons$: Observable<LookupDTO[]>;
  form: FormGroup;



  constructor(
    private reportService: ReportsService,
    private alertModel: AlertHandlerService,
    private fb: FormBuilder,
    private translateService: TranslationService) {

    this.form = this.fb.group({
      report: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    this.form = this.fb.group({
      report: new FormControl('', [Validators.required]),
    });
  }



  openReport() {
    this.reasons$ = this.reportService.getListingReportsReason().pipe(map(res => res.data));
    this.reportVisible = true;
  }

  report() {
    this.reportVisible = false;
    let model: ReportListing = {
      reason: this.form.value.report,
      listingID: this.listingID,
    };

    this.reportService.reportListing(model).subscribe(
      {
        next: x => {
          if (x.succeded) {

            this.alertModel.success({
              message: this.translateService.instant("Reported Successfully")
            });
          }
        },
        error: err => {
          this.alertModel.warn({
            message: this.translateService.instant("SomthingWrongOrListingAlreadyReported")
          });
        }
      }
    );
  }


}
