import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxOtpInputComponent } from 'ngx-otp-input';
import { ButtonModule } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { SelectButtonModule } from 'primeng/selectbutton';
import { PrimengModuleModule } from 'src/app/modules/shared-modules/primeng-module.module';
import { GrayBtnComponent } from 'src/app/shared/components/gray-btn/gray-btn.component';
import { LocationInputComponent } from 'src/app/shared/components/inputs/location-input/location-input.component';
import { PasswordInputComponent } from 'src/app/shared/components/inputs/password-input/password-input.component';
import { PhoneInputComponent } from 'src/app/shared/components/inputs/phone-input/phone-input.component';
import { PrimaryBtnComponent } from 'src/app/shared/components/primary-btn/primary-btn.component';
import { SecondaryBtnComponent } from 'src/app/shared/components/secondary-btn/secondary-btn.component';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { SharedModule } from 'src/app/shared/shared.module';
import { OperationRegisterationRoutingModule } from './operation-registeration-routing.module';
import { OperationEditPhoneComponent } from './page/operation-edit-phone/operation-edit-phone.component';
import { OperationIndexComponent } from './page/operation-index/operation-index.component';
import { OperationInterestComponent } from './page/operation-interest/operation-interest.component';
import { OperationRegisterationComponent } from './page/operation-registeration/operation-registeration.component';
import { OperationSuccessComponent } from './page/operation-success/operation-success.component';
import { OperationVerificationComponent } from './page/operation-verification/operation-verification.component';


@NgModule({
    imports: [
        CommonModule,
        OperationRegisterationRoutingModule,
        NtranslatePipe,
        ReactiveFormsModule,
        PrimengModuleModule,
        SharedModule,
        FormsModule,
        SelectButtonModule,
        NgxOtpInputComponent,
        ButtonModule,
        PrimaryBtnComponent,
        SecondaryBtnComponent,
        PhoneInputComponent,
        PasswordInputComponent,
        CheckboxModule,
        LocationInputComponent,
        GrayBtnComponent,
        OperationRegisterationComponent,
        OperationEditPhoneComponent,
        OperationSuccessComponent,
        OperationIndexComponent,
        OperationInterestComponent,
        OperationVerificationComponent,
    ]
})
export class OperationRegisterationModule { }
