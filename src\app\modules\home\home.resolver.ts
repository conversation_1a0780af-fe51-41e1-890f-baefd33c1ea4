import { inject } from "@angular/core";
import { ActivatedRouteSnapshot, ResolveFn, RouterStateSnapshot } from "@angular/router";
import { HomeSections } from "@src/app/shared/models/listing.model";
import { ListingService } from "@src/app/shared/services/listing.service";
import { FormUtils } from "@src/utils/form-utils";
import { EMPTY, map, mergeMap, of, take } from "rxjs";
let blackList = ['pageSize'];

export const homeResolver: ResolveFn<HomeSections[]> = (
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
) => {
    // const cacheService = inject(RedisCacheService);
    const homeService = inject(ListingService);
    return homeService.getHomeSections().pipe(
        take(1),
        map(res => res.data.sections),
        // switchMap((res) => {
        //     return defer(async function () {
        //         const sss = await cacheService.set("home-section", res, '5m');
        //         return res;
        //     });
        // }),
        map(res => res.map((item) => {
            if (item.filters) {

                let notnullobject = Object.keys(item.filters).reduce((acc, key) => {
                    if (item.filters[key] !== null && !blackList.includes(key)) {
                        acc[key] = item.filters[key];
                    }
                    return acc;
                }, {});

                let fparams = { ...notnullobject };


                fparams = Object.keys(fparams).reduce((acc, key) => {

                    switch (key) {
                        case 'selectedProperties':
                            if (Array.isArray(fparams[key])) {
                                let changePropTypeToType = fparams[key].map(prop => {
                                    if (prop.propTypeName == "Range") {
                                        return { ...prop, type: prop.propTypeName };

                                    }
                                    return { ...prop, type: prop.propTypeName, value: FormUtils.parseValue(prop.value) };
                                });
                                acc[key] = btoa(JSON.stringify(changePropTypeToType));

                            } else {
                                acc[key] = fparams[key];
                            }

                            break;
                        case 'conditionsIDs':
                            acc['conditions'] = fparams[key];
                            break;
                        case 'paymentMethodIDs':
                            acc['payments'] = [...fparams[key], 3];
                            break;


                        default:
                            acc[key] = FormUtils.parseValue(fparams[key])
                    }
                    return acc;
                }, {});

                item.filters = { ...fparams };

            }


            return {
                ...item,
            };
        })),
        mergeMap(data => {

            if (data) {
                return of(data)
            } else {
                return EMPTY;
            }
        })
    );
};