import { CommonModule, NgOptimizedImage } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { HelpPagesService } from '@shared/services/helppages.service';
import { AppcenterService } from '@src/app/shared/services/appcenter.service';
import { CommonService } from '@src/app/shared/services/common.service';
import { DeviceDetectionService } from '@src/app/shared/services/device-detection.service';
import { map, Observable } from 'rxjs';
import { BrowserService } from 'src/app/modules/core/service/browser.service';
import { HelpPagesDTO } from 'src/app/shared/models/helppage.model';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
  standalone: true,
  imports: [CommonModule, RouterModule, NtranslatePipe, NgOptimizedImage],
  host: { ngSkipHydration: 'true' },


})
export class FooterComponent implements OnInit {
  blogUrl: string = '';
  pagesList$: Observable<HelpPagesDTO[]>;


  constructor(private _helpPagesService: HelpPagesService, private browser: BrowserService, private langService: AppcenterService, private commonService: CommonService, public ds: DeviceDetectionService) {
    this.blogUrl = this.commonService.getBlogUrl();

  }


  ngOnInit(): void {
    this.getPages();
  }
  getPages() {
    let currentLang = this.langService.lang;
    this.pagesList$ = this._helpPagesService.getbylang(currentLang).pipe(map(x => x.data));
  }


}
