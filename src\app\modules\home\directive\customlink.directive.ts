import { Directive, ElementRef, inject, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Store } from '@ngrx/store';
import { CommonService } from '@src/app/shared/services/common.service';
import { LookupService } from '@src/app/shared/services/lookup.service';
import { getCategories } from '@src/app/store/app/selectors/app.selector';
import { map, tap } from 'rxjs';
import { HomeSections } from 'src/app/shared/models/listing.model';
@UntilDestroy({ checkProperties: true })
@Directive({
  selector: '[appCustomlink]',
  standalone: true
})
export class CustomlinkDirective implements OnInit {
  @Input() directLink: HomeSections;

  link: string;
  queryParams: any;

  blackList = ['pageSize'];

  _lookupService = inject(LookupService);


  store = inject(Store);

  constructor(private router?: Router, private cs?: CommonService, private el?: ElementRef) { }
  ngOnInit(): void {

    this.transform(this.directLink);

  }

  transform(item: HomeSections) {


    if (item.filters) {

      let fparams = { ...item.filters };


      this.queryParams = { ...fparams };


      if (fparams['categoryIDs'] && fparams['categoryIDs'] > 0) {

        this.store.select(getCategories).pipe(untilDestroyed(this), map(res => res.menu), map(res => this._lookupService.convertToMenuItems(res)), tap(items => {

          let categoryId = fparams['categoryIDs'];
          delete fparams['categoryIDs'];
          const { category, parent } = this._lookupService.findCategoryById(categoryId, items);
          this.link = '/category/' + (parent ? parent.id + '/' + category.id : category.id);

        })).subscribe();

      } else {
        this.link = '/search';
      }

    } else {
      this.link = item.type == 'category' ? ('/category/' + (item.parentId ? item.parentId + '/' + item.id : item.id)) : '/tag/' + (item.label);

    }

    //set href attribute
    const urlTree = this.router!.createUrlTree([this.link], { queryParams: this.queryParams });
    const href = this.router!.serializeUrl(urlTree);
    const element = this.el.nativeElement;
    element.setAttribute('href', this.cs.getSiteURL() + href);


  }


}

