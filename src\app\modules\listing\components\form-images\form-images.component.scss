@import "mixins";
@import "variables";

.images_listio {
  position: relative;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
  gap: 10px;
  margin: 20px 0px;

  @include Large {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));

  }

  &.loading:before {
    content: '';
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    background: #e0e0e0c9;
    z-index: 100;
  }

  &.loading::after {
    content: '';
    display: inline-block;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    margin-inline-end: 10px;
    border-top: 3px solid $primary;
    border-right: 3px solid transparent;
    box-sizing: border-box;
    animation: loaderRotation 1s linear infinite;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateY(-50%);
    z-index: 101;
  }

}

.images_holder {
  margin-bottom: 40px;
  line-height: 2;

  h2 {
    font-size: 18px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    @include rtl2(font-size, 15px);
  }

  h3 {
    font-size: 14px;
    margin: 4px 0px;
    @include rtl2(font-size, 12px);
  }

  .upload-input {
    position: relative;

    .add-img {
      width: 106px;
      height: 84px;
      border-radius: 12px;
      background: url('/assets/img/add-photo.svg') no-repeat center center;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .icon {
      background-color: #8a51be;
      border-radius: 50%;
      font-size: 2rem;
      color: #ffffff;
      padding: 1rem;
    }

    input {
      font-size: 0;
      height: 100%;
      width: 100%;
      position: absolute;
      background-color: transparent;
      border: 0;
      color: transparent;
      top: 0;
      @include ltr2(padding-left, 0);
      @include rtl2(padding-right, 0);
      opacity: 0;
      cursor: pointer;
    }
  }


  .added-imgs {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    img {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }

    .img-card {
      width: 106px;
      height: 84px;
      border-radius: 12px;
      overflow: hidden;
      flex-shrink: 0;
      box-shadow: 1px 2px 6px #00000017;


      .info {
        border-bottom-right-radius: 0.25em;
        border-bottom-left-radius: 0.25em;
        display: none;
      }
    }

    .img-card {
      position: relative;

      &.cover {
        .info {
          position: absolute;
          bottom: 0;
          width: 100%;
          height: 1.2rem;
          display: flex;
          font-size: 0.85rem;
          align-items: center;
          justify-content: center;
          background-color: rgba($color: $primary, $alpha: 0.64);
          color: #ffffff;
          text-align: center;
        }
      }

    }


  }
}

.total_images {

  border-radius: 17px;
  background: rgba(45, 49, 66, 0.10);
  backdrop-filter: blur(25px);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px 0px;
  width: 55px;
  gap: 5px;
  font-size: 14px;

  svg {
    width: 16px;
    height: 14px;
  }
}

.noListingImages {
  color: #DC2D2D;

  .total_images {
    background: #DC2D2D;
    color: #fff;

    svg {
      opacity: 0.7;
    }
  }
}



.edit,
.delete {
  background-color: rgb(255 255 255 / 80%);
  position: absolute;
  top: 0.3rem;
  right: 0.3rem;
  cursor: pointer;
  border-radius: 50%;
  width: 1.4rem;
  height: 1.4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 0px 10px #00000042;
  z-index: 2;

  .pi {
    color: $orangeColor;
    font-size: 0.85rem;
  }
}



.cover_label {
  position: absolute;
  bottom: 0px;
  left: 0px;
  width: 100%;
  background: rgba($color: $orangeColor, $alpha: 0.5);
  color: #fff;
  padding: 2px;
  z-index: 2;
  text-align: center;
  font-size: 12px;
  opacity: 0;
  transition: all 0.3s ease-out;
  pointer-events: none;
}

.image_icon {
  // width: 19%;
  // height: 82px;
  border-radius: 14px;
  background: rgba($color: $primary, $alpha: 0.05);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  // margin: 5px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  vertical-align: top;

  &::before {
    content: '';
    display: block;
    padding-top: 100%;
  }

  svg {
    width: 30px;
    height: 30px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate3d(-50%, -50%, 0);
    pointer-events: none;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0px;
    left: 0px;
  }

  input {
    font-size: 0;
    height: 100%;
    width: 100%;
    position: absolute;
    background-color: transparent;
    border: 0;
    color: transparent;
    top: 0;
    @include ltr2(padding-left, 0);
    @include rtl2(padding-right, 0);
    opacity: 0;
    cursor: pointer;
  }

  &:hover {
    .cover_label {
      opacity: 1;
    }
  }
}

.image_icon.cover_icon {
  // width: 35%;
  // height: 174px;

  grid-column: span 2;
  grid-row: span 2;

  .cover_label {
    background: $orangeColor;
    padding: 5px;
    font-size: 15px;
    opacity: 1;
  }

  svg {
    margin-top: -10%;
  }

  // @include rtl2(float, right);
  // @include ltr2(float, left);


}