import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, UntypedFormControl, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { FormInterestControlComponent } from 'src/app/modules/listing/components/form-interest-control/form-interest-control.component';
import { InnerListingService } from 'src/app/modules/listing/services/inner-listing.service';
import { SecondaryBtnComponent } from 'src/app/shared/components/secondary-btn/secondary-btn.component';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';

@Component({
  selector: 'app-form-listing-details-swapp-interest',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormInterestControlComponent, SecondaryBtnComponent, NtranslatePipe],
  templateUrl: './form-listing-details-swapp-interest.component.html',
  styleUrls: ['./form-listing-details-swapp-interest.component.scss']
})
export class FormListingDetailsSwappInterestComponent implements OnInit, OnDestroy {
  @Output() onSubmit = new EventEmitter();
  @Input() intersetedCategories;
  @Input() treadBy;

  form: FormGroup;
  watcher: Subscription;
  constructor(
    private fb: FormBuilder,
    public innerService: InnerListingService

  ) { }
  ngOnDestroy(): void {
    this.watcher.unsubscribe();
  }

  ngOnInit(): void {



    this.form = this.fb.group({
      treadBy: new FormControl(''),
      interestIds: new UntypedFormControl(null, Validators.required),
    });


    if (this.intersetedCategories) {
      this.form.patchValue({ interestIds: this.intersetedCategories, treadBy: this.treadBy });
    }

    this.watcher = this.innerService.fetch.subscribe(res => {
      if (!res) return;
      this.saveData();
    });
  }





  saveData() {
    this.onSubmit.emit({ intersetedCategories: this.form.value.interestIds, treadBy: this.form.value.treadBy });
  }

}
