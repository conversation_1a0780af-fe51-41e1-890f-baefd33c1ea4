<div class="row">
    <div class="col-md-6">
        <div class="white_box" [ngClass]="{ 'active' : selectedOption == 1}" (click)="selectedOption = 1">
            <div>
                <h2>I`m Open</h2>
                <p>Get offers from anybodywith anything!</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="white_box specific" [ngClass]="{ 'active' : selectedOption == 2}" (click)="selectedOption = 2">
            <div>
                <h2>Specific Categories</h2>
                <p>Get offers only from specific categories!</p>
            </div>
        </div>
    </div>
    <div class="submit col-md-6 offset-md-6" *ngIf="!innerService.onePageMode">
        <app-secondary-btn [btnText]="'Next' | translate" btnType="button" [bigBtn]="true"
            (click)="moveNext()"></app-secondary-btn>
    </div>
</div>