import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { RippleModule } from 'primeng/ripple';
import { DarkBtnComponent } from 'src/app/shared/components/dark-btn/dark-btn.component';
import { GrayBtnComponent } from 'src/app/shared/components/gray-btn/gray-btn.component';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';

interface confirmButtons {
  title: string;
  value: boolean;
}

@Component({
  selector: 'app-warning-message',
  standalone: true,
  imports: [CommonModule, RippleModule, ButtonModule, DarkBtnComponent, GrayBtnComponent, NtranslatePipe],
  templateUrl: './warning-message.component.html',
  styleUrls: ['./warning-message.component.scss']
})
export class WarningMessageComponent implements OnInit {

  message: string;
  description!: string;
  btns: any = [];

  constructor(private ref: DynamicDialogRef, public config: DynamicDialogConfig) { }

  ngOnInit(): void {

    this.message = this.config.data.message;
    this.description = this.config.data.description ?? '';
    if (this.config.data.buttons) {
      this.btns = this.config.data.buttons;
    }
  }

  close(value) {
    if (this.ref) {
      this.ref.close(value);
    }
  }

}
