<div *ngIf="ads$ | async as ads">

    <div *ngIf="ads.sponsorAdImages.length > 0" class="adsStrip"
        [ngClass]="{'stickyMode': isSticky() , 'hideSearch' : hideSearch}">
        <div *ngFor="let ad of ads.sponsorAdImages" [attr.class]="ad.name">

            <a [attr.href]="ads.redirectLink" target="_blank">
                <img [ngSrc]="ad.imageURL" [alt]="ads.sponsorName" priority width="412" height="86" />
            </a>
        </div>
    </div>

</div>