import { Injectable } from '@angular/core';
import { ListingFilters } from 'src/app/shared/models/listing.model';
import { BrowserService } from './browser.service';

@Injectable({
  providedIn: 'root'
})
export class FilterResultsService {

  filter: ListingFilters;

  constructor(private browser: BrowserService) { }

  generateQueryParams(params: any): string {
    if (!params || Object.keys(params).length === 0) {
      return '';
    }

    if (this.browser.isBrowser()) {

      const queryParams = Object.keys(params)
        .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(params[key]))
        .join('&');

      return '?' + queryParams;

    }
    return '';
  }



}
