@import "variables";
@import "mixins";
@import "../../../../shared/components/inputs/style/common_input.scss";


.toggle_list {

    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
        margin: 0px !important;
        font-size: 12px !important;
        text-align: start;
    }

    ::ng-deep {
        .p-selectbutton {
            border-radius: 36px !important;
        }

        .p-selectbutton svg {
            width: 20px;
            height: 20px;
            margin-inline-end: 10px;
            flex-shrink: 0;
        }

        .p-buttonset .p-button {
            border: none;
            border-radius: 36px !important;
            overflow: hidden;
            justify-content: center;
            flex: 1;
            padding: 7px 20px;
            font-size: 14px;

            svg {
                fill: $text-color;
            }
        }

        .p-selectbutton {
            display: flex;
            border-radius: 36px;
            border: 1px solid rgba(45, 49, 66, 0.05);
            padding: 5px;
            gap: clamp(10px, 3vw, 41px);
            justify-content: space-between;
            min-height: 50px;
        }

        .p-selectbutton .p-button.p-highlight {
            border-radius: 36px !important;
            background: $orangeColor;
            padding: 7px 20px;

            svg {
                fill: #fff;
            }
        }

        .select_element {
            display: flex;
        }

    }
}

.clear_btn {
    width: 30px;
    height: 30px;
    position: absolute;
    z-index: 2;
    top: 0px;
    left: 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #dc0000;
    color: #fff;
    border-bottom-right-radius: 4px;
    pointer-events: cursor;
}

.location_filter {
    &::ng-deep {
        .location-container {
            width: 100%;
            background: #fff;
            padding-inline: 24px;
        }
    }
}

.business-label {
    font-size: 0.8em;
    text-align: center;
    display: block;
    margin: 5px 0px 20px;
}

.category_selector {
    &::ng-deep {
        p-dropdown {
            display: block;
            width: 100%;
        }

        .p-dropdown {
            width: 100%;
            border: none;
            padding: 8px;
        }

        .p-dropdown .p-dropdown-label.p-placeholder {
            color: $text-color;
            font-size: clamp(14px, 4vw, 16px);
            padding-inline-start: 16px;
        }
    }
}

p-dropdown.required_input {
    border-radius: 50px;
}

.new_account {
    padding: 0px;

    .auth_form {


        .upload_photo {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 300px;
            height: 100px;
            @include borderRadius(8px);
            background-color: #f2edf7;
            border: 1px solid #f2edf7;
            overflow: hidden;
            cursor: pointer;
            margin: 20px auto 0px;

            input[type="file"] {
                display: none;
            }

            .camera_icon {
                width: 100%;
                max-width: clamp(20px, 11vw, 39px);
            }

            #uploadedImg {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            img[src=""] {
                display: none;
            }
        }


        .info_icon {
            width: 24px;
            height: 24px;
            display: inline-flex;
            background: #2d314217;
            border-radius: 50%;
            justify-content: center;
            align-items: center;
            margin-inline-end: 10px;


            svg {
                width: 12px;
                height: 12px;
                fill: rgba(45, 49, 66, 0.78);

            }

            &.right {
                background: rgba($color: $primary, $alpha: 0.16);
                color: var(--secondary-color);

                svg {
                    width: 16px;
                    height: 16px;
                }
            }

            &.fail {
                background: #DC2D2D29;
                color: #DC2D2D;

                svg {
                    width: 16px;
                    height: 16px;
                }
            }
        }

        .agree_to {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 12px;
            margin-bottom: clamp(20px, 11vw, 5vh);

            &::ng-deep {
                .p-checkbox {
                    &-box {
                        // width: 24px;
                        // height: 24px;
                        border-color: rgba(#2d3142, 0.3);
                        @include transition(0.3s);
                        border-radius: 50%;

                        &.p-focus {
                            box-shadow: none !important;
                        }

                        &.p-highlight {
                            background-color: var(--secondary-color);
                            border-color: var(--secondary-color);
                        }
                    }
                }
            }

            label {
                font-size: 14px;
                font-weight: 400;
                color: #2d3142;
                line-height: normal;
                display: flex;
                align-items: center;
                justify-content: flex-start;
                gap: 5px;

                .p-button {
                    padding: 0;
                    background-color: transparent !important;
                    box-shadow: none !important;
                    border: 0 !important;
                    font-size: 14px;
                    font-weight: 400;
                    color: var(--secondary-color);
                    @include transition(0.3s);

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }

        .using {
            font-size: 14px;
            font-weight: 400;
            color: #2d3142;
            text-align: center;
            margin: clamp(10px, 5vw, 36px) 0;
        }

        .agreeing {
            font-size: 14px;
            font-weight: 400;
            color: #2d314287;
            text-align: center;
            margin-bottom: clamp(20px, 2vw, 69px);
            align-items: center;
            justify-content: center;
            flex-direction: column;
            gap: 10px;

            .p-button {
                background-color: transparent !important;
                padding: 0;
                border: 0 !important;
                box-shadow: none !important;
                font-size: 14px;
                font-weight: 400;
                color: var(--secondary-color);
                @include transition(0.3s);

                span {
                    font-size: 14px;
                    font-weight: 400;
                    color: var(--secondary-color);
                }

                &:hover {
                    text-decoration: underline;
                }
            }
        }

        .have_account {
            h5 {
                font-size: 14px;
                font-weight: 400;
                color: #2d3142;
                text-align: center;
                margin-bottom: 22px;
            }
        }
    }
}


@include Large {


    .new_account {
        .auth_form {

            .agreeing {
                display: flex;
            }
        }
    }
}