import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { RippleModule } from 'primeng/ripple';
import { LazyloadDirective } from 'src/app/shared/directives/lazyload.directive';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';

@Component({
  selector: 'app-call-warning',
  standalone: true,
  imports: [CommonModule, RippleModule, ButtonModule, NtranslatePipe, LazyloadDirective],
  templateUrl: './call-warning.component.html',
  styleUrls: ['./call-warning.component.scss']
})
export class CallWarningComponent {

  phone: string;
  constructor(private ref: DynamicDialogRef, public config: DynamicDialogConfig) { }

  ngOnInit(): void {
    this.phone = this.config.data.phone;

  }

  close(value) {
    if (this.ref) {
      this.ref.close(value);
    }
  }

}
