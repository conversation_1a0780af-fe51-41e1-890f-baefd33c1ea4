import { ComponentFixture, TestBed } from '@angular/core/testing';

import { FormListingDetailsSwappOptionComponent } from './form-listing-details-swapp-option.component';

describe('FormListingDetailsSwappOptionComponent', () => {
  let component: FormListingDetailsSwappOptionComponent;
  let fixture: ComponentFixture<FormListingDetailsSwappOptionComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ FormListingDetailsSwappOptionComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(FormListingDetailsSwappOptionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
