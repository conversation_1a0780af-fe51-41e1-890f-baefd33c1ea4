<div class="auth_form_wrapper edit_phone">
    <div class="row">
        <div class="enter_phoneNum">
            <div class="row">
                <div class="col-md-8 col-lg-10 col-xl-8 mx-auto">
                    <h3>{{ 'Enter Your Phone Number' | translate}}</h3>
                    <form [formGroup]="editPhoneForm" autocomplete="off" novalidate
                        (ngSubmit)="editPhone(editPhoneForm.value)">
                        <div class="inputs_holder">
                            <div class="row">
                                <div class="col-md-12 mb-4">
                                    <app-phone-input formControlName="phone"></app-phone-input>



                                </div>
                            </div>
                        </div>

                        <app-secondary-btn [btnText]="'Next' | translate" btnType="submit" [bigBtn]="true"
                            [btnDisabled]="validateControl('phone')"></app-secondary-btn>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>