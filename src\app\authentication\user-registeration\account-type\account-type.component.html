<div class="auth_form_wrapper account_type">
    <div class="auth_form">
        <div class="back_logo d-flex d-lg-none">
            <Button pButton pRipple class="back_icon" [routerLink]="['/']">
                <img src="assets/img/backIcon.svg" alt="" />
            </Button>
            <Button pButton pRipple class="app_logo" [routerLink]="'/'">
                <img src="assets/img/4swapp-logo.svg?v=1.1.2" alt="" />
            </Button>
        </div>
        <div class="row">
            <div class="col-md-8 col-lg-10 col-xl-8 mx-auto">
                <form [formGroup]="registerForm" autocomplete="off" novalidate>
                    <div>
                        <h3>{{ 'accounttype' | translate}}</h3>
                        <div class="inputs_holder mb-4">
                            <div class="row" *ngFor="let account of accountTypeOptions">
                                <Button pButton (click)="setAccountType(account.value)"
                                    [class.active]="registerForm.controls['accounttype'].value === account.value">

                                    <span>{{ account.label | translate }}</span>

                                    <img [src]="account.img" [alt]="account.label + '_icon'" />
                                </Button>
                            </div>
                        </div>
                        <div class="agree_to">
                            <p-checkbox formControlName="agree" value="agree" inputId="agree"
                                [binary]="true"></p-checkbox>
                            <label for="agree">
                                {{ 'I Agree to' | translate}}
                                <a pButton pRipple (click)="openTermsAndConditions()"
                                    [label]="'Terms_and_Conditions' | translate">
                                </a>
                            </label>

                        </div>
                    </div>
                    <app-secondary-btn [btnText]="'Next' | translate" btnType="button" [bigBtn]="true"
                        (click)="submit()" [btnDisabled]="checkFormIsInValid()"></app-secondary-btn>

                </form>
            </div>
        </div>
    </div>
</div>

<p-dialog [modal]="true" [(visible)]="showTermsAndConditions" [style]="{ width: '25rem' , height: '35rem' }">
    <app-fetching-page-data pageId='terms-of-use' [isPopup]="true" />
</p-dialog>