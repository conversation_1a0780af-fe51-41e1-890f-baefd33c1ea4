import { AuthConfig } from 'angular-oauth2-oidc';

import { environment } from 'src/environments/environment';

// export const authConfig: PassedInitialConfig = {
//   config: {
//     renewTimeBeforeTokenExpiresInSeconds: 10,
//     disableIatOffsetValidation: true,
//     authority: environment.authAPI,
//     redirectUrl: environment.clintURL,
//     postLogoutRedirectUri: environment.clintURL,
//     renewUserInfoAfterTokenRenew: true,
//     triggerAuthorizationResultEvent: true,
//     postLoginRoute: '/home',
//     clientId: 'swapp_users',
//     scope: 'openid profile userapi notifications offline_access IdentityServerApi',
//     responseType: 'id_token token',
//     silentRenew: true,
//     useRefreshToken: true,
//     silentRenewUrl: `${environment.clintURL}/silent-renew.html`,
//     startCheckSession: true,
//     ignoreNonceAfterRefresh: true,
//     logLevel: environment.production ? LogLevel.None : LogLevel.Debug,
//     maxIdTokenIatOffsetAllowedInSeconds: -60,
//     disableIdTokenValidation: false,
//     secureRoutes: [environment.authAPI, environment.userURL, environment.notificationUrl],
//   }
// };




export const authCodeFlowConfig: AuthConfig = {
  issuer: environment.authAPI,
  redirectUri: environment.clintURL,
  redirectUriAsPostLogoutRedirectUriFallback: true,
  clientId: 'swapp_users',
  silentRefreshRedirectUri: `${environment.clintURL}/silent-renew.html`,
  scope: 'openid profile userapi notifications offline_access IdentityServerApi',
  responseType: 'id_token token',
  showDebugInformation: false,
  useSilentRefresh: true,
  sessionChecksEnabled: true,
  silentRefreshIFrameName: 'swapp-silent-refresh-iframe',

};
