<div class="listing_instructions_box">
    <h2>
        <span>{{ 'YourSafetyMatters' | translate }}</span>
        <ng-container *ngIf="!viewMode">
            <div class="report_box">
                <span class="report_btn" VerifiedClick (VerifiedClick)="openReport()">
                    <svg data-id="report_icon" viewBox="0 0 14 13" fill="none">
                        <path
                            d="M13.2007 8.98196H12.5476V5.13255C12.5476 3.77131 11.9972 2.46583 11.0175 1.50329C10.0377 0.540749 8.70891 0 7.32334 0C5.93777 0 4.60895 0.540749 3.62921 1.50329C2.64946 2.46583 2.09904 3.77131 2.09904 5.13255V8.98196H1.44601C1.27281 8.98196 1.10671 9.04955 0.984239 9.16987C0.861771 9.29019 0.792969 9.45338 0.792969 9.62353V12.1898C0.792969 12.36 0.861771 12.5231 0.984239 12.6435C1.10671 12.7638 1.27281 12.8314 1.44601 12.8314H13.2007C13.3739 12.8314 13.54 12.7638 13.6624 12.6435C13.7849 12.5231 13.8537 12.36 13.8537 12.1898V9.62353C13.8537 9.45338 13.7849 9.29019 13.6624 9.16987C13.54 9.04955 13.3739 8.98196 13.2007 8.98196ZM3.40512 5.13255C3.40512 4.11162 3.81793 3.13251 4.55274 2.4106C5.28755 1.6887 6.28417 1.28314 7.32334 1.28314C8.36252 1.28314 9.35914 1.6887 10.0939 2.4106C10.8288 3.13251 11.2416 4.11162 11.2416 5.13255V8.98196H3.40512V5.13255ZM12.5476 11.5482H2.09904V10.2651H12.5476V11.5482Z"
                            fill="#f99c10" />
                        <path
                            d="M4.57812 5.13268H5.8842C5.8842 4.79237 6.0218 4.466 6.26674 4.22537C6.51168 3.98473 6.84388 3.84954 7.19027 3.84954V2.56641C6.49749 2.56641 5.83308 2.83678 5.34321 3.31805C4.85333 3.79932 4.57813 4.45206 4.57812 5.13268Z"
                            fill="#f99c10" />
                    </svg>

                    <b>{{ 'Report Ad' | translate }}</b>
                </span>
            </div>
        </ng-container>
    </h2>
    <ul class="instructions_box">
        <li>{{ 'Never transfer money in advance' | translate }}</li>
        <li>{{ 'Meet the seller at a public place' | translate }}</li>
        <li>{{ 'Don`t proceed if something seems wrong' | translate }}</li>
    </ul>
</div>


<p-dialog header="" [closable]="true" [modal]="true" [(visible)]="reportVisible" [style]="{width: '50vw'}">
    <form [formGroup]="form">
        <div class="modelTop reasons_box">
            <h3>{{ 'Report Listing' | translate}}</h3>
            <ng-container *ngIf="reasons$ | async as reasons">
                <div *ngFor="let reason of reasons" class="reason_item">
                    <p-radioButton [inputId]="reason.id.toString()" formControlName="report"
                        [value]="reason.id"></p-radioButton>
                    <label [for]="reason.id">{{ reason.name | translate }}</label>
                </div>
            </ng-container>

        </div>
        <div class="model_actions">
            <app-dark-btn [btnDisabled]="!form.valid" [btnText]="'Ok' | translate" btnType="button" [bigBtn]="true"
                (click)="report()"></app-dark-btn>
        </div>
    </form>
</p-dialog>