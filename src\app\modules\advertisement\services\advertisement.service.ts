import { Injectable } from '@angular/core';
import { ProfileModel } from '@src/app/shared/models/profile.model';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AdvertisementService {

  sellerInfo: ProfileModel;

  private formStep$: BehaviorSubject<number> = new BehaviorSubject<number>(1);
  formStep = this.formStep$.asObservable();

  constructor() { }

  setStep(value) {
    this.formStep$.next(value);
  }
}
