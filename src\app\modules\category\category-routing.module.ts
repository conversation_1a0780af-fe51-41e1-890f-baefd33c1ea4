import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MainLayoutComponent } from 'src/app/modules/layouts/main-layout/main-layout.component';
import { categoryResolver } from './category.resolver';
import { MainCategoryComponent } from './pages/main-category/main-category.component';

const routes: Routes = [
  {
    path: '',
    component: MainLayoutComponent,
    children: [
      {
        path: ':id',
        component: MainCategoryComponent,
        resolve: {
          categoryResolver: categoryResolver
        }
      },

      {
        path: ':id/:sub',
        component: MainCategoryComponent,
        resolve: {
          categoryResolver: categoryResolver
        }
      },

      {
        path: ':id/:sub/:name',
        component: MainCategoryComponent,
        resolve: {
          categoryResolver: categoryResolver
        }
      },
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CategoryRoutingModule { }
