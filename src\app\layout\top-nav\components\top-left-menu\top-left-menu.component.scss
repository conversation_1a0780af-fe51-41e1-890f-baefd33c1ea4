@import "variables";
@import "mixins";

h2 {
  padding: 20px 10px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 15px;

  .pi {
    font-size: 1rem;
  }
}

.header_left {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  gap: 16px;

  .app_logo {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 109px;

    img {
      width: 100%;
    }
  }
}

.burger_icon_btn {
  padding: 0px !important;
  background-color: transparent !important;
  border: 0 !important;
  box-shadow: none !important;
  flex-shrink: 0;


  img {
    max-width: 100%;
  }

  svg {
    width: 23px;
    height: 20px;
  }
}



@include Large {
  h2 {
    display: none;
  }

  .header_left {
    .app_logo {
      max-width: 109px;

    }
  }
}