import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';
import { RadioButtonModule } from 'primeng/radiobutton';
import { Observable, Subscription, map } from 'rxjs';
import { BrowserService } from 'src/app/modules/core/service/browser.service';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { LocationForm } from 'src/app/modules/listing/models/location.form.model';
import { InnerListingService } from 'src/app/modules/listing/services/inner-listing.service';
import { SecondaryBtnComponent } from 'src/app/shared/components/secondary-btn/secondary-btn.component';
import { Regions } from 'src/app/shared/models/location.model';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { LocationService } from 'src/app/shared/services/location.service';

@Component({
  selector: 'app-form-listing-details-location',
  standalone: true,
  imports: [CommonModule, DropdownModule, ReactiveFormsModule, NtranslatePipe, RadioButtonModule, SecondaryBtnComponent],
  templateUrl: './form-listing-details-location.component.html',
  styleUrls: ['./form-listing-details-location.component.scss']
})
export class FormListingDetailsLocationComponent implements OnInit, OnDestroy {
  @ViewChild('target') targetHost: ElementRef<HTMLInputElement>;
  areas$: Observable<Regions[]>;
  @Output() onSubmit = new EventEmitter();
  @Input() locationForm: LocationForm;
  form: FormGroup;
  watcher: Subscription;

  locationsList: Regions[];
  locations: Regions[];

  mainAreas = {};

  reachtypes = [
    { id: 1, name: 'Phone & Messages' },
    { id: 2, name: 'Phone Only' },
    { id: 3, name: 'Messages Only' },
  ];

  cityOfIds = {};
  locationOfIds = {};

  constructor(
    private locationService: LocationService,
    public innerService: InnerListingService,
    private translationService: TranslationService,
    private cd: ChangeDetectorRef,
    private browser: BrowserService
  ) { }
  ngOnDestroy(): void {
    this.watcher.unsubscribe();
  }

  changeEvent(event) {

  }

  ngOnInit(): void {
    this.form = new FormGroup({
      biglocation: new FormControl('', [Validators.required]),
      location: new FormControl('', [Validators.required]),
    });

    this.areas$ = this.locationService.getAllLocations('').pipe(map(res => {
      this.locationsList = res.data.map(item => {
        const oo = { ...item };
        oo.name = this.translationService.instant(item.name);
        oo.areaIds = item.areas.reduce((acc, obj) => {
          acc[obj.id] = obj;
          return acc;
        }, {});
        return oo;
      });

      this.cityOfIds = res.data.reduce((acc, obj) => {
        acc[obj.id] = obj;
        return acc;
      }, {});




      if (this.locationForm && this.locationForm.location) {
        const city = this.locationsList.find(item => item.areaIds![this.locationForm.location]);

        if (city) {
          this.displayAreas(city.id);

          this.form.patchValue({
            biglocation: city.id,
            location: this.locationForm.location,
          });
        }



      }

      return res.data;
    }));



    this.watcher = this.innerService.fetch.subscribe(res => {
      if (!res) return;
      this.saveData();
    });
  }

  hasError(controlName: string, errorName: string) {
    return this.form.controls[controlName].hasError(errorName);
  }

  onChooseCity(event) {

    this.displayAreas(event.value);
  }

  displayAreas(id) {

    this.form.patchValue({
      'location': ''
    });

    if (this.cityOfIds[id]) {
      this.locations = this.cityOfIds[id].areas.map(item => {
        const oo = { ...item };
        oo.name = this.translationService.instant(item.name);

        return oo;
      });

      this.locationOfIds = this.cityOfIds[id].areas.reduce((acc, obj) => {
        acc[obj.id] = obj;
        return acc;
      }, {});
    }
    this.cd.detectChanges();
  }


  saveData() {
    if (!this.form.valid) {
      this.browser.scrollTo({
        behavior: 'smooth',
        top: this.targetHost.nativeElement.offsetTop - 100,
      });
      this.innerService.hasLocationError = true;
      return;
    }


    this.onSubmit.emit({

      location: {
        id: this.locationOfIds[this.form.value.location].id,
        name: this.locationOfIds[this.form.value.location].name
      }
    });
  }

}
