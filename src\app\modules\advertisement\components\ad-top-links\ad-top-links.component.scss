@import "mixins";

.top_details {
    padding: 0px 15px 15px;

    >div {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
}


.top_btns {
    display: flex;
    gap: 10px;

    svg {
        width: 23px;
        height: 23px;
        color: #d6d6d6;
    }

    span {
        cursor: pointer;
    }

    app-love-button {
        &::ng-deep {
            .like_icon {
                svg {
                    width: 23px;
                    height: 23px;
                }
            }
        }

    }
}



.share_list {
    display: flex;
    justify-content: space-between;
    padding: 30px 0px;

    img {
        cursor: pointer;
    }
}

.copy_link {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 15px;
    border-radius: 60px;
    border-radius: 46px;
    background: rgba(45, 49, 66, 0.05);

    >div {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 62%;
        display: inline-block;
        vertical-align: middle;
    }

    app-dark-btn {
        flex-shrink: 0;
    }
}

.share_box {
    h2 {
        font-size: 22px;
        padding: 20px 0px 0px;
    }
}


@include Large {
    .top_btns {
        gap: 20px;
    }

    // .top_details{
    //     background: linear-gradient(
    //         180deg,
    //         #f7f7fb 0%,
    //         rgba(45, 49, 66, 0.06) 30%,
    //         rgba(#2d3142, 0.06) 100%
    //       );
    // }
}