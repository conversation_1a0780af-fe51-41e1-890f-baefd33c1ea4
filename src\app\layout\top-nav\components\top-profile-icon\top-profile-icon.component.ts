import { CommonModule } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';

import { Router, RouterModule } from '@angular/router';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Store } from '@ngrx/store';
import { AuthService } from '@services/auth.service';
import { SidebarModule } from 'primeng/sidebar';
import { Subscription } from 'rxjs';
import { User } from 'src/app/authentication/models/dto/user.dto.model';
import { SideNavComponent } from 'src/app/layout/side-nav/side-nav.component';
import { SideMenuService } from 'src/app/layout/top-nav/service/side-menu.service';
import { SellerListingImageComponent } from 'src/app/modules/advertisement/components/seller-listing-image/seller-listing-image.component';
import { PrimaryBtnComponent } from 'src/app/shared/components/primary-btn/primary-btn.component';
import { VerifiedClickDirective } from 'src/app/shared/directives/isverifiedclick.directive';
import { CategoryMenuDTO } from "src/app/shared/models/lookup.model";
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { RoundPipe } from 'src/app/shared/pipes/round.pipe';
import { AppConstantModel, AppConstants } from 'src/app/shared/services/app.common';
import { CommonService } from 'src/app/shared/services/common.service';
import { getCategories, getLang, getUser } from 'src/app/store/app/selectors/app.selector';
@UntilDestroy({ checkProperties: true })
@Component({
  selector: 'app-top-profile-icon',
  standalone: true,
  imports: [CommonModule, SidebarModule, RouterModule, PrimaryBtnComponent, NtranslatePipe, VerifiedClickDirective, SideNavComponent, SellerListingImageComponent, RoundPipe],
  templateUrl: './top-profile-icon.component.html',
  styleUrls: ['./top-profile-icon.component.scss']
})
export class TopProfileIconComponent implements OnInit, OnDestroy {

  blogUrl: string = '';
  isUserAuthenticated: boolean = false;
  userImage: string = '';
  userName: string = '';
  user!: User;
  isOpen: boolean = false;
  menuId: number;
  currentConfig: AppConstantModel;
  currentId = 0;
  subscription: Subscription;
  displayDesktopMenu: boolean = false;

  categoriesItems: CategoryMenuDTO[];



  constructor(
    private _authService: AuthService,
    private store: Store,
    public menuService: SideMenuService,
    private router: Router,
    private commonService: CommonService,

  ) {
    this.menuId = Math.floor(Math.random() * 50);
    this.blogUrl = this.commonService.getBlogUrl();

  }

  goToCreateListing() {
    this.router.navigate(['/listing/create']);
    this.commonService.pushDataLayer({
      'event': 'add_ad_button_clicked',
      'session_status': 'logged',
    });

  }

  sendEvent(e) {
    this.commonService.pushDataLayer({
      'event': 'add_ad_button_clicked',
      'session_status': 'unlogged',
    });
  }

  ngOnInit(): void {

    this.store.select(getUser).pipe(untilDestroyed(this)).subscribe(res => {
      if (res) {
        this.user = res;
      }
    });

    this.store.select(getLang).pipe(untilDestroyed(this)).subscribe(res => {
      if (res) {
        this.currentConfig = AppConstants[res];
      }

    });

    this.store.select(getCategories).pipe(untilDestroyed(this)).subscribe(res => {
      if (res) {
        this.categoriesItems = res.menu;
      }

    });





  }

  logout() {
    this._authService.logout();
  }

  onVisibleChange(event: boolean) {
    this.menuService.set(event);
  }

  toggle() {
    this.menuService.toggle();
  }

  ngOnDestroy() {
    this.subscription?.unsubscribe();
  }

}
