import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { BrowserService } from 'src/app/modules/core/service/browser.service';
import { CommonService } from 'src/app/shared/services/common.service';
import { LanguageService } from 'src/app/shared/services/language.service';

@Component({
  selector: 'app-shorten',
  templateUrl: './shorten.component.html',
  styleUrls: ['./shorten.component.scss']
})
export class ShortenComponent {

  constructor(private activated: ActivatedRoute, private service: CommonService, private ls: LanguageService, private browser: BrowserService) {
    this.activated.params.subscribe(params => {
      if (params['key']) {
        this.service.redirect(params['key']).subscribe(res => {
          if (res.data) {
            this.browser.href(res.data);
          }
        });
      }
    });

  }

}
