@import "mixins";
@import "variables";

.offers_info {
    background: $orangeColor;
    color: #fff;
    padding: 13px 18px;
    border-radius: 30px;
    cursor: pointer;
}

.listing_info {
    background: #fff;
    padding: 14px 24px;
    border-radius: 12px;
    min-height: 90px;
    font-size: 14px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    b {
        font-size: 20px;
    }
}

.listing_btns {
    display: flex;
    justify-content: space-between;
    gap: 24px;
    padding: 20px 0px;

    &>* {
        flex: 1;
        border-radius: 30px;
        height: 50px;
        font-size: 1rem;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
    }

    svg {
        width: 15px;
        height: 15px;
    }
}



.rejected_list {
    background: #fff;
    padding: 30px 24px;
    border-radius: 12px;
    min-height: 90px;
    font-size: 18px;

    ul {
        list-style: none;
        padding: 0px;
        margin: 0px;

        li {
            padding: 10px 0px;
            font-size: 14px;
        }
    }

    svg {
        width: 25px;
        height: 25px;
        margin-inline-end: 11px;
    }

    h2 {
        font-size: 16px;
        font-weight: 400;
    }


}


.floated_listing_info {
    position: fixed;
    z-index: 10;
    bottom: 0px;
    left: 0px;
    width: 100%;

    &::ng-deep {
        .offers_box {
            height: 60px;
            overflow: hidden;
            box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);

        }
    }
}

.floated_listing_btns {
    position: fixed;
    z-index: 10;
    bottom: 60px;
    left: 0px;
    width: 100%;

    button {
        flex: 1;
        border-radius: 50%;
        height: 50px;
        width: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #fff;
        margin: 10px;
        box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);

        svg {
            width: 20px;
            height: 20px;
        }
    }
}