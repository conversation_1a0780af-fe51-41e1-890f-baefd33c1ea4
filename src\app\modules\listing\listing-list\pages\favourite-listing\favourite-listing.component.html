@defer () {
<div class="container">
    <h1>{{ "Favourites" | translate }}</h1>
    <div>
        <div class="items_list" *ngIf="listings$ | async as listings">
            <h2 class="list_title"><b>{{ listings.length }}</b> {{ "MyListing" | translate }}</h2>
            <div class="list_content">
                <app-item-card-row (onRemoveFavourite)="getListing()" *ngFor="let item of listings" [itemData]="item"
                    [mode]="'favourite'">
                </app-item-card-row>
                <app-no-result [type]="2" *ngIf="pagination.totalItems == 0"></app-no-result>

            </div>
        </div>
        <div>
            <p-paginator *ngIf="pagination.totalPages! > 1" (onPageChange)="pageChanged($event)" [first]="0"
                [rows]="pagination.pageSize" [totalRecords]="pagination.totalItems" [pageLinkSize]="3"
                [showFirstLastIcon]="false"></p-paginator>
        </div>

    </div>
</div>
}

@placeholder {
<div></div>
}

@loading {
<div></div>
}