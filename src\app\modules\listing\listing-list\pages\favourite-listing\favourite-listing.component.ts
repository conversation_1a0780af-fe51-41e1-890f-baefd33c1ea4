import { Component, OnInit } from '@angular/core';
import { MetaService } from '@src/app/modules/core/service/meta.service';
import { TranslationService } from '@src/app/modules/core/service/translation.service';
import { Observable, map, of, switchMap } from 'rxjs';
import { Pagination } from 'src/app/shared/models/base.response.model';
import { ListingView } from 'src/app/shared/models/listing.model';
import { FavouriteBaordService } from 'src/app/shared/services/favouritebaord.service';
import { NgIf, NgFor, AsyncPipe } from '@angular/common';
import { ItemCardRowComponent } from '../../../../../shared/components/item-card-row/item-card-row.component';
import { NoResultComponent } from '../../../../../shared/components/no-result/no-result.component';
import { PaginatorModule } from 'primeng/paginator';
import { NtranslatePipe } from '../../../../../shared/pipes/ntranslate.pipe';

@Component({
    selector: 'app-favourite-listing',
    templateUrl: './favourite-listing.component.html',
    styleUrls: ['./favourite-listing.component.scss'],
    standalone: true,
    imports: [NgIf, NgFor, ItemCardRowComponent, NoResultComponent, PaginatorModule, AsyncPipe, NtranslatePipe]
})
export class FavouriteListingComponent implements OnInit {
  listings$: Observable<ListingView[]>;
  pagination: Pagination = {
    currentPage: 1,
    pageSize: 12,
    totalItems: 0,
    totalPages: 1,
  };
  userFavourites: number[];


  constructor(
    private _userFavourite: FavouriteBaordService,
    private metaService: MetaService,
    private translateService: TranslationService

  ) { }

  ngOnInit(): void {
    this.metaService.set({
      title: this.translateService.instant('Favorites'),
    });
    this.getListing();
  }

  pageChanged(event: any): void {
    this.pagination.currentPage = event.page + 1;
    this.pagination.pageSize = event.rows;
    this.pagination.totalPages = event.pageCount;
    this.getListing();
  }

  getListing() {

    this.listings$ = this._userFavourite.getuserbaords().pipe(switchMap(res => {
      if (res.data.length > 0) {

        return this._userFavourite.getbaordListing(res.data[0].id).pipe(map(res => res), map(res => {
          // this.pagination.totalPages = res.totalPages;

          this.pagination.totalItems = res.data.length;


          return res;
        }))
      }
      return of({ data: [] });
    }), map(res => res.data));


    //console.log(this.userFavourites, 'ppp');
  }


}
