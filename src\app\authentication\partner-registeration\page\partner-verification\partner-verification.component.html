<div class="auth_form_wrapper verification">
    <div class="verify_num">
        <div class="row">
            <div>
                <h3>{{ 'Enter Verification Number' | translate}}</h3>
                <ngx-otp-input #ngxotp [options]="otpInputConfig" (otpChange)="handeOtpChange($event)"
                    (otpComplete)="handleFillEvent($event)"></ngx-otp-input>

                <div class="loader_place" [ngClass]="{'active' : loading}">
                    <div class="loaderIcon"></div>
                </div>

                <div *ngIf="wrongOTP" class="otp_wrong">
                    <div class="icon">
                        <i class="pi pi-times"></i>
                    </div>
                    <div class="txt">{{ 'Wrong Code, Please Re-enter Code' | translate}}</div>
                </div>

                <div class="row">
                    <div>
                        <div class="resend_code">
                            <p-button class="txt" [label]="'Resend Code' | translate"
                                [disabled]="transform(counter) != '00:00'" (click)="resendClicked()"
                                severity="secondary"></p-button>

                            <div class="counter" *ngIf="counter != 0">{{ transform(counter) }}</div>
                        </div>

                        <div class="phone_number" *ngIf="phoneNumber">
                            <div>
                                {{phoneNumber}} <button (click)="showDialog()">
                                    <svg data-id="edit_icon" viewBox="0 0 18 18" fill="none">
                                        <path
                                            d="M16.8653 1.13829C16.5044 0.777415 16.076 0.491146 15.6046 0.295836C15.1331 0.100526 14.6277 0 14.1174 0C13.607 0 13.1017 0.100526 12.6302 0.295836C12.1587 0.491146 11.7303 0.777415 11.3695 1.13829L0.379457 12.1265C0.259136 12.2467 0.163692 12.3895 0.0985813 12.5466C0.0334711 12.7037 -2.77112e-05 12.8721 1.84413e-07 13.0422V16.7049C-9.06118e-05 16.875 0.0333473 17.0435 0.0984024 17.2006C0.163458 17.3578 0.258854 17.5006 0.379138 17.6209C0.499421 17.7412 0.642233 17.8366 0.799409 17.9016C0.956584 17.9667 1.12504 18.0001 1.29515 18H4.9594C5.12949 18 5.29791 17.9665 5.45503 17.9014C5.61216 17.8363 5.75492 17.7409 5.87514 17.6206L16.8666 6.63236C17.5928 5.90241 18.0003 4.91462 18 3.88504C17.9997 2.85547 17.5918 1.86788 16.8653 1.13829ZM15.0326 2.96968C15.2749 3.21277 15.411 3.5419 15.4113 3.8851C15.4115 4.22829 15.2758 4.55761 15.0339 4.80104L14.118 5.71687L12.2856 3.88518L13.2011 2.96968C13.3214 2.8494 13.4641 2.75399 13.6212 2.6889C13.7784 2.6238 13.9468 2.5903 14.1168 2.5903C14.2869 2.5903 14.4553 2.6238 14.6124 2.6889C14.7695 2.75399 14.9124 2.8494 15.0326 2.96968ZM4.42321 15.4098H2.59058V13.5785L10.4538 5.71639L12.2862 7.54808L4.42321 15.4098Z"
                                            fill="currentColor" />
                                    </svg>

                                </button>
                            </div>
                        </div>

                        <div class="forceresend">
                            <app-secondary-btn [btnText]="'Verify' | translate" btnType="button" [midBtn]="true"
                                (click)="tryVerify()" [btnDisabled]="!otpFilled"
                                [loading]="tryMode && loading"></app-secondary-btn>
                        </div>



                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Edit Phone Number -->
<app-edit-phone-modal redirectTo="/authentication/register-partner/edit-phone-number"></app-edit-phone-modal>