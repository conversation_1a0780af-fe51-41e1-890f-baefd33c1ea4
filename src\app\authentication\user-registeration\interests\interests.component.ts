import { PlatformLocation } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ProgressHeadComponent } from '../../../shared/components/progress-head/progress-head.component';
import { CustomizeInterestsComponent } from '../../../shared/components/customize-interests/customize-interests.component';
import { NtranslatePipe } from '../../../shared/pipes/ntranslate.pipe';

@Component({
    selector: 'app-interests',
    templateUrl: './interests.component.html',
    styleUrls: ['./interests.component.scss'],
    standalone: true,
    imports: [
        ProgressHeadComponent,
        CustomizeInterestsComponent,
        NtranslatePipe,
    ],
})
export class InterestsComponent implements OnInit {
  constructor(
    private location: PlatformLocation,
    private router: Router
  ) {
  }

  ngOnInit(): void {

    // this.router.events.subscribe(event => {
    //   if (event instanceof NavigationStart) {
    //     if (event.navigationTrigger === 'popstate') {
    //       this.location.go('/authentication/register/interest');
    //     }
    //   }
    // });
  }
}
