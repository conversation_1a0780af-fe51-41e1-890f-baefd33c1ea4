import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MetaService } from '@src/app/modules/core/service/meta.service';
import { SecondaryBtnComponent } from '../../shared/components/secondary-btn/secondary-btn.component';
import { NtranslatePipe } from '../../shared/pipes/ntranslate.pipe';

@Component({
  selector: 'app-notfound',
  templateUrl: './notfound.component.html',
  styleUrls: ['./notfound.component.scss'],
  standalone: true,
  imports: [SecondaryBtnComponent, NtranslatePipe]
})
export class NotfoundComponent implements OnInit {

  constructor(
    private router: Router,
    private metaService: MetaService,
  ) { }

  ngOnInit(): void {
    this.metaService.set({});
    this.metaService.addNoIndex();
  }

  submit() {
    this.router.navigate(['/home']);
  }

}
