import { HttpRequest } from '@angular/common/http';
type CachePattern = {
    urlPattern: RegExp;
};
// Object of cache patterns
const CACHE_PATTERNS: { [method: string]: CachePattern[] } = {
    GET: [
        { urlPattern: /^https?:\/\/[^\/]+\/todos$/ },
        // Add more GET patterns here
    ],
    PUT: [
        // { urlPattern: /^\/api\/account\/todos$/ },
        // { urlPattern: /^\/api\/account\/todos\/\d+\/\d+$/ }, Paginated Services
        // Add more PUT patterns here
    ],
    // Add more HTTP methods and their patterns as needed
};
export const DELETE_PATTERNS = {
    '/api/add/todos': true,
};
// Array of URLs that should never be cached
const NEVER_CACHE_PATTERNS: RegExp[] = [
    /^\/api\/account\/generateOTP$/,
    // Add more patterns as needed
];
export function canCacheRequest(req: HttpRequest<any>): boolean {
    const urlWithoutHost = req.url.split(window.origin)[1];

    if (NEVER_CACHE_PATTERNS.some((pattern) => pattern.test(urlWithoutHost))) {
        return false;
    }

    const methodPatterns = CACHE_PATTERNS[req.method];
    if (methodPatterns) {
        const matchedPattern = methodPatterns.find((pattern) =>
            pattern.urlPattern.test(urlWithoutHost)
        );
        if (matchedPattern) {
            //return true;
        }
    }

    return false;
}