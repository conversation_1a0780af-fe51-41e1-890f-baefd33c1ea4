<div class="category_btn" [ngClass]="{'createMode' : !editMode}" (click)="!editMode ? backToCategory() : null">
    <span class="pi pi-chevron-left" *ngIf="!editMode">

    </span>
      <div class="category_info">
          <img [src]="mainCategory.icon"/>
          <div>
              <h2>{{ mainCategory.label }}</h2>
              <h3 *ngIf="subCategory">{{ subCategory.label }}</h3>
          </div>
      </div>
</div>