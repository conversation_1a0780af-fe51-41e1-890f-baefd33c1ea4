import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { AppcenterService } from '@src/app/shared/services/appcenter.service';
import { environment } from '@src/environments/environment';
import { BehaviorSubject } from 'rxjs';
import { debounceTime, distinctUntilChanged, map } from 'rxjs/operators';

interface AccumulatorPayload {
    culture: string;
    keys: string[];
}

interface ApiTranslation {
    key: string;
    value: string;
}

interface TranslationMap {
    [key: string]: string;
}



@Injectable({
    providedIn: 'root'
})
export class LangAccumulatorService {
    private accumulatedKeys = new BehaviorSubject<Set<string>>(new Set());

    constructor(
        private http: HttpClient,
        private lang: AppcenterService
    ) { }

    public keys$ = this.accumulatedKeys.pipe(
        debounceTime(300),
        distinctUntilChanged()
    );

    addKey(key: string) {
        const currentKeys = this.accumulatedKeys.value;
        currentKeys.add(key);
        this.accumulatedKeys.next(new Set(currentKeys));
    }

    getValues(): Record<string, any> {
        return this.accumulatedKeys.value;
    }

    getKeys(): string[] {
        return Array.from(this.accumulatedKeys.value);
    }

    clear() {
        this.accumulatedKeys.next(new Set());
    }

    private transformResponse(response: ApiTranslation[]): TranslationMap {
        return response.reduce((acc, item) => {
            acc[item.key.toLowerCase()] = item.value;
            return acc;
        }, {} as TranslationMap);
    }

    loadTranslations() {

        const payload: AccumulatorPayload = {
            culture: this.lang.lang,
            keys: this.getKeys()
        };

        return this.http.post<ApiTranslation[]>(environment.adminURL + '/api/Language/getbykeys', payload).pipe(
            map((response: ApiTranslation[]) => this.transformResponse(response))
        )

        // const formData = new FormData();
        // keys.forEach(key => formData.append('keys', key));
        // this.http.post(environment.adminURL + '/api/language/i18n?lang=', '', {
        //     params: { keys: keys.join(',') }
        // }).subscribe((translations) => {

        // }
    }
}
