import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { InterestsComponent } from './interests/interests.component';
import { NewAccountComponent } from './new-account/new-account.component';
import { PersonalInfoComponent } from './personal-info/personal-info.component';
import { SuccessComponent } from './success/success.component';
import { UserRegisterationRoutingModule } from './user-registeration-routing.module';
import { UserRegisterationComponent } from './user-registeration.component';

import { NgxOtpInputComponent } from 'ngx-otp-input';
import { PrimengModuleModule } from 'src/app/modules/shared-modules/primeng-module.module';
import { ValidatorMessageStatusComponent } from 'src/app/shared/components/validator-message/validator-message-status/validator-message-status.component';
import { ValidatorMessageComponent } from 'src/app/shared/components/validator-message/validator-message.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { EditPhoneNumberComponent } from './edit-phone-number/edit-phone-number.component';

@NgModule({
    imports: [
        CommonModule,
        UserRegisterationRoutingModule,
        SharedModule,
        PrimengModuleModule,
        FormsModule,
        ReactiveFormsModule,
        NgxOtpInputComponent,
        ValidatorMessageComponent,
        ValidatorMessageStatusComponent,
        UserRegisterationComponent,
        NewAccountComponent,
        PersonalInfoComponent,
        InterestsComponent,
        SuccessComponent,
        EditPhoneNumberComponent,
    ],
})
export class UserRegisterationModule { }
