@defer {
<ng-container [ngSwitch]="listing.status == ListingStatus.sold">
  <ng-container *ngSwitchCase="true">
    <div class="listing_info">{{ listing.statusName! | translate }}</div>
  </ng-container>
  <ng-container *ngSwitchDefault>
    <form [formGroup]="form" VerifiedClick>
      <div [ngSwitch]="formStep">
        <div *ngSwitchCase="1" class="form_holder">
          <div class="money_input" *ngIf="isCashable()">
            <svg data-id="money_icon" viewBox="0 0 62 62" fill="none">
              <path
                d="M45.1146 21.1049C43.2969 20.3069 41.4792 20 39.6615 20C33.8906 20 28.1094 23.0633 22.3385 23.0633C20.8911 23.0633 19.4443 22.8669 17.9969 22.3881C17.8339 22.3326 17.6698 22.308 17.512 22.308C16.7031 22.308 16 22.9651 16 23.8696V39.4562C16 40.076 16.3398 40.6594 16.8849 40.899C18.701 41.6955 20.5234 42 22.3385 42C28.1094 42 33.8854 38.9367 39.6615 38.9367C41.1089 38.9367 42.5557 39.1331 44.0031 39.6119C44.1672 39.6672 44.3313 39.6917 44.4895 39.6917C45.2969 39.692 46 39.0339 46 38.1254V22.5477C46 21.9275 45.6615 21.3446 45.1146 21.1049ZM43.5 25.8536C41.8594 25.6571 40.5833 24.3509 40.5833 22.75C40.5833 22.6415 40.6067 22.5407 40.6185 22.4356C41.625 22.5153 42.599 22.6793 43.5 23.0147V25.8536ZM26.8333 31C26.8333 28.3963 28.699 26.2857 30.9531 26.2857C33.2073 26.2857 35.0729 28.3963 35.0729 31C35.0729 33.6037 33.2073 35.7143 30.9531 35.7143C28.699 35.7143 26.8333 33.6027 26.8333 31ZM18.5 24.9893C19.5443 25.2348 20.6281 25.3085 21.75 25.3871C21.4271 26.8013 20.0953 27.8571 18.5 27.8571V24.9893ZM18.5 36.1464C20.1422 36.3429 21.4167 37.6491 21.4167 39.25C21.4167 39.3584 21.3933 39.4593 21.3815 39.5644C20.3724 39.4857 19.4036 39.3188 18.5 38.9848V36.1464ZM43.5 37.0107C42.4557 36.7642 41.3724 36.6473 40.2479 36.611C40.5729 35.1987 41.9062 34.1429 43.5 34.1429V37.0107Z"
                fill="#2D3142" fill-opacity="0.8" />
            </svg>

            <div>
              <input type="text" formControlName="price" appArabicToEnglishNumerals [maxlength]="12" inputmode="numeric"
                [placeholder]="'0'" />
              <span [innerHTML]="'' | ncurrency"></span>
            </div>
          </div>
          <div class="small_add" *ngIf="isSwappable() && isCashable()">
            <svg data-id="small_add_icon" viewBox="0 0 36 36" fill="none">
              <rect width="36" height="36" rx="18" fill="white" />
              <rect x="16.8333" y="11" width="2.33333" height="14" rx="1.16667" fill="#9597A0" />
              <rect x="25" y="16.8333" width="2.33333" height="14" rx="1.16667" transform="rotate(90 25 16.8333)"
                fill="#9597A0" />
            </svg>

          </div>
          <ng-container *ngIf="isSwappable()">

            <div class="closable_list" *ngIf="selectedList.size > 0">
              <app-ad-mini-closable *ngFor="let item of selectedList" [item]="item"
                (onClose)="removeItem($event)"></app-ad-mini-closable>
            </div>
            <div class="listing_input" (click)="openList()">
              <svg data-id="big_add_icon" width="34" height="28" viewBox="0 0 34 28" fill="none">
                <rect width="33.8824" height="27.5294" rx="13.7647" fill="#545765" />
                <rect x="16" y="8" width="2" height="12" rx="1" fill="white" />
                <rect x="23" y="13" width="2" height="12" rx="1" transform="rotate(90 23 13)" fill="white" />
              </svg>

              <div>{{ 'Offer Listings' | translate}}</div>
            </div>
          </ng-container>

          <button class="final_offer_price" [ngClass]="{ active: true }" pRipple VerifiedClick
            (VerifiedClick)="createOffer()">
            <div>
              <div>{{ 'Offer Value' | translate}}</div>
              <span [innerHTML]="price | ncurrency"></span>
            </div>
            <div class="submit_btn">
              <span>{{ 'Submit offer' | translate}}</span>
            </div>
          </button>
        </div>
        <div *ngSwitchDefault>
          <app-seller-mini-listing [listing]="listing" [selected]="selectedList" (onBack)="formStep = 1"
            (onSubmit)="addListing($event)" [allowBack]="!isDesktop"></app-seller-mini-listing>
        </div>
      </div>
    </form>
    <div class="engage_user_links">
      <app-ad-seller-info *ngIf="listing.user && listing.user.businessName " [listing]="listing"
        [isBussnisLogo]="true" />
      <div class="engage_links">
        <div class="engage_link" VerifiedClick (VerifiedClick)="scrollToComments()">
          <svg data-id="comment_lising_icon" viewBox="0 0 20 20" fill="none">
            <path
              d="M0 10C0 4.47715 4.47715 0 10 0C15.5228 0 20 4.47715 20 10V16.8421C20 18.5862 18.5862 20 16.8421 20H10C4.47715 20 0 15.5228 0 10Z"
              fill="#2D3142" />
            <rect x="11.25" y="8.75" width="3.75" height="3.75" rx="1.875" fill="#E3E4E9" />
            <rect x="5" y="8.75" width="3.75" height="3.75" rx="1.875" fill="#E3E4E9" />
          </svg>

          <span>{{ 'Comment' | translate }}</span>
        </div>
        <div *ngIf="canViewPhone()" class="engage_link" [skipAuth]="forcePhoneVisible" VerifiedClick
          (VerifiedClick)="showPhoneNumber()" (unVerifiedClick)="sendEvent($event)">
          <svg data-id="listing_icon" viewBox="0 0 19 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M17.4338 14.1016L13.2219 11.9985C13.0046 11.8844 12.766 11.8371 12.5288 11.861C12.2915 11.8849 12.0633 11.9793 11.8658 12.1352L9.6117 13.8913C8.23788 13.11 7.12557 11.815 6.45051 10.2108L7.9413 7.53976C8.07353 7.30829 8.15429 7.0418 8.17625 6.7645C8.1982 6.4872 8.16066 6.20787 8.06703 5.95188L6.26191 1.01996C6.13595 0.686939 5.92267 0.409557 5.65301 0.228039C5.38334 0.0465206 5.0713 -0.0296959 4.76215 0.010442C3.54939 0.196471 2.43559 0.891848 1.62783 1.96729C0.820064 3.04272 0.373302 4.42504 0.370605 5.85723C0.370605 14.2068 6.17211 21 13.3027 21C14.5259 20.9968 15.7064 20.4737 16.6248 19.5279C17.5433 18.582 18.1371 17.2778 18.296 15.8578C18.3303 15.4958 18.2652 15.1304 18.1102 14.8146C17.9551 14.4989 17.7183 14.2491 17.4338 14.1016Z"
              fill="#2D3142" />
          </svg>

          <span>{{ sellerPhone }}</span>
        </div>
      </div>

    </div>
  </ng-container>
</ng-container>


<ng-container *ngIf="offerMenuVisible">

  <p-dialog header="" [closable]="true" [modal]="true" [(visible)]="offerMenuVisible" [style]="{ width: '50vw' }"
    (onHide)="resetForm()">
    <div class="modelTop">
      <app-seller-mini-listing [listing]="listing" [selected]="selectedList" (onBack)="offerMenuVisible = false"
        (onSubmit)="addListing($event)"></app-seller-mini-listing>
    </div>
    <div class="model_actions"></div>
  </p-dialog>


</ng-container>
}
@placeholder {
<div></div>
}