import { Ng<PERSON>or } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  OnInit,
  ViewEncapsulation
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { FetchingPageDataComponent } from '@src/app/shared/components/fetching-page-data/fetching-page-data.component';
import { SecondaryBtnComponent } from '@src/app/shared/components/secondary-btn/secondary-btn.component';
import { NtranslatePipe } from '@src/app/shared/pipes/ntranslate.pipe';
import { ButtonDirective } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { DialogModule } from 'primeng/dialog';
import { Ripple } from 'primeng/ripple';
import { BrowserService } from 'src/app/modules/core/service/browser.service';


@Component({
  selector: 'app-account-type',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  imports: [
    NgFor,
    ButtonDirective,
    Ripple,
    RouterLink,
    FormsModule,
    ReactiveFormsModule,
    CheckboxModule,
    SecondaryBtnComponent,
    NtranslatePipe,
    DialogModule,
    FetchingPageDataComponent
  ],
  templateUrl: './account-type.component.html',
  styleUrl: './account-type.component.scss'
})
export class AccountTypeComponent implements OnInit, AfterViewInit {

  public registerForm: FormGroup;

  showTermsAndConditions: boolean = false;

  accountTypeOptions: any[] = [
    { label: 'account-individual', value: 'consumer', img: 'assets/img/individualIcon.svg' },
    { label: 'account-business', value: 'business', img: 'assets/img/businessIcon.svg' },
  ];

  constructor(
    private _formBuilder: FormBuilder,
    private _router: Router,
    private cdr: ChangeDetectorRef,
    private activetedRoute: ActivatedRoute,
    private browser: BrowserService,
  ) {

  }
  ngAfterViewInit(): void {
    this.cdr.detectChanges();
  }

  ngOnInit(): void {
    this.browser.removeStorageItem('firstName');
    this.browser.removeStorageItem('lastName');
    this.browser.removeStorageItem('image');

    this.registerForm = this._formBuilder.group({
      accounttype: new FormControl('consumer', Validators.required),
      agree: new FormControl(false, Validators.requiredTrue),
    });

    this.activetedRoute.params.subscribe(res => {
      if (res['code']) {
        this.browser.setStorageItem("referralCode", res['code']);
      }
    });

  }

  get registerFormControl() {
    return this.registerForm.controls;
  }

  validateValue(controlName: string) {
    return (
      this.registerForm.controls[controlName].valid &&
      this.registerForm.controls[controlName].touched
    );
  }

  getValue(controlName: string) {
    return this.registerForm.controls[controlName].value;
  }

  checkFormIsInValid(): boolean {
    return !(
      this.registerForm.controls['agree'].valid &&
      this.registerForm.controls['accounttype'].valid
    );
  }

  isControlInvalid(controlName: string): boolean {
    const control = this.registerForm.get(controlName);
    return control!.errors !== null;
  }

  public hasError = (controlName: string, errorName: string) => {
    return this.registerForm.controls[controlName].hasError(errorName);
  };

  setAccountType(name: string) {
    this.registerForm.controls['accounttype'].setValue(name);
  }

  openTermsAndConditions() {
    this.showTermsAndConditions = true;
  }

  submit() {
    if (!this.registerForm.valid) { return }
    this.browser.setStorageItem('AccountType', this.registerForm.controls['accounttype'].value);
    if (this.registerForm.controls['accounttype'].value === 'business') {
      this._router.navigate(['/authentication/register-partner']);
    } else {
      this._router.navigate(['/authentication/register/new-account']);

    }
  }

}
