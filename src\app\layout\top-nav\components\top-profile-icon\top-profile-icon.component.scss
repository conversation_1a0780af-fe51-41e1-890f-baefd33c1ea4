@import "mixins";
@import "variables";

.profile_menu {
  flex-shrink: 0;

  .profile_btn {
    width: 20px;
    height: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    min-width: auto;
    line-height: normal;
    overflow: hidden;

    svg {
      width: 20px;
      height: 20px;
    }


  }
}


.user_rate {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 1rem;
  margin-top: 5px;
  font-weight: 400;

  svg {
    width: 13px;
    height: 17px;
    color: $orangeColor;
  }

  b {
    font-weight: normal;
  }

}

.custom_lang {
  display: flex;
  justify-content: space-between;
  align-items: center;

  app-language-button {
    border: 1px solid #dedede;
    border-radius: 20px;
    padding: 5px 6px;
  }
}

.middle_btn {
  margin: 40px 0px;
  color: $primary;

  app-primary-btn {
    color: $primary;
  }

  &::ng-deep {
    .p-button {
      color: $primary;
    }
  }
}

.user_info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e7e7e7;
  margin-left: -17px;
  margin-right: -17px;
  color: #000;
  cursor: pointer;

  h3 {
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0px;

    // img{
    //   width: 48px;
    //   height: 48px;
    //   object-fit: cover;
    //   border-radius: 50%;
    //   background: gray;
    // }
    b {
      display: block;
      font-size: 0.9rem;
    }
  }

  .pi-chevron-right {
    transform: rotate(var(--arrow-rotation));
  }
}

ul {
  list-style: none;
  padding: 0px !important;
  margin: 0px;

  li {
    a {
      color: $text-color;
    }
  }
}


.menu_body {
  width: 100%;
  height: calc(100vh - 105px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}


.link {
  display: flex;
  align-items: center;
  font-size: 1rem;
  gap: 17px;
  padding: 13px 0px;
  cursor: pointer;

  svg {
    width: 17px;
    height: 17px;
    color: rgba($color: $text-color, $alpha: 0.4);


  }



  &.custom {
    gap: 10px;

    svg {
      width: 25px;
      height: 25px;
    }
  }
}


.info_link {
  display: flex;
  align-items: center;
  gap: 15px;
  margin: 10px 0px;
  color: $text-color;

  svg {
    width: 20px;
    height: 20px;
  }

  i {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;
    border-radius: 15px;
  }
}

#bottom_links {
  padding-bottom: 50px;
}