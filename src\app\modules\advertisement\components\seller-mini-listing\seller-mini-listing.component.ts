import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { RouterModule } from '@angular/router';
import { Observable, map } from 'rxjs';
import { LoaderSpinnerComponent } from 'src/app/shared/components/loader-spinner/loader-spinner.component';
import { PrimaryBtnComponent } from 'src/app/shared/components/primary-btn/primary-btn.component';
import { SecondaryBtnComponent } from 'src/app/shared/components/secondary-btn/secondary-btn.component';
import { LazyloadDirective } from 'src/app/shared/directives/lazyload.directive';
import { ToggleClassDirective } from 'src/app/shared/directives/toggle-class.directive';
import { ListingDetails, ListingStatus, ListingView, UserLisitingFilters } from 'src/app/shared/models/listing.model';
import { OfferListing } from 'src/app/shared/models/offer.model';
import { ListingImagePathPipe } from 'src/app/shared/pipes/listing-image-path.pipe';
import { NCurrencyPipe } from 'src/app/shared/pipes/ncurrency.pipe';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { ListingService } from 'src/app/shared/services/listing.service';

@Component({
  selector: 'app-seller-mini-listing',
  standalone: true,
  imports: [CommonModule,
    ToggleClassDirective,
    LazyloadDirective,
    SecondaryBtnComponent,
    PrimaryBtnComponent,
    RouterModule,
    ListingImagePathPipe,
    LoaderSpinnerComponent,
    NtranslatePipe,
    NCurrencyPipe],
  templateUrl: './seller-mini-listing.component.html',
  styleUrls: ['./seller-mini-listing.component.scss']
})
export class SellerMiniListingComponent implements OnInit, OnChanges {
  @Output() onBack = new EventEmitter();
  @Output() onSubmit = new EventEmitter();
  @Input() allowBack: boolean = false;
  @Input() selected: Set<ListingView | OfferListing>;
  @Input() listing: ListingDetails | OfferListing | undefined;

  listing$: Observable<ListingView[]>;
  selectedList = new Map<number, ListingView | OfferListing>();
  constructor(private listingService: ListingService) { }
  ngOnChanges(changes: SimpleChanges): void {

  }

  ngOnInit(): void {
    this.getListing();
    if (this.selected) {
      this.selected.forEach(item => {
        this.selectedList.set(item.id, item);
      });
    }
  }




  getListing() {

    let filter: UserLisitingFilters = {
      status: ListingStatus.approved,
      pageSize: 100,
      pageNumber: 1
    };

    if (this.listing && this.listing.interedtedCategoris && this.listing.interedtedCategoris.length > 0) {
      filter.categories = this.listing.interedtedCategoris;
    }

    this.listing$ = this.listingService.getUserListing(filter).pipe(map(res => res.body?.data.items ?? []));
  }

  SelectedItem(item: ListingView) {
    if (!this.selectedList.has(item.id)) {
      this.selectedList.set(item.id, item);
    } else {
      this.selectedList.delete(item.id);
    }
  }


}
