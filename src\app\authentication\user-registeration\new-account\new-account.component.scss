@import "variables";
@import "mixins";
@import "../../../shared/components/inputs/style/common_input.scss";


.new_account {
  padding: 0px;

  .auth_form {




    .agree_to {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 12px;
      margin-bottom: clamp(20px, 11vw, 5vh);

      .p-checkbox {
        &-box {
          border-color: rgba(#2d3142, 0.3);
          @include transition(0.3s);
          border-radius: 50%;

          &.p-focus {
            box-shadow: none !important;
          }

          &.p-highlight {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
          }
        }
      }

      label {
        font-size: 14px;
        font-weight: 400;
        color: #2d3142;
        line-height: normal;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 5px;

        .p-button {
          padding: 0;
          background-color: transparent !important;
          box-shadow: none !important;
          border: 0 !important;
          font-size: 14px;
          font-weight: 400;
          color: var(--secondary-color);
          @include transition(0.3s);

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }

    .using {
      font-size: 14px;
      font-weight: 400;
      color: #2d3142;
      text-align: center;
      margin: clamp(10px, 5vw, 36px) 0;
    }

    .agreeing {
      font-size: 14px;
      font-weight: 400;
      color: #2d314287;
      text-align: center;
      margin-bottom: clamp(20px, 2vw, 69px);
      align-items: center;
      justify-content: center;
      flex-direction: column;
      gap: 10px;

      .p-button {
        background-color: transparent !important;
        padding: 0;
        border: 0 !important;
        box-shadow: none !important;
        font-size: 14px;
        font-weight: 400;
        color: var(--secondary-color);
        @include transition(0.3s);

        span {
          font-size: 14px;
          font-weight: 400;
          color: var(--secondary-color);
        }

        &:hover {
          text-decoration: underline;
        }
      }
    }

    .have_account {
      h5 {
        font-size: 14px;
        font-weight: 400;
        color: #2d3142;
        text-align: center;
        margin-bottom: 22px;
      }
    }
  }
}


@include Large {


  .new_account {
    .auth_form {

      .agreeing {
        display: flex;
      }
    }
  }
}