import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MainLayoutComponent } from 'src/app/modules/layouts/main-layout/main-layout.component';
import { AdvertisementIndexComponent } from './pages/advertisement-index/advertisement-index.component';

const routes: Routes = [
  {
    path: '',
    component: MainLayoutComponent,
    children: [
      { path: '', redirectTo: '/home', pathMatch: 'full' },
      {
        path: ':id',
        component: AdvertisementIndexComponent
      },
      {
        path: ':id/:listing',
        component: AdvertisementIndexComponent
      },
    ]
  }
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AdvertisementRoutingModule { }
