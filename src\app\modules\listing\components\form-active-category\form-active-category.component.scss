@import "mixins";
@import "variables";

.category_btn {
    display: flex;
    background: #eaeaea;
    border-radius: 82px;
    align-items: center;
    padding: 15px 25px;
    margin-bottom: 28px;
    gap: 20px;
    img {
        width: 35px;
        height: 35px;
        object-fit: contain;
    }
    h2{
        font-size: 16px;
        font-weight: 800;
        margin: 0px;
        @include rtl2(margin-bottom,5px);
    }
    h3{
        font-size: 14px;
        font-weight: 400;
        margin: 0px;
    }

    .category_info{
        display: flex;
        align-items: center;
        gap: 14px;

        @include Large{
            gap: 24px;
        }
        
    
    }

    &.createMode{
        cursor: pointer;
    }


    .pi{
        @include rtl2(transform , rotate(180deg));
    }

    
    
}