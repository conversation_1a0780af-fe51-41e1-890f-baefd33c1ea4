import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { StripComponent } from '@src/app/layout/top-nav/components/strip/strip.component';
import { Observable, Subject } from 'rxjs';
import { FooterComponent } from 'src/app/layout/footer/footer.component';
import { SideMenuService } from 'src/app/layout/top-nav/service/side-menu.service';
import { TopNavComponent } from 'src/app/layout/top-nav/top-nav.component';
import { DeviceDetectionService } from 'src/app/shared/services/device-detection.service';
import { OpenAppNavComponent } from "../../../layout/open-app-nav/open-app-nav.component";

@Component({
  selector: 'app-main-layout',
  templateUrl: './main-layout.component.html',
  styleUrls: ['./main-layout.component.scss'],
  standalone: true,
  imports: [CommonModule, RouterModule, TopNavComponent, FooterComponent, StripComponent, OpenAppNavComponent],
})
export class MainLayoutComponent implements OnInit {

  hideSearchBar$: Observable<boolean>;

  filterResults$ = new Subject();







  constructor(
    public device: DeviceDetectionService,
    private menuService: SideMenuService,

  ) {



    this.hideSearchBar$ = menuService.unSearchBar$;


  }




  ngOnInit(): void {








  }



}
