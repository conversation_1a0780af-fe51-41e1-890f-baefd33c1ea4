import { CommonModule } from '@angular/common';
import { Component, Input, SimpleChanges } from '@angular/core';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Store } from '@ngrx/store';
import { getCategories } from '@src/app/store/app/selectors/app.selector';
import { MenuItem } from 'primeng/api';
import { map, Observable, tap } from 'rxjs';
import { MinipageComponent } from 'src/app/modules/page/component/minipage/minipage.component';
import { LookupDTO } from "src/app/shared/models/lookup.model";
import { LookupService } from 'src/app/shared/services/lookup.service';
@UntilDestroy({ checkProperties: true })
@Component({
  selector: 'app-category-footer',
  standalone: true,
  imports: [CommonModule, MinipageComponent],
  templateUrl: './category-footer.component.html',
  styleUrls: ['./category-footer.component.scss']
})
export class CategoryFooterComponent {

  @Input() category: MenuItem | LookupDTO;
  subCategories$: Observable<LookupDTO[]>;
  @Input() subCategoryId: number;
  subCategory?: LookupDTO;
  categoriesList: MenuItem[];
  selectedCategory: MenuItem;
  parentCategory: MenuItem;

  constructor(
    private _lookupService: LookupService,
    private store: Store


  ) { }

  ngOnInit(): void {



  }

  ngOnChanges(changes: SimpleChanges): void {


    if (changes['category']) {
      this.subCategories$ = this._lookupService.getChildCategories(+this.category.id!).pipe(map(e => e.data), tap(res => {
        if (this.subCategoryId && res.length > 0) {
          this.subCategory = res.find(item => item.id == this.subCategoryId);
        }
      }));

      this.store.select(getCategories).pipe(untilDestroyed(this), map(res => res.menu), map(res => this._lookupService.convertToMenuItems(res)), tap(res => {
        this.categoriesList = res;
        const { category, parent } = this._lookupService.findCategoryById(this.subCategoryId ? this.subCategoryId : this.category.id, this.categoriesList);
        this.selectedCategory = category;
        this.parentCategory = parent ?? category;

      })).subscribe();
    }

  }


}
