@import "variables";
@import "mixins";
@import "../../../shared/components/inputs/style/common_input.scss";

.personal_info {
  // padding: 48px;
  padding: 32px 24px;
  height: 100%;
  margin: 0 -12px;

  .row>* {
    padding: 0px;
  }

  // @media (max-width: 991.98px) {
  //   padding: 32px 24px;
  // }



  .upload_photo {
    display: flex;
    align-items: center;
    justify-content: center;
    width: clamp(78px, 30vw, 166px);
    height: clamp(78px, 30vw, 166px);
    @include borderRadius(100%);
    background-color: #f2edf7;
    border: 1px solid #f2edf7;
    overflow: hidden;
    margin: 0 auto clamp(40px, 13vw, 48px);
    cursor: pointer;

    input[type="file"] {
      display: none;
    }

    .camera_icon {
      width: 100%;
      max-width: clamp(30px, 11vw, 58px);
    }

    #uploadedImg {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    img[src=""] {
      display: none;
    }
  }

  .inputs_holder {
    margin-bottom: clamp(25px, 7vw, 120px);

    // @media (max-width: 991.98px) {
    //   margin-bottom: 48px;
    // }
  }
}

.error_list {
  list-style: none;
  padding: 20px;
  margin: 0px;

  li {
    display: flex;
    margin-bottom: 10px;
    align-items: center;
    font-size: 14px;
  }

}

.info_icon {
  width: 24px;
  height: 24px;
  display: inline-flex;
  background: #2d314217;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  margin-inline-end: 10px;


  svg {
    width: 12px;
    height: 12px;
    fill: rgba(45, 49, 66, 0.78);

  }

  &.right {
    background: rgba($color: $primary, $alpha: 0.16);
    ;
    color: var(--secondary-color);

    svg {
      width: 16px;
      height: 16px;
    }
  }

  &.fail {
    background: #DC2D2D29;
    color: #DC2D2D;

    svg {
      width: 16px;
      height: 16px;
    }
  }
}


@include Large {
  .personal_info {

    .inputs_holder {
      margin-bottom: clamp(25px, 5vh, 120px);
    }
  }
}