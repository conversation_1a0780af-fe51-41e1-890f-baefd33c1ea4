import { NgIf } from '@angular/common';
import { ChangeDetectorRef, Component } from '@angular/core';
import { BrowserService } from '@src/app/modules/core/service/browser.service';
import { TranslationService } from '@src/app/modules/core/service/translation.service';
import { GeneralSettingsPipe } from '@src/app/shared/pipes/general-settings.pipe';
import { NtranslatePipe } from '@src/app/shared/pipes/ntranslate.pipe';
import { DeviceDetectionService } from '@src/app/shared/services/device-detection.service';

@Component({
  selector: 'app-open-app-nav',
  standalone: true,
  imports: [NgIf, NtranslatePipe],
  templateUrl: './open-app-nav.component.html',
  styleUrl: './open-app-nav.component.scss',
  providers: [GeneralSettingsPipe]
})
export class OpenAppNavComponent {
  platform: 'android' | 'ios' | 'huawei' | 'other' = 'other';
  showBanner = false;
  expiryDays = 30;

  constructor(
    public ds: DeviceDetectionService,
    private cdr: ChangeDetectorRef,
    private browser: BrowserService,
    private generalSettingsPipe: GeneralSettingsPipe,
    private translation: TranslationService
  ) {

    if (this.browser.isBrowser()) {
      this.platform = this.getMobileOperatingSystem();
      this.generalSettingsPipe.transform('AppBannerExpiry', 30).subscribe((days) => {
        this.expiryDays = Number(days);
      });
      this.showBanner = this.shouldShowBanner() && !(this.platform === 'ios' && this.isSafari());
    }

  }

  getMobileOperatingSystem(): 'android' | 'ios' | 'huawei' | 'other' {
    const ua = navigator.userAgent.toLowerCase();

    if (ua.includes('huawei') || ua.includes('honor')) {
      return 'huawei';
    }

    if (/android/i.test(ua)) {
      return 'android';
    }

    if (/iphone|ipad|ipod/.test(ua)) {
      return 'ios';
    }

    return 'other';
  }

  isSafari(): boolean {
    const ua = navigator.userAgent.toLowerCase();
    const isIos = /iphone|ipad|ipod/.test(ua);
    const isSafari = ua.includes('safari') && !ua.includes('crios') && !ua.includes('fxios');
    return isIos && isSafari;
  }


  openApp(): void {
    const lang = this.translation.currentLang?.toLowerCase() || 'en-us';

    const deepLink =
      this.platform === 'android'
        ? `intent://4sw.app#Intent;scheme=https;package=com.forswapp.forswappandroid;end`
        : `https://4sw.app/${lang}/home`;

    const fallbackUrls = {
      android: 'https://play.google.com/store/apps/details?id=com.forswapp.forswappandroid',
      ios: 'https://apps.apple.com/eg/app/4swapp/id6673908461',
      huawei: 'https://appgallery.huawei.com/app/C114122853?sharePrepath=ag&locale=en_GB'
    };

    const fallbackUrl = fallbackUrls[this.platform];
    let appOpened = false;

    const blurHandler = () => {
      appOpened = true;
    };

    window.addEventListener('blur', blurHandler);
    window.location.assign(deepLink);

    setTimeout(() => {
      window.removeEventListener('blur', blurHandler);
      if (!appOpened && this.platform !== 'other') {
        window.location.href = fallbackUrl;
      }
    }, 1000);
  }

  shouldShowBanner(): boolean {
    if (typeof localStorage === 'undefined') return true;

    const dismissedUntil = localStorage.getItem('bannerDismissedUntil');
    if (!dismissedUntil) return true;

    const now = new Date();
    const expiry = new Date(dismissedUntil);
    return now > expiry;
  }

  closeBanner(): void {
    if (typeof localStorage === 'undefined') return;

    const banner = document.querySelector('.mobile-app-banner') as HTMLElement;
    if (banner) {
      banner.classList.add('closing');
      setTimeout(() => {
        const expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + this.expiryDays);
        localStorage.setItem('bannerDismissedUntil', expiryDate.toISOString());
        this.showBanner = false;
        this.cdr.detectChanges();
      }, 300);
    }
  }
}
