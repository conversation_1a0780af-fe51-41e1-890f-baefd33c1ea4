<p-dialog header="" [closable]="true" [className]="'offer_form'" [modal]="true" [(visible)]="makeOfferVisible"
    [style]="{width: '50vw'}" [dismissableMask]="false">
    <div class="modelTop" *ngIf="offer">
        <app-offer-create-suggested [offer]="offer" (onClose)="makeOfferVisible = false"></app-offer-create-suggested>
    </div>
    <div class="model_actions"></div>
</p-dialog>

<div class="offers_box">
    <h2 class="list_title">
        <span>{{ "Offers" | translate }}</span> <b>{{ currentIndex() == 0 ? pagination.totalItems :
            suggestedPagination.totalItems }}</b>
    </h2>
    <div>
        <ul class="tabs">
            <li [ngClass]="{'active' : currentIndex() == 0}" (click)="currentIndex.set(0)">{{ "Offers" | translate }}
            </li>
            <li class="smartmatchingIcon" [ngClass]="{'active' : currentIndex() == 1}" (click)="currentIndex.set(1)">{{
                "SmartMatching" | translate }} <svg xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink" width="22px" height="22px" viewBox="0 0 22 22"
                    version="1.1">
                    <g id="surface1">
                        <path
                            style=" stroke:none;fill-rule:nonzero;fill:rgb(94.901961%,65.098041%,28.627452%);fill-opacity:1;"
                            d="M 13.5 4.25 C 13.746094 4.332031 13.996094 4.414062 14.25 4.5 C 14.5 5.09375 14.714844 5.671875 14.921875 6.28125 C 15.683594 8.320312 16.542969 9.421875 18.546875 10.378906 C 19.273438 10.691406 20.003906 10.980469 20.75 11.25 C 20.714844 11.710938 20.714844 11.710938 20.5 12.25 C 19.863281 12.585938 19.863281 12.585938 19.0625 12.875 C 17.183594 13.5625 16.207031 14.382812 15.285156 16.132812 C 14.855469 17.066406 14.535156 18.019531 14.25 19 C 13.921875 19 13.589844 19 13.25 19 C 13.136719 18.679688 13.136719 18.679688 13.019531 18.351562 C 11.867188 15.140625 11.867188 15.140625 9.234375 13.171875 C 8.742188 12.945312 8.246094 12.71875 7.75 12.5 C 7.414062 12.335938 7.082031 12.167969 6.75 12 C 6.75 11.753906 6.75 11.503906 6.75 11.25 C 7.097656 11.121094 7.097656 11.121094 7.453125 10.992188 C 9.34375 10.265625 10.953125 9.574219 12 7.75 C 12.375 6.898438 12.707031 6.046875 13.015625 5.164062 C 13.25 4.5 13.25 4.5 13.5 4.25 Z M 13.5 4.25 " />
                        <path
                            style=" stroke:none;fill-rule:nonzero;fill:rgb(94.117647%,41.176471%,21.176471%);fill-opacity:1;"
                            d="M 4.75 1.25 C 5.21875 1.3125 5.21875 1.3125 5.75 1.5 C 6.03125 1.9375 6.03125 1.9375 6.25 2.46875 C 6.777344 3.617188 7.273438 4.042969 8.4375 4.546875 C 8.625 4.613281 8.808594 4.679688 9 4.75 C 8.933594 5.222656 8.933594 5.222656 8.75 5.75 C 8.332031 5.988281 8.332031 5.988281 7.8125 6.171875 C 6.503906 6.753906 6.0625 7.734375 5.5 9 C 5.023438 8.929688 5.023438 8.929688 4.5 8.75 C 4.234375 8.175781 3.984375 7.589844 3.75 7 C 3.136719 6.140625 2.203125 5.878906 1.25 5.5 C 1.25 5.253906 1.25 5.003906 1.25 4.75 C 1.417969 4.6875 1.585938 4.621094 1.761719 4.554688 C 3.042969 4.007812 3.839844 3.613281 4.40625 2.296875 C 4.53125 1.949219 4.648438 1.601562 4.75 1.25 Z M 4.75 1.25 " />
                        <path
                            style=" stroke:none;fill-rule:nonzero;fill:rgb(94.509804%,65.490198%,29.019609%);fill-opacity:1;"
                            d="M 4.25 13.75 C 4.578125 13.75 4.910156 13.75 5.25 13.75 C 5.324219 13.949219 5.324219 13.949219 5.398438 14.152344 C 5.964844 15.488281 6.511719 16.035156 7.847656 16.601562 C 7.980469 16.652344 8.113281 16.699219 8.25 16.75 C 8.25 17.078125 8.25 17.410156 8.25 17.75 C 7.976562 17.851562 7.703125 17.957031 7.421875 18.0625 C 6.320312 18.539062 5.925781 19.089844 5.453125 20.1875 C 5.386719 20.375 5.320312 20.558594 5.25 20.75 C 4.777344 20.683594 4.777344 20.683594 4.25 20.5 C 4.003906 20.074219 4.003906 20.074219 3.8125 19.546875 C 3.324219 18.449219 2.734375 18.105469 1.652344 17.648438 C 1.519531 17.597656 1.386719 17.550781 1.25 17.5 C 1.25 17.253906 1.25 17.003906 1.25 16.75 C 1.386719 16.691406 1.519531 16.632812 1.660156 16.574219 C 2.796875 16.058594 3.480469 15.691406 4 14.5 C 4.082031 14.253906 4.164062 14.003906 4.25 13.75 Z M 4.25 13.75 " />
                    </g>
                </svg>
            </li>
        </ul>
    </div>
    <div [ngSwitch]="currentIndex()">
        <div *ngSwitchCase="0">
            <div class="list_content" *ngIf="offers$ | async as list">
                <app-offer-card *ngFor="let item of list" [offer]="item" [type]="'offer'"></app-offer-card>

                <app-no-result *ngIf="pagination.totalItems == 0"></app-no-result>

                <p-paginator *ngIf="pagination.totalPages > 1" (onPageChange)="pageChanged($event)"
                    [first]="(filter.pageNumber - 1) * pagination.pageSize" [rows]="pagination.pageSize"
                    [totalRecords]="pagination.totalItems" [pageLinkSize]="3" [showFirstLastIcon]="false"></p-paginator>
            </div>
        </div>
        <div *ngSwitchDefault>
            <div class="list_content" *ngIf="suggestedList$ | async as list">
                <app-offer-suggested-card *ngFor="let item of list" [offer]="item"
                    (onChoose)="onChoose(item)"></app-offer-suggested-card>

                <app-no-result *ngIf="suggestedPagination.totalItems == 0"></app-no-result>

                <p-paginator *ngIf="suggestedPagination.totalPages! > 1" (onPageChange)="suggestedPageChanged($event)"
                    [first]="(this.filter.pageNumber! - 1) * suggestedPagination.pageSize!"
                    [rows]="suggestedPagination.pageSize" [totalRecords]="suggestedPagination.totalItems"
                    [pageLinkSize]="3" [showFirstLastIcon]="false"></p-paginator>
            </div>
        </div>
    </div>

</div>