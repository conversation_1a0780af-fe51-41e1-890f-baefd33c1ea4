import { Component, OnInit } from '@angular/core';
import { AuthImgSideComponent } from '../../../../shared/components/auth-img-side/auth-img-side.component';
import { RouterOutlet } from '@angular/router';
import { LanguageButtonComponent } from '../../../../shared/components/language-button/language-button.component';

@Component({
    selector: 'app-operation-registeration',
    templateUrl: './operation-registeration.component.html',
    styleUrls: ['./operation-registeration.component.scss'],
    standalone: true,
    imports: [AuthImgSideComponent, RouterOutlet, LanguageButtonComponent]
})
export class OperationRegisterationComponent implements OnInit {

  constructor() { }

  ngOnInit(): void {


  }

}
