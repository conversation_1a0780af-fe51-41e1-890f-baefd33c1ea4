<div class="alert-body">
    <svg data-id="warn_icon" viewBox="0 0 142 142" fill="none">
        <path
            d="M56.75 8.80459C65.5679 3.71356 76.4321 3.71356 85.25 8.80459L117.738 27.5614C126.556 32.6525 131.988 42.0611 131.988 52.2432V89.7568C131.988 99.9389 126.556 109.348 117.738 114.439L85.25 133.195C76.4321 138.286 65.5679 138.286 56.75 133.195L24.2622 114.439C15.4443 109.348 10.0122 99.9389 10.0122 89.7568V52.2432C10.0122 42.0611 15.4443 32.6525 24.2622 27.5614L56.75 8.80459Z"
            stroke="#EA8C35" stroke-opacity="0.21" />
        <path opacity="0.25"
            d="M62 14.1962C67.5692 10.9808 74.4308 10.9808 80 14.1962L115.694 34.8038C121.263 38.0192 124.694 43.9615 124.694 50.3923V91.6077C124.694 98.0385 121.263 103.981 115.694 107.196L80 127.804C74.4308 131.019 67.5692 131.019 62 127.804L26.3064 107.196C20.7372 103.981 17.3064 98.0385 17.3064 91.6077V50.3923C17.3064 43.9615 20.7372 38.0192 26.3064 34.8038L62 14.1962Z"
            fill="#fbb855" />
        <path
            d="M75.0586 47L74.2852 80.8906H66.7383L65.9883 47H75.0586ZM70.5117 95.5156C69.0898 95.5156 67.8711 95.0156 66.8555 94.0156C65.8555 93.0156 65.3555 91.7969 65.3555 90.3594C65.3555 88.9531 65.8555 87.75 66.8555 86.75C67.8711 85.75 69.0898 85.25 70.5117 85.25C71.9023 85.25 73.1055 85.75 74.1211 86.75C75.1523 87.75 75.668 88.9531 75.668 90.3594C75.668 91.3125 75.4258 92.1797 74.9414 92.9609C74.4727 93.7422 73.8477 94.3672 73.0664 94.8359C72.3008 95.2891 71.4492 95.5156 70.5117 95.5156Z"
            fill="#f99c10" />
    </svg>


    <h2 [ngClass]="{ 'bold-text' : description && description.length > 0}">{{ message }}</h2>
    <div *ngIf="description && description.length > 0">
        {{ description }}
    </div>

    <div [ngSwitch]="btns.length > 0">
        <div *ngSwitchCase="true" class="btns_list">
            <ng-container *ngFor="let btn of btns">
                <ng-container [ngSwitch]="btn.value">
                    <app-dark-btn *ngSwitchCase="true" [btnText]="btn.title" btnType="button" [bigBtn]="true"
                        (click)="close(btn.value)"></app-dark-btn>

                    <app-gray-btn *ngSwitchDefault [btnText]="btn.title" btnType="button" [bigBtn]="true"
                        (click)="close(btn.value)"></app-gray-btn>
                </ng-container>

            </ng-container>
        </div>
        <div *ngSwitchDefault>
            <Button pButton pRipple [label]="'Ok' | translate" (click)="close(true)"></Button>
        </div>
    </div>
</div>