import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { Observable } from 'rxjs';
import { itemDetails, itemImages } from 'src/app/modules/listing/models/item.details.model';
import { InnerListingService } from 'src/app/modules/listing/services/inner-listing.service';
import { GrayBtnComponent } from 'src/app/shared/components/gray-btn/gray-btn.component';
import { ItemCarouselComponent } from 'src/app/shared/components/item-carousel/item-carousel.component';
import { LoaderBoxComponent } from 'src/app/shared/components/loader-box/loader-box.component';
import { SecondaryBtnComponent } from 'src/app/shared/components/secondary-btn/secondary-btn.component';
import { SwappableItemsComponent } from 'src/app/shared/components/swappable-items/swappable-items.component';
import { ListingPaymentMethod, TempSelectedProperty } from 'src/app/shared/models/listing.model';
import { NCurrencyPipe } from 'src/app/shared/pipes/ncurrency.pipe';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';

@Component({
  selector: 'app-form-listing-details-review',
  standalone: true,
  imports: [
    CommonModule,
    ItemCarouselComponent,
    SecondaryBtnComponent,
    GrayBtnComponent,
    LoaderBoxComponent,
    SwappableItemsComponent,
    NtranslatePipe,
    NCurrencyPipe,
  ],
  templateUrl: './form-listing-details-review.component.html',
  styleUrls: ['./form-listing-details-review.component.scss']
})
export class FormListingDetailsReviewComponent implements OnInit, OnDestroy {

  @Output() onSubmit = new EventEmitter();
  @Output() onEdit = new EventEmitter();
  @Input() locationName: string;
  @Input() mainCategory: MenuItem;
  @Input() subCategory: MenuItem;

  @Input() intersetedCategories: number[];
  @Input() details: itemDetails;
  @Input() set images(value) {
    this.listingImages = value;

    if (value.urls && value.urls.length > 0) {
      this.items = value.urls.map(e => {
        return e.url;
      });

      const cindex = this.items.findIndex(item => item == value.coverURL.url);

      if (cindex > -1) {
        this.items.splice(cindex, 1);
      }
      this.items.unshift(value.coverURL.url);
    }
  }

  listingImages: itemImages;
  editMode: boolean = false;
  isLoading$: Observable<boolean>;
  ListingPaymentMethod = ListingPaymentMethod;
  forAdoption$: Observable<boolean> = this.innerService.isAdoptionSelected;


  @Input() props: TempSelectedProperty[];
  items: any = [];
  constructor(
    private activatedRoute: ActivatedRoute,
    private innerService: InnerListingService
  ) {
    this.isLoading$ = this.innerService.isLoading;
  }
  ngOnDestroy(): void {
    this.innerService.disconnect();
  }

  ngOnInit(): void {

    this.innerService.disconnect();

    this.activatedRoute.params.subscribe(res => {
      if (res['id']) {
        this.editMode = true;
      }
    });


  }

  post() {

  }

  hasFeatures() {
    return this.props.filter(item => {
      return item.propTypeName == 'Boolean' && item.value != '';
    }).length > 0;
  }

}
