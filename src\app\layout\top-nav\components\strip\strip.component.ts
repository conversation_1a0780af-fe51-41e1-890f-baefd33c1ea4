import { CommonModule, NgOptimizedImage } from '@angular/common';
import { Component, Input, signal, WritableSignal } from '@angular/core';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Store } from '@ngrx/store';
import { BrowserService } from '@src/app/modules/core/service/browser.service';
import { AdsDTO, AdsSectionDTO } from '@src/app/shared/models/lookup.model';
import { AdsService } from '@src/app/shared/services/ads.service';
import { getAds } from '@src/app/store/app/selectors/app.selector';
import { combineLatest, map, Observable } from 'rxjs';
@UntilDestroy({ checkProperties: true })
@Component({
  selector: 'app-strip',
  standalone: true,
  imports: [CommonModule, NgOptimizedImage],
  templateUrl: './strip.component.html',
  styleUrl: './strip.component.scss'
})
export class StripComponent {

  @Input() hideSearch: boolean;


  isSticky: WritableSignal<boolean> = signal(false);
  topPosToStartShowing = 300;
  ads$: Observable<AdsDTO>;
  constructor(
    private store: Store,
    private adsService: AdsService,
    private browser: BrowserService

  ) {
    this.ads$ = combineLatest([this.store.select(getAds), this.adsService.adsRoute$, this.adsService.adsStripType$]).pipe(
      untilDestroyed(this),
      map(([ads, route, stripType]) => {
        const filteredAds: AdsSectionDTO[] = ads?.filter((x) => x.routeName === route && x.locationName === stripType);
        return filteredAds.length > 0 ? filteredAds[0].ads.length > 0 ? filteredAds[0].ads[0] : null : null;
      })
    );

  }

  ngOnInit() {
    if (this.browser.isBrowser()) {
      window.addEventListener('scroll', this.checkScroll);
    }
  }

  ngOnDestroy() {
    if (this.browser.isBrowser())
      window.removeEventListener('scroll', this.checkScroll);
  }

  checkScroll = () => {
    if (this.browser.isBrowser()) {
      const scrollPosition = window.scrollY || document.documentElement.scrollTop || document.body.scrollTop || 0;
      this.isSticky.set(scrollPosition >= this.topPosToStartShowing);


    }
  }


}
