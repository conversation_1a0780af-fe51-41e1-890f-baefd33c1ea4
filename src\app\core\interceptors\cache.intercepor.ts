import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ttpInterceptor, HttpRequest, HttpResponse } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";
import { tap } from "rxjs/operators";
import { CacheService } from "../services/cache.service";
import { canCacheRequest } from "../utils/cache-request-utils";

@Injectable()
export class CachingInterceptor implements HttpInterceptor {
    constructor(private cacheService: CacheService) { }

    intercept(req: HttpRequest<any>, next: HttpHandler): Observable<any> {

        const cacheKey = this.createCacheKey(req.urlWithParams, req.body);
        const cachedResponse = this.cacheService.getCache(cacheKey);
        if (cachedResponse) {
            return of(cachedResponse);
        }

        return next.handle(req).pipe(
            tap((event) => {
                if (event instanceof HttpResponse) {
                    if (canCacheRequest(req)) this.cacheService.setCache(cacheKey, { data: event, maxAge: 90000 });
                }
            })
        );
    }
    private createCacheKey(url: string, body: any): string {
        const bodyHash = this.simpleHash(JSON.stringify(body)).toString(); // with hash we can do it with only small key

        return `${url}_${bodyHash}`;
    }

    /** Generates a Hash to be appended with key */
    private simpleHash(str: string): string {
        let hash = 0;
        if (str.length === 0) return hash.toString();
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = (hash << 5) - hash + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        return hash.toString();
    }



    // cacheBust(req: HttpRequest<any>) {
    //     if (req.method == 'DELETE' || req.method == 'POST') {
    //         if (req.url.includes('API_ENDPOINT'))// EndPoint without Host
    //             this.cacheService.deleteCache(this.generateDeleteURL("API_ENDPOINT")); //Generated Hash will always be same.
    //     }
    //     return next.handle(req);
    // }

    // private generateDeleteURL(key) {
    //     const url = `${HOST}/${key}`;
    //     const genKey = this.createCacheKey(url, null);
    //     return genKey;
    // }

}