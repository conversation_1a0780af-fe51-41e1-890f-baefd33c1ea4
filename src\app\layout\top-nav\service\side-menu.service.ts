import { effect, Injectable, signal } from '@angular/core';
import { BrowserService } from '@src/app/modules/core/service/browser.service';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SideMenuService {


  private isListingDetails = new BehaviorSubject<boolean>(false);
  isListingDetails$ = this.isListingDetails.asObservable();

  private unSearchBar = new BehaviorSubject<boolean>(false);
  unSearchBar$ = this.unSearchBar.asObservable();

  openSidebar = signal(false);

  constructor(private browser: BrowserService) {
    if (this.browser.isBrowser()) {
      effect(() => {

        if (this.openSidebar()) {
          document.body.classList.add('no-scroll');
        } else {
          document.body.classList.remove('no-scroll');
        }
      });
    }

  }

  toggle() {
    this.openSidebar.set(!this.openSidebar());

  }

  set(value) {
    this.openSidebar.set(value);
  }


  close() {
    this.openSidebar.set(false);
  }

  isListingDetailsPage(value) {
    this.isListingDetails.next(value);
  }

  unSearchBarPage(value) {
    this.unSearchBar.next(value);
  }



}
