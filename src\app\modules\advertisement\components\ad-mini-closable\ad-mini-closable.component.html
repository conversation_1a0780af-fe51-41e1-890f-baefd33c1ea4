<div class="item">
  <div class="item-img">
    <img class="item_img" [lazyload]="listingItem.imageURL | listingImagePath:'myListing'"
      [placeholder]="'assets/img/placeholder/no-image.svg'" />
  </div>
  <div class="item-content">
    <h2>{{ listingItem.name }}</h2>
    <h3 [innerHTML]="listingItem.price | ncurrency"></h3>
  </div>

  <i *ngIf="isCloseable" class="pi pi-times" (click)="onClose.emit(listingItem)"></i>
</div>