@import "mixins";
@import "variables";
@import "../../style/common.scss";



.category_btn {
    display: flex;
    background: #eaeaea;
    border-radius: 82px;
    align-items: center;
    padding: 15px 25px;
    margin-bottom: 28px;
    gap: 20px;

    img {
        width: 35px;
        height: 35px;
        object-fit: contain;
    }

    h2 {
        font-size: 16px;
        font-weight: 800;
        margin: 0px;
        @include rtl2(margin-bottom, 5px);
    }

    h3 {
        font-size: 14px;
        font-weight: 400;
        margin: 0px;
    }

    .category_info {
        display: flex;
        align-items: center;
        gap: 14px;

        @include Large {
            gap: 24px;
        }


    }

    &.createMode {
        cursor: pointer;
    }


    .pi {
        @include rtl2(transform, rotate(180deg));
    }



}

.required_editor {
    border: 1px solid $orangeColor !important;

    border-radius: 9px;
    overflow: hidden;
    position: relative;

    .required {
        position: absolute;
        top: 14px;
        right: 15px;
        @include rtl2(right, auto);
        @include rtl2(left, 15px);
    }
}

p-editor {
    width: 100%;

    &::ng-deep {
        .ql-editor {
            text-align: start;
        }

        // .p-editor-container .p-editor-toolbar.ql-snow{
        //     border: none;
        // }

        // .p-editor-container .p-editor-content.ql-snow{
        //     border: none;
        // }
    }
}

.images_listio {
    position: relative;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 10px;
    margin: 20px 0px;

    @include Large {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));

    }

    &.loading:before {
        content: '';
        position: absolute;
        top: 0px;
        left: 0px;
        width: 100%;
        height: 100%;
        background: #e0e0e0c9;
        z-index: 100;
    }

    &.loading::after {
        content: '';
        display: inline-block;
        width: 28px;
        height: 28px;
        border-radius: 50%;
        margin-inline-end: 10px;
        border-top: 3px solid $primary;
        border-right: 3px solid transparent;
        box-sizing: border-box;
        animation: loaderRotation 1s linear infinite;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translateY(-50%);
        z-index: 101;
    }

}

.condition_box {
    margin-top: 20px;
}

.input_box {
    .inputField {
        border-radius: 46px;
        background: #fff;
        font-size: 16px;
        font-weight: 400;
        padding: 17px;
        color: $text-color;
        width: 100%;
        margin-bottom: 24px;
        display: flex;

        &.textareaField {
            border-radius: 20px;
            position: relative;

            .required {
                position: absolute;
                top: 30px;
                right: 15px;
                @include rtl2(right, auto);
                @include rtl2(left, 15px);
            }
        }
    }

    input,
    textarea {
        width: 100%;
        background: transparent;
        border: none;
        padding: 0px;
    }

    input {

        &::placeholder {
            font-size: 16px;
            font-weight: 400;
            color: $text-color;
        }
    }

    textarea {
        &::placeholder {
            font-size: 16px;
            font-weight: 400;
            color: $text-color;
        }

    }

}



.price {
    margin-bottom: 25px;

    ::ng-deep {

        .p-inputtext:enabled:focus {
            box-shadow: none;
            border: none;
        }

        .p-inputgroup-addon {
            background: transparent;
            border: none;
            border-radius: 0px;
            padding-inline-end: 20px;
        }

        .p-inputgroup {
            overflow: hidden;
            justify-content: flex-start;
            border-radius: 82px;
            border: 1px solid rgba(45, 49, 66, 0.20);
            padding: 17px;
            align-items: center;
        }

        .p-inputgroup input {
            padding-inline-start: 20px;
            width: 100%;
            background: transparent;
            border: none;

            &::placeholder {
                font-size: 14px;
                color: $text-color;
            }
        }
    }
}


.toggle_list {
    margin-bottom: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
        font-weight: 600;
        font-size: 18px;
    }

    ::ng-deep {
        .p-selectbutton {
            border-radius: 36px !important;
        }

        .p-selectbutton svg {
            width: 20px;
            height: 20px;
            margin-inline-end: 10px;
            flex-shrink: 0;
        }

        .p-buttonset .p-button {
            border: none;
            border-radius: 36px !important;
            overflow: hidden;
            justify-content: center;
            flex: 1;
            padding: 9px 28px;
            font-size: 14px;
            text-transform: capitalize;

            svg {
                fill: $text-color;
            }
        }

        .p-selectbutton {
            display: flex;
            border-radius: 82px;
            border: none;
            background: #fff;
            padding: 5px;
            gap: 10px;
            justify-content: space-between;
            min-height: 50px;
        }

        .p-selectbutton .p-button.p-highlight {
            background: $orangeColor;

            svg {
                fill: #fff;
            }
        }

        .select_element {
            display: flex;
        }

    }
}

.payments {
    margin-bottom: 40px;

    ::ng-deep {

        .p-radiobutton .p-radiobutton-box .p-radiobutton-icon {
            background: url(/assets/img/check-icon.svg) $orangeColor no-repeat center center;
            background-color: $orangeColor;
        }
    }

    h3 {
        font-weight: 600;
        font-size: 18px;
        margin: 40px 0px 20px;
    }

    .payment_item {
        display: flex;
        gap: 12px;
        margin-bottom: 29px;

        label {
            font-size: 16px;
        }

    }
}

.photos_list {
    display: flex;
    overflow: auto;
    flex-wrap: nowrap;
    padding: 10px 0px;
    gap: 14px;
}



.images_holder {
    margin-bottom: 40px;
    line-height: 2;

    h2 {
        font-size: 18px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        @include rtl2(font-size, 15px);
    }

    h3 {
        font-size: 14px;
        margin: 4px 0px;
        @include rtl2(font-size, 12px);
    }

    .upload-input {
        position: relative;

        .add-img {
            width: 106px;
            height: 84px;
            border-radius: 12px;
            background: url('/assets/img/add-photo.svg') no-repeat center center;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .icon {
            background-color: #8a51be;
            border-radius: 50%;
            font-size: 2rem;
            color: #ffffff;
            padding: 1rem;
        }

        input {
            font-size: 0;
            height: 100%;
            width: 100%;
            position: absolute;
            background-color: transparent;
            border: 0;
            color: transparent;
            top: 0;
            @include ltr2(padding-left, 0);
            @include rtl2(padding-right, 0);
            opacity: 0;
            cursor: pointer;
        }
    }


    .added-imgs {
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        img {
            height: 100%;
            width: 100%;
            object-fit: cover;
        }

        .img-card {
            width: 106px;
            height: 84px;
            border-radius: 12px;
            overflow: hidden;
            flex-shrink: 0;
            box-shadow: 1px 2px 6px #00000017;


            .info {
                border-bottom-right-radius: 0.25em;
                border-bottom-left-radius: 0.25em;
                display: none;
            }
        }

        .img-card {
            position: relative;

            &.cover {
                .info {
                    position: absolute;
                    bottom: 0;
                    width: 100%;
                    height: 1.2rem;
                    display: flex;
                    font-size: 0.85rem;
                    align-items: center;
                    justify-content: center;
                    background-color: rgba($color: $primary, $alpha: 0.64);
                    color: #ffffff;
                    text-align: center;
                }
            }

        }


    }
}



.edit,
.delete {
    background-color: rgb(255 255 255 / 80%);
    position: absolute;
    top: 0.3rem;
    right: 0.3rem;
    cursor: pointer;
    border-radius: 50%;
    width: 1.4rem;
    height: 1.4rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0px 0px 10px #00000042;
    z-index: 2;

    .pi {
        color: $orangeColor;
        font-size: 0.85rem;
    }
}



.cover_label {
    position: absolute;
    bottom: 0px;
    left: 0px;
    width: 100%;
    background: rgba($color: $orangeColor, $alpha: 0.5);
    color: #fff;
    padding: 2px;
    z-index: 2;
    text-align: center;
    font-size: 12px;
    opacity: 0;
    transition: all 0.3s ease-out;
    pointer-events: none;
}

.image_icon {
    // width: 19%;
    // height: 82px;
    border-radius: 14px;
    background: rgba($color: $primary, $alpha: 0.05);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    // margin: 5px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    vertical-align: top;

    &::before {
        content: '';
        display: block;
        padding-top: 100%;
    }

    svg {
        width: 30px;
        height: 30px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate3d(-50%, -50%, 0);
        pointer-events: none;
    }

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        position: absolute;
        top: 0px;
        left: 0px;
    }

    input {
        font-size: 0;
        height: 100%;
        width: 100%;
        position: absolute;
        background-color: transparent;
        border: 0;
        color: transparent;
        top: 0;
        @include ltr2(padding-left, 0);
        @include rtl2(padding-right, 0);
        opacity: 0;
        cursor: pointer;
    }

    &:hover {
        .cover_label {
            opacity: 1;
        }
    }
}

.image_icon.cover_icon {
    // width: 35%;
    // height: 174px;

    grid-column: span 2;
    grid-row: span 2;

    .cover_label {
        background: $orangeColor;
        padding: 5px;
        font-size: 15px;
        opacity: 1;
    }

    svg {
        margin-top: -10%;
    }

    // @include rtl2(float, right);
    // @include ltr2(float, left);


}





// Tags
.tags_box {
    margin-top: 20px;

    h2 {
        font-size: 1.1rem;

        span {
            font-weight: 300;
            display: inline-flex;
        }
    }
}

.predefined_tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 0px;
    padding: 0px;
    padding-right: 0px !important;
    margin-bottom: 15px;
    list-style: none;

    li {
        cursor: pointer;
        background: rgba($color: $text-color, $alpha: 0.1);
        font-size: 0.85rem;
        padding: 6px 12px;
        border-radius: 22px;
        color: $text-color;
    }

}

.tags_input {
    p-chips {
        width: 100%;
        display: block;
    }

    &::ng-deep {

        .p-chips,
        .p-chips-multiple-container {
            width: 100%;
            border: none;
        }

        .p-chips-multiple-container {
            padding: 20px;
        }

        .p-chips .p-chips-multiple-container .p-chips-token {
            background: #000;
            color: #fff;
            border-radius: 30px;
        }

        .p-chips .p-chips-multiple-container .p-chips-token .p-chips-token-icon {
            margin-left: 0px;
            margin-inline-start: 8px;
        }

        .p-chips .p-chips-multiple-container:not(.p-disabled):hover {
            border-color: transparent;
        }


        .p-chips-input-token {
            display: none;
        }

    }


}



@include Wide {

    .max-container {
        max-width: clamp(10vw, 32vw, 580px);
    }
}