@import "mixins";
@import "variables";

.floated_addbtn {
  position: fixed;
  bottom: 19px;
  right: 18px;
  color: $orangeColor;

  app-primary-btn {
    &::ng-deep {
      .primary_btn {
        color: $orangeColor;
        padding: 13px 19px 15px !important;

        @include rtl2(padding, 10px 27px 10px !important);
      }
    }
  }
}

.header_right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
  gap: 16px;

  .tempadd {
    &::ng-deep {
      img {
        display: none;
      }
    }
  }

  @include Large {
    gap: 20px;
  }
}

.auth_btns {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  transform: translateY(-2px);

  .p-button {
    font-size: 16px;
    font-weight: 600;
    line-height: normal;
    background-color: transparent;
    border: 0 !important;
    box-shadow: none !important;
    color: var(--secondary-color) !important;
    border-radius: 22px;
    padding: 0px;
    text-transform: capitalize;
    overflow: visible;
    line-height: 1;

    @include rtl2(font-size, 14px);

    &:active,
    &:hover {
      background: #e7e7e7 !important;
      border-color: #e7e7e7;
    }
  }
}


@include Medium {
  .header_right {

    .tempadd {
      &::ng-deep {
        img {
          display: inline-flex;
        }
      }
    }
  }

}

@include Large {
  .floated_addbtn {
    position: relative;
    top: 0px;
    left: 0px;
    right: auto;

    app-primary-btn {
      &::ng-deep {
        .primary_btn {
          padding: 14px 19px !important
        }
      }
    }
  }
}