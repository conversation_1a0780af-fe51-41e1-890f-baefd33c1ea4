<div class="header_right">

  <ng-container *ngIf="user$ | async ; else renderDefault">

    <app-language-button></app-language-button>

    <div class="floated_addbtn">
      <button class="mainBtn" id="top_create_lisitng_btn" pButton pRipple VerifiedClick
        (VerifiedClick)="goToCreateListing()" (unVerifiedClick)="sendEvent($event)" class="mainBtn d-lg-flex" [ngClass]="{
              'd-none' : isListingDetails$ | async
            }">
        <img [src]="'assets/img/icons/add-lisitng-icon.svg'" alt="" width="13" height="13" />
        {{ 'sell_and_swap' | translate}}
      </button>
    </div>

    <app-top-notification-icon></app-top-notification-icon>
  </ng-container>

  <app-top-profile-icon></app-top-profile-icon>


</div>

<ng-template #renderDefault>
  <div class="floated_addbtn">
    <button id="top_create_lisitng_btn" class="mainBtn d-lg-flex" pButton pRipple VerifiedClick
      (VerifiedClick)="goToCreateListing()" (unVerifiedClick)="sendEvent($event)" [ngClass]="{
  
        'd-none' : isListingDetails$ | async
       }">
      <img [src]="'assets/img/icons/add-lisitng-icon.svg'" alt="" width="13" height="13" />
      {{ 'sell_and_swap' | translate}}
    </button>
  </div>

  <app-language-button></app-language-button>


  <div class="auth_btns">
    <button pButton pRipple styleClass="p-button-text" (click)="login()" id="top_login_btn">
      {{ 'Login' | translate}}
    </button>
  </div>

</ng-template>