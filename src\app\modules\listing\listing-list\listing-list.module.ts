import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { PaginatorModule } from 'primeng/paginator';
import { RadioButtonModule } from 'primeng/radiobutton';
import { DarkBtnComponent } from 'src/app/shared/components/dark-btn/dark-btn.component';
import { ItemCardRowComponent } from 'src/app/shared/components/item-card-row/item-card-row.component';
import { NoResultComponent } from 'src/app/shared/components/no-result/no-result.component';
import { SideFilterComponent } from 'src/app/shared/components/side-filter/side-filter.component';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { ListingListRoutingModule } from './listing-list-routing.module';
import { FavouriteListingComponent } from './pages/favourite-listing/favourite-listing.component';
import { MyListingComponent } from './pages/my-listing/my-listing.component';


@NgModule({
    imports: [
        CommonModule,
        ListingListRoutingModule,
        ItemCardRowComponent,
        PaginatorModule,
        NtranslatePipe,
        NoResultComponent,
        DropdownModule,
        DialogModule,
        SideFilterComponent,
        FormsModule,
        ReactiveFormsModule,
        RadioButtonModule,
        DarkBtnComponent,
        MyListingComponent,
        FavouriteListingComponent
    ]
})
export class ListingListModule { }
