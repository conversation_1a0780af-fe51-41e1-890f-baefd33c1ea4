import { Pipe, PipeTransform } from '@angular/core';
import { TranslationService } from 'src/app/modules/core/service/translation.service';

@Pipe({
  name: 'parseprops',
  standalone: true
})
export class ParsepropsPipe implements PipeTransform {

  constructor(private translation: TranslationService) { }

  transform(value: any, parent): any {
    if (parent) {
      value = value.filter(name => name.toLowerCase().startsWith(parent.toLowerCase()));
    }
    return value.map(e => {
      return { value: e, viewValue: this.translation.instant(e) };
    });
  }

}
