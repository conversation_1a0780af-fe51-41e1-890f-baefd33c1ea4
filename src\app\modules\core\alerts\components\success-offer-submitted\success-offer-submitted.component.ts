import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { RippleModule } from 'primeng/ripple';
import { DarkBtnComponent } from 'src/app/shared/components/dark-btn/dark-btn.component';
import { GrayBtnComponent } from 'src/app/shared/components/gray-btn/gray-btn.component';
import { SuccessIconComponent } from 'src/app/shared/components/success-icon/success-icon.component';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';

@Component({
  selector: 'app-success-offer-success-offer-submitted',
  standalone: true,
  imports: [CommonModule, RippleModule, ButtonModule, SuccessIconComponent, DarkBtnComponent, GrayBtnComponent, NtranslatePipe, ReactiveFormsModule],
  templateUrl: './success-offer-submitted.component.html',
  styleUrls: ['./success-offer-submitted.component.scss']
})
export class SuccessOfferSubmittedComponent implements OnInit {

  message: string;
  btns = [];
  form = new FormGroup({
    message: new FormControl("", [Validators.required, Validators.minLength(3)]),
  });

  constructor(private ref: DynamicDialogRef, public config: DynamicDialogConfig) { }

  ngOnInit(): void {
    this.message = this.config.data.message;
    if (this.config.data.buttons) {
      this.btns = this.config.data.buttons;
    }
  }

  close(value) {
    if (this.ref) {
      this.ref.close({
        value,
        message: this.form.value.message,
      });
    }
  }

}
