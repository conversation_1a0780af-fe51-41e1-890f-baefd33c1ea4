import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AlertHandlerService } from 'src/app/modules/core/alerts/alert-handler.service';
import { InnerListingService } from 'src/app/modules/listing/services/inner-listing.service';
import { SecondaryBtnComponent } from 'src/app/shared/components/secondary-btn/secondary-btn.component';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';

@Component({
  selector: 'app-form-listing-details-swapp-option',
  standalone: true,
  imports: [CommonModule, SecondaryBtnComponent, NtranslatePipe],
  templateUrl: './form-listing-details-swapp-option.component.html',
  styleUrls: ['./form-listing-details-swapp-option.component.scss']
})
export class FormListingDetailsSwappOptionComponent implements OnInit {
  @Output() onSubmit = new EventEmitter();
  @Input() currentOption: number = 0;
  selectedOption: number = 0;

  constructor(
    private alertService: AlertHandlerService,
    public innerService: InnerListingService
  ) { }

  ngOnInit(): void {
    if (this.currentOption) {
      this.selectedOption = this.currentOption;
    }
  }



  moveNext() {
    if (this.selectedOption == 1) {
      this.onSubmit.emit(1);
    } else if (this.selectedOption == 2) {
      this.onSubmit.emit(2);
    } else {
      this.alertService.message({ message: "Please choose what do you want to swap with " });
    }
  }

}
