@import "variables";
@import "mixins";
@import "../../style/common.scss";

.tabHeader {
  display: flex;
  font-size: 20px;
  font-weight: 600;
  align-items: center;
  color: $text-color;
  padding: 15px 20px 10px;
  gap: 14px;
  @include rtl2(font-size, 17px);
}

.editMode {
  pointer-events: none;
  opacity: 0;
}

.back_holder {
  position: relative;
  transform: rotate(var(--arrow-rotation));

  &.active {
    .over_btn {
      display: block;
    }
  }
}

.over_btn {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  z-index: 2;
  cursor: pointer;
  display: none;
}

.mainmenu {
  @include XLarge {
    min-width: 350px;
  }
}

.menu_box {
  position: relative;

  &>div {
    padding: 0px;
  }

  &.active {
    .mainmenu {
      display: none;
    }

    .submenu {
      top: 0px;
      left: 0px;
      z-index: 2;
      width: 100%;
    }
  }

  &.visible {
    .mainmenu {
      display: block !important;
    }

    .submenu {
      display: none !important;
    }
  }
}

.side_image {
  display: none;
  justify-content: center;
  align-items: center;
  height: 100%;
  flex-direction: column;
  gap: 15px;
  max-width: max-content;
  margin-inline: auto;
  text-align: center;

  img {
    width: 75%;
  }

  h2 {
    font-size: 14px;
  }

  @include Large {
    display: flex;
  }

}

.form_area {
  padding: 0px 15px;
}



.select_main_category {
  font-size: 18px;
  font-weight: 800;
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 0px 20px;
  margin: 20px 0px;

  @include rtl2(font-size, 16px);

  img {
    width: 42px;
    height: 42px;
  }
}

.modelTop {
  background: linear-gradient(180deg, rgba(250, 175, 64, 0) 0%, rgba(250, 175, 64, 0.17) 100%);
  padding: clamp(50px, 15vw, 117px) 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  app-success-icon {
    position: absolute;
    top: -70px;
    left: 50%;
    transform: translateX(-50%);
  }
}

.model_actions {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.success_img {
  width: 100%;
  margin-top: 20px;
}

.modalTitle {
  font-size: 30px;
  font-weight: 800;
}

.modelContent {
  font-size: 22px;
  font-weight: 400;
  text-align: center;
}

.menuitems {
  list-style: none;
  padding: 0px;
  margin: 0px;
}

.menuitem {
  margin-bottom: 0px;

  &__link {
    padding: 12px 24px !important;
    padding: 0;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    gap: 24px;
    box-shadow: none !important;

    &:hover,
    &.active {
      background: linear-gradient(270deg, rgba(250, 175, 64, 0) 0%, rgb(242 101 34 / 11%) 100%);
      position: relative;

      &:after {
        content: "";
        position: absolute;
        width: 4px;
        height: 50%;
        background: var(--primary-color);
        left: 0px;
        top: 50%;
        transform: translateY(-50%);
        border-top-left-radius: 3px;
        border-bottom-left-radius: 3px;
      }
    }
  }

  &__icon {
    flex-shrink: 0;

    img {
      width: 32px;
      height: 32px;
      object-fit: contain;
    }
  }

  &__text {
    font-size: 16px;
    font-weight: 400;
    color: $text-color;
  }


}

.submit {
  padding-top: 30px;
}

@include Large {

  .back_holder {
    .over_btn {
      display: none;
    }
  }

  .menuitems {
    overflow: auto;
    height: 100%;
  }

  .menu_box {
    border-radius: 12px;
    background: #fff;
    height: calc(100vh - 260px);

    >div {
      padding: 20px;
      height: 100%;
      overflow: auto;

      &:first-child {
        flex-grow: unset;
        width: auto;
        border-inline-end: 2px solid rgba(45, 49, 66, 0.11);
      }

      &:last-child {
        flex: 1;
        justify-content: center;
        align-items: center;
      }
    }


    &.active {

      .mainmenu {
        display: block;
      }

      .submenu {
        position: relative;
        top: 0px;
        left: 0px;
        z-index: 2;
        height: calc(100% - 90px);
      }
    }
  }
}

.submit_area {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.sub_category_menu {
  display: flex;

  .sub_category_menu_list {
    width: 100%;
    height: 100%;

    @include Large {
      width: auto;
    }

    @include XLarge {
      min-width: 350px;
    }
  }
}


@include Wide {

  .max-container {
    max-width: clamp(10vw, 32vw, 580px);
  }
}