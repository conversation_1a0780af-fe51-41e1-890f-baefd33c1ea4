import { CommonModule } from '@angular/common';
import { Component, ElementRef, HostListener, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Store } from '@ngrx/store';
import { AppSignalRService } from '@src/app/core/services/signalr.service';
import { NotificationsHeaderComponent } from '@src/app/modules/notification/components/notifications-header/notifications-header.component';
import { MenuItem } from 'primeng/api';
import { Menu, MenuModule } from 'primeng/menu';
import { Subscription } from 'rxjs';
import { LazyloadDirective } from 'src/app/shared/directives/lazyload.directive';
import { NotificationDto, NotificationEntityType } from 'src/app/shared/models/notification.model';
import { FromNowPipe } from 'src/app/shared/pipes/from-now.pipe';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { loadNotifications } from 'src/app/store/app/actions/app.actions';
import { getNotifications, getTotalUnseenNotifications } from 'src/app/store/app/selectors/app.selector';
import { SideMenuService } from '../../service/side-menu.service';

@UntilDestroy({ checkProperties: true })
@Component({
  selector: 'app-top-notification-icon',
  standalone: true,
  imports: [CommonModule, RouterModule, FromNowPipe, LazyloadDirective, NtranslatePipe, MenuModule, NotificationsHeaderComponent],
  templateUrl: './top-notification-icon.component.html',
  styleUrls: ['./top-notification-icon.component.scss'],
})
export class TopNotificationIconComponent implements OnInit, OnDestroy {

  @ViewChild('op', { static: true }) op: Menu;
  @ViewChild('notButton', { static: false }) notButton: ElementRef;

  items: MenuItem[] = [];
  notifications: NotificationDto[] = [];

  NotificationEntityType = NotificationEntityType;

  totalUnSeen: number = 0;

  receivedMessage: any;

  isRead: boolean = false;

  constructor(
    private store: Store,
    private notificationService: NotificationService,
    private router: Router,
    private menuService: SideMenuService,
    private signalRService: AppSignalRService

  ) { }

  @HostListener('document:click', ['$event'])
  onClickOutside(event: Event) {
    this.clickedOutside(event);
  }


  closeMenu() {
    this.menuService.close();
  }

  ngOnInit(): void {


    this.store.select(getNotifications).pipe(untilDestroyed(this)).subscribe(res => {
      this.notifications = res;
    });

    this.store.select(getTotalUnseenNotifications).pipe(untilDestroyed(this)).subscribe(res => {
      this.totalUnSeen = res;
    });


    this.loadNotificaitons();

    this.signalRService.getMessages().pipe(untilDestroyed(this)).subscribe((msg) => {
      if (msg) {
        if (msg.indexOf("target") >= 0) {
          this.loadNotificaitons();
        }
      }
    });



  }

  notificationSub$: Subscription;


  trackby(index, item) {
    return item.id;
  }

  loadNotificaitons() {
    this.store.dispatch(loadNotifications({ isRead: !this.isRead }));
  }

  seen(id, route) {
    this.notificationService.seen(id).subscribe(res => {
      this.loadNotificaitons();
    });



    if (route.queryParams) {
      this.router.navigate([route.url], { queryParams: route.queryParams });
    } else {
      this.router.navigate([route.url]);

    }

  }

  clickedOutside(event: Event) {
    if (
      this.notButton && !this.notButton.nativeElement.contains(event.target) &&
      this.op && !this.op.el.nativeElement.contains(event.target)
    ) {
      this.op.hide();
    }

  }

  ngOnDestroy(): void {
    this.signalRService.closeConnection();
  }


}
