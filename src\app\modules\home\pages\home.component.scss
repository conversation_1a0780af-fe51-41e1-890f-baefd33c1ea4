@import "variables";
@import "mixins";

.home-top-widgets {

  @include Large {
    background: linear-gradient(180deg, #f7f7fb 30%, rgba(45, 49, 66, 0.06) 50%, rgba(45, 49, 66, 0.06) 100%);
  }

}

.home_sections_box {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

ul {
  list-style: none;
  padding: 0;

  li {
    margin-bottom: 0.5rem;

    a {
      color: $text-muted-2;
      font-size: 0.9rem;
    }
  }

  .show-more {
    color: $primary;
    font-size: 0.9rem;
    cursor: pointer;

    .icon {
      font-size: 0.75rem;
    }
  }
}

.slider_box {
  padding: 0px 15px;
}

.form-check-input {
  height: 1em;
  border-radius: 50%;
}

input {
  background-color: $grey-2;
  border: 0;
}

.categories {
  li {
    @include ltr2(padding-left, 0.5rem);
    @include rtl2(padding-right, 0.5rem);

    &:hover {
      background-color: $secondary;
    }

    &.active,
    &:focus,
    &:active {
      background-color: $primary;

      a {
        color: #ffffff;
      }
    }
  }
}

.conditions {
  a {
    &:hover {
      background-color: $side-nav-link-active-color;
      border: 1px solid $side-nav-link-active-color;
      color: $primary;
    }

    &.active,
    &:focus,
    &:active {
      background-color: $side-nav-link-active-color;
      border: 1px solid $side-nav-link-active-color;
      color: $primary;
    }
  }
}

@include Large {
  .side-filters {
    max-width: 320px;
  }

  .side_filters {
    max-width: 320px;
  }
}

.side-filters {
  &.collapse:not(.show) {
    display: block;
  }
}

.side_filters {
  &.collapse:not(.show) {
    display: block;
  }
}

@media (max-width: 767.98px) {
  .side-filters {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99;
    background-color: #ffffff;
    overflow-y: auto;
    padding-bottom: 1rem;
    max-height: 100vh;
    max-width: 85vw;

    .inner {
      width: 80vw;
    }

    &.collapse:not(.show) {
      display: none;
    }
  }

  .side_filters {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99;
    background-color: #ffffff;
    overflow-y: auto;
    padding-bottom: 1rem;
    max-height: 100vh;
    max-width: 85vw;
    bottom: 0;

    .inner {
      width: 80vw;
      padding: 20px;
    }

    &.collapse:not(.show) {
      display: none;
    }
  }
}

.side-filters {

  li,
  h2 {
    padding-left: 0;
    text-transform: capitalize;
  }
}

.side_filters {

  li,
  h2 {
    padding-left: 0;
    text-transform: capitalize;
  }
}