import { makeStateKey } from "@angular/core";
import { TranslateLoader } from "@ngx-translate/core";
import { Observable, of } from "rxjs";
import { DataService } from "../services/data.service";

const dataKey = makeStateKey<{ data: string }>("data");

export class LowerCaseLoader implements TranslateLoader {

    constructor(private data: DataService) {
    }

    getTranslation(lang: string): Observable<any> {
        return of(this.data?.value?.languagesData);

    }
}