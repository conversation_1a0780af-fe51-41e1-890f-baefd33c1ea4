<div [formGroup]="form" [ngClass]="{'swapp_interest_box' : innerService.onePageMode}">
  <div class="interest_titles" *ngIf="innerService.onePageMode">
    <h2>{{ 'Swap With' | translate }}</h2>
  </div>
  <div class="form_box">
    <div class="inputField">
      <input type="text" [placeholder]="'Specific Item' | translate" formControlName="treadBy" />
    </div>
    <app-form-interest-control formControlName="interestIds"></app-form-interest-control>
  </div>
</div>
<div class="submit col-md-6 offset-md-6" *ngIf="!innerService.onePageMode">
  <app-secondary-btn [btnDisabled]="!form.valid" [btnText]="'Next' | translate" btnType="button" [bigBtn]="true"
    (click)="saveData()"></app-secondary-btn>
</div>