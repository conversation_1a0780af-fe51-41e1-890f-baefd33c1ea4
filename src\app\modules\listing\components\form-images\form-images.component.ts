import { CommonModule } from '@angular/common';
import { Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { HeicConversionService } from '@src/app/core/services/heic.service';
import { Subscription } from 'rxjs';
import { AlertHandlerService } from 'src/app/modules/core/alerts/alert-handler.service';
import { BrowserService } from 'src/app/modules/core/service/browser.service';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { ListingImagePathPipe } from 'src/app/shared/pipes/listing-image-path.pipe';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { ListingService } from 'src/app/shared/services/listing.service';
import { FormUtils } from 'src/utils/form-utils';
import { itemImages } from '../../models/item.details.model';
import { InnerListingService } from '../../services/inner-listing.service';

@Component({
  selector: 'app-form-images',
  standalone: true,
  imports: [CommonModule, NtranslatePipe, ListingImagePathPipe],
  templateUrl: './form-images.component.html',
  styleUrls: ['./form-images.component.scss']
})
export class FormImagesComponent implements OnInit, OnDestroy {
  @ViewChild('target') targetHost: ElementRef<HTMLInputElement>;
  @Output() onSubmit = new EventEmitter();

  @Input() details: itemImages;
  urls: any[] = [];
  coverURL: any = {};
  filesUploaded: any[] = [];
  deletedImages: number[] = [];
  maxImages: number = 15;
  maxSize = 5 * 1024 * 1024;
  maxWidth = 1000;
  loadingimage = false;
  watcher: Subscription;


  constructor(
    private alertService: AlertHandlerService,
    private translateService: TranslationService,
    private listingService: ListingService,
    public innerService: InnerListingService,
    private browser: BrowserService,
    private hcs: HeicConversionService
  ) { }


  ngOnDestroy(): void {
    this.watcher.unsubscribe();
  }

  ngOnInit(): void {
    if (this.details != null && this.details.urls && this.details.urls.length > 0) {
      this.urls = this.details.urls.map(e => {
        e.name = e.url;
        return e;
      });
    }


    if (this.details != null && this.details.filesUploaded && this.details.filesUploaded.length > 0) {
      this.filesUploaded = this.details.filesUploaded;
    }
    if (this.details != null && this.details.coverURL && this.details.coverURL.url) {
      this.coverURL = this.details.coverURL;
    }

    this.watcher = this.innerService.fetch.subscribe(res => {
      if (!res) return;
      this.saveData();
    });

    this.watcher = this.innerService.fetch.subscribe(res => {
      if (!res) return;
      this.saveData();
    });

  }

  async onSelectFile(event: any, isCover = false) {

    if (this.browser.isBrowser()) {



      if (event.target.files && event.target.files[0]) {


        var remainingSlots = this.maxImages - this.urls.length;
        var filesAmount = Math.min(event.target.files.length, remainingSlots);

        for (let i = 0; i < filesAmount; i++) {




          const uid = this.generateUid();

          const file = event.target.files[i];

          let convertedFile;

          if (!FormUtils.validImageFile(file)) {
            this.alertService.warn({ message: this.translateService.instant('Invalid_Extension') });
            return;
          }

          var validHic = FormUtils.validHicFile(file);

          if (validHic) {
            convertedFile = await this.hcs.convertIfHeic(file);
          }


          var reader = new FileReader();

          reader.onload = (event: any) => {


            if (isCover) {
              this.coverURL = {
                url: event.target.result,
                name: this.filesUploaded[0]?.name,
                uid: uid
              };
            }




            if (this.urls.length < this.maxImages) {

              this.urls.push({
                url: event.target.result,
                name: this.filesUploaded[i]?.name,
                uid: uid
              });

              this.filesUploaded.push({
                uid: uid,
                element: validHic ? convertedFile : file
              });

              if (this.urls.length == filesAmount) {
                if (!this.coverURL.url) {
                  this.coverURL = { ...this.urls[0] };
                }

              }
            }

          };

          reader.readAsDataURL(validHic ? convertedFile : event.target.files[i]);

        }







        event.target.value = "";

      }

    }

  }

  removeImage(image: any) {
    let index = this.urls.indexOf(image);
    if (this.filesUploaded) {
      for (let index = 0; index < this.filesUploaded.length; index++) {
        if (this.filesUploaded[index].uid == image.uid) {
          let array = Array.from(this.filesUploaded);
          array.splice(index, 1);
          this.filesUploaded = array;
        }
      }
    }
    this.deletedImages.push(image.id);

    if (this.coverURL.uid && this.coverURL.uid == image.uid) this.coverURL = {};

    this.urls.splice(index, 1);

    if (this.urls.length > 0 && !this.coverURL.uid) {
      this.coverURL = this.urls[0];
    }
  }


  saveData() {




    if (this.urls.length == 0) {
      this.browser.scrollTo({
        behavior: 'smooth',
        top: this.targetHost.nativeElement.offsetTop - 100,
      });
      this.innerService.hasImagesError = true;
      return;
    }

    this.innerService.hasImagesError = false;

    this.onSubmit.emit({ urls: this.urls, filesUploaded: this.filesUploaded, coverURL: this.coverURL, deletedImages: this.deletedImages });
  }

  generateUid() {
    return new Date().getTime() + '-' + Math.floor(Math.random() * 1000000);
  }

}
