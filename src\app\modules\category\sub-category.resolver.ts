import { inject } from "@angular/core";
import { ActivatedRouteSnapshot, ResolveFn, RouterStateSnapshot } from "@angular/router";
import { CategoryMenuDTO } from "@src/app/shared/models/lookup.model";
import { LookupService } from "@src/app/shared/services/lookup.service";
import { EMPTY, map, mergeMap, of, switchMap, take } from "rxjs";

export const subCategoryResolver: ResolveFn<CategoryMenuDTO> = (
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
) => {
    const categoryService = inject(LookupService);
    const categoryId = route.params['sub'];
    return categoryService.getCategoryDetails(categoryId).pipe(
        take(1),
        map(res => res.data),
        switchMap(res => {
            if (res.parentId != null) {
                return categoryService.getCategoryDetails(res.parentId).pipe(map(item => item.data));
            }
            return of(res);
        }),
        mergeMap(data => {
            if (data) {
                return of(data)
            } else {
                return EMPTY;
            }
        })
    );
};