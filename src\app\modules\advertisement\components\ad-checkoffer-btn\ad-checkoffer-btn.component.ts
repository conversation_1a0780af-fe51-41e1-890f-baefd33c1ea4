import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { RippleModule } from 'primeng/ripple';
import { Observable } from 'rxjs';
import { OfferDetails, OffersView } from 'src/app/shared/models/offer.model';
import { NCurrencyPipe } from 'src/app/shared/pipes/ncurrency.pipe';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { OfferService } from 'src/app/shared/services/offer.service';

@Component({
  selector: 'app-ad-checkoffer-btn',
  standalone: true,
  imports: [CommonModule, RippleModule, ButtonModule, NtranslatePipe, NCurrencyPipe, RouterModule],
  templateUrl: './ad-checkoffer-btn.component.html',
  styleUrls: ['./ad-checkoffer-btn.component.scss']
})
export class AdCheckofferBtnComponent implements OnInit {

  @Input() offer!: OffersView | undefined;
  offer$: Observable<OfferDetails>;

  constructor(
    private offerService: OfferService
  ) { }

  ngOnInit(): void {
    // if(this.offer){
    //   this.offer$ = this.offerService.getOffer(this.offer.id).pipe(map(res => res.data)
    //   );
    // }
  }

  viewOffer() {

  }

}
