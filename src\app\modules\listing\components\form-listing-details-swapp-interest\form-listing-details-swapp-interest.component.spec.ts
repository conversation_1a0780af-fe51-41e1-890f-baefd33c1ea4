import { ComponentFixture, TestBed } from '@angular/core/testing';

import { FormListingDetailsSwappInterestComponent } from './form-listing-details-swapp-interest.component';

describe('FormListingDetailsSwappInterestComponent', () => {
  let component: FormListingDetailsSwappInterestComponent;
  let fixture: ComponentFixture<FormListingDetailsSwappInterestComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ FormListingDetailsSwappInterestComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(FormListingDetailsSwappInterestComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
