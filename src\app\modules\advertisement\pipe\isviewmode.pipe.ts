import { ChangeDetectorRef, Pipe, PipeTransform } from '@angular/core';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Store } from '@ngrx/store';
import { User } from '@src/app/authentication/models/dto/user.dto.model';
import { ListingDetails } from '@src/app/shared/models/listing.model';
import { getUser } from '@src/app/store/app/selectors/app.selector';
@UntilDestroy({ checkProperties: true })
@Pipe({
    name: 'isViewMode',
    standalone: true,
    pure: false
})
export class IsViewModePipe implements PipeTransform {

    private currentUser!: User;


    constructor(private store: Store,
        private cd: ChangeDetectorRef
    ) {
        this.store.select(getUser).pipe(untilDestroyed(this)).subscribe(res => {
            if (res) {
                this.currentUser = res;
                this.cd.markForCheck();
            }
        });
    }

    transform(value: ListingDetails): any {
        if (this.currentUser && this.currentUser.id == value.userID) {
            this.cd.markForCheck();
            return true;
        }
        return false;
    }
}