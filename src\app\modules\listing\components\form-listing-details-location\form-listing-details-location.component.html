<form [formGroup]="form" autocomplete="off" #target>
  <div class="location_page">
    <div class="location_box">
      <h2>{{ 'City' | translate }}<b *ngIf="hasError('location' , 'required')" class="required">*</b></h2>
      <ng-container *ngIf="areas$ | async as areas">
        <p-dropdown [ngClass]="{'required_drop' : hasError('location' , 'required')}" [options]="locationsList"
          formControlName="biglocation" [placeholder]="'City' | translate" [filter]="true" optionValue="id"
          [emptyFilterMessage]="'no_result_found' | translate" optionLabel="name" (onChange)="onChooseCity($event)">
          <ng-template let-item pTemplate="selectedItem">

            <div class="flex align-items-center gap-2 selectDrop">

              <div>
                {{ item.name }}
              </div>
            </div>
          </ng-template>
          <ng-template let-item pTemplate="item">
            <div class="flex align-items-center gap-2">
              <div>{{ item.name }}</div>
            </div>
          </ng-template>
        </p-dropdown>

      </ng-container>
    </div>
    <div class="location_box">
      <h2>{{ 'Area' | translate }}<b *ngIf="hasError('location' , 'required')" class="required">*</b></h2>
      <ng-container *ngIf="areas$ | async as areas">
        <p-dropdown [ngClass]="{'required_drop' : hasError('location' , 'required')}" [options]="locations"
          formControlName="location" optionValue="id" optionLabel="name" [filter]="true"
          [placeholder]="'Area' | translate" [emptyFilterMessage]="'no_result_found' | translate"
          [emptyMessage]="'no_result_found' | translate">
          <ng-template let-item pTemplate="noResult">


          </ng-template>
          <ng-template let-item pTemplate="selectedItem">

            <div class="flex align-items-center gap-2 selectDrop">

              <div>
                {{ item.name }}
              </div>
            </div>
          </ng-template>
          <ng-template let-item pTemplate="item">
            <div class="flex align-items-center gap-2">
              <div>{{ item.name }}</div>
            </div>
          </ng-template>

        </p-dropdown>
      </ng-container>
    </div>
  </div>

</form>