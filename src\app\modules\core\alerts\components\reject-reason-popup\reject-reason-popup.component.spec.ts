import { ComponentFixture, TestBed } from '@angular/core/testing';

import { RejectReasonPopupComponent } from './reject-reason-popup.component';

describe('RejectReasonPopupComponent', () => {
  let component: RejectReasonPopupComponent;
  let fixture: ComponentFixture<RejectReasonPopupComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RejectReasonPopupComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(RejectReasonPopupComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
