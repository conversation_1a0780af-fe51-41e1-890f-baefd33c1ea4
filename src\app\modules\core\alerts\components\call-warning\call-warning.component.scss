@import "variables";
@import "mixins";

.top_image {
    position: absolute;
    top: -70px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 120px;
    object-fit: contain;
    border-radius: 50%;
    border: 1px solid #ececec;
    background-color: #fff;

    img {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
}

.phone_txt {
    @include rtl2(direction, ltr);
}

.phone_txt_only {
    display: block;
    text-align: center;
    width: 100%;
}

.call_btn {
    display: flex;
    justify-content: space-between;
}

.call_txt svg {
    width: 16px !important;
    height: 16px !important;
    display: inline-block;
    margin: 0px !important;
}

.call_btn {
    color: #fff;
    padding: 15px;
    font-size: 1rem;
}

.call_theme h2 {
    font-size: 1.2rem !important;
    font-weight: bold;
    line-height: 2rem;
}

.call_theme {
    padding-top: 4rem;
    line-height: 2rem;
}