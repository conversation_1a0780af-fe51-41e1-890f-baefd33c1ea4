<div class="container">
    <ng-container>
        <div class="banner_wrapper">
            <div class="header_image">
                <picture>
                    <source media="(min-width: 992px)" [srcset]="mobilebanner">
                    <img [alt]="parentCategory.label" [lazyload]="desktopbanner"
                        [placeholder]="device.isMobile ? 'assets/img/placeholder/mobilebanner.jpg?v=1.1.2' : 'assets/img/placeholder/desktopbanner.jpg?v=1.1.2'">
                </picture>
                <h2>{{ parentCategory.label }}</h2>
            </div>
        </div>
    </ng-container>
</div>

<ng-container *ngIf="subCategoryId && !device.isDesktop">
    <div *ngIf="subCategories$ | async as subCategories" class="sub_categories_list">
        <div class="container list">
            <a [routerLink]="['/category/' + parentCategory.id]">
                {{ parentCategory.label }}
            </a>
            <svg data-id="arrow_icon" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                class="w-6 h-6">
                <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
            </svg>
            <a *ngIf="selectedCategory">
                {{ categoryPageTitle | translate }}
            </a>
        </div>
    </div>
</ng-container>