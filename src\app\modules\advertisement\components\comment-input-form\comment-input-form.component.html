<div [ngSwitch]="editMode">
    <div *ngSwitchCase="true">
        <form class="add-comment">
            <input [(ngModel)]="title" type="text" name="replycomment" (keyup.enter)="submit()"
                [placeholder]="placeholder" />

            <div [ngSwitch]="loading">
                <div *ngSwitchCase="true">
                    <app-loader-icon></app-loader-icon>
                </div>
                <div *ngSwitchDefault>
                    <span VerifiedClick (VerifiedClick)="submit()">
                        <svg data-id="send_icon" viewBox="0 0 19 19" fill="none">
                            <path
                                d="M18.2279 0.772511C17.88 0.421995 17.4399 0.177213 16.9585 0.0665134C16.4772 -0.044186 15.9744 -0.0162743 15.5083 0.147016L2.17944 4.75702C1.48483 4.99719 0.914634 5.43435 0.52759 6.02356C0.173136 6.57316 -0.0103171 7.21557 0.00044801 7.86947C0.0112131 8.52338 0.215712 9.1594 0.588066 9.69704C0.995844 10.2724 1.57987 10.6923 2.28138 10.9083L5.23951 11.8223C5.4328 11.8825 5.63888 11.8886 5.83537 11.8398C6.03186 11.7911 6.21123 11.6894 6.35399 11.5459L8.72809 9.17176C8.87404 9.02581 9.072 8.94381 9.27841 8.94381C9.48483 8.94381 9.68279 9.02581 9.82874 9.17176C9.9747 9.31772 10.0567 9.51568 10.0567 9.7221C10.0567 9.92851 9.9747 10.1265 9.82874 10.2724L7.45464 12.6465C7.31111 12.7893 7.20945 12.9687 7.16071 13.1652C7.11197 13.3616 7.11801 13.5677 7.17818 13.761L8.09223 16.7192C8.30821 17.4207 8.72809 18.0047 9.30347 18.4125C9.83976 18.788 10.4769 18.9927 11.1316 19H11.1869C11.8223 19.0021 12.4446 18.8184 12.9769 18.4712C13.5661 18.0842 14.0033 17.514 14.2435 16.8194L18.8534 3.49048C19.0164 3.02459 19.0441 2.5221 18.9334 2.04112C18.8227 1.56013 18.5781 1.12031 18.2279 0.772511Z"
                                fill="#722282" />
                        </svg>

                    </span>
                </div>
            </div>
        </form>
    </div>

    <div *ngSwitchDefault>
        <div *isAuthorized="true" [innerHTML]="text| linkParser : 'Phonenumber' | async"
            (click)="clickOnComment($event)" VerifiedClick></div>

        <div *isAuthorized="false" [innerHTML]="text| linkParser : 'loginPhonenumber' | async"
            (click)="clickOnComment($event)" VerifiedClick></div>
    </div>
</div>