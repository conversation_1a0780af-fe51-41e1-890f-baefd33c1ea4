import { Injectable } from '@angular/core';
import { BrowserService } from './browser.service';
// import { GoogleAES } from './data/google-analytics-events.service';

@Injectable({
  providedIn: 'root'
})
export class AddToHomeService {

  promptIntercepted = false;
  isStandalone = false;
  deferredPrompt;
  userInstalled = false;
  whereIsShare = 'bottom';

  // user agent
  isChrome = false;
  isExplorer = false;
  isExplorer_11 = false;
  isFirefox = false;
  isSafari = false;
  isOpera = false;
  isEdgeDesktop = false;
  isEdgeiOS = false;
  isEdgeAndroid = false;
  userAgent = '';

  isIOS = false;
  isMobile = false;

  promptSaved = false;
  customButtonClicked = false;
  deferredPromptShown = false;
  deferredPromptRejected = false;

  // Detects if device is in standalone mode
  // isInStandaloneMode = () => ('standalone' in window.navigator);

  // constructor (public gas: GoogleAES) { }  // if using Google Analytics
  constructor(
    private browser: BrowserService
  ) { }

  checkUserAgent() {

    if (this.browser.isBrowser()) {
      this.userAgent = navigator.userAgent.toLowerCase();
      const uaString = this.userAgent;

      this.isChrome = /chrome/.test(uaString);
      this.isExplorer = /msie/.test(uaString);
      this.isExplorer_11 = /rv:11/.test(uaString);
      this.isFirefox = /firefox/.test(uaString);
      this.isSafari = /safari/.test(uaString);
      this.isOpera = /opr/.test(uaString);
      this.isEdgeDesktop = /edge/.test(uaString);
      this.isEdgeiOS = /edgios/.test(uaString);
      this.isEdgeAndroid = /edga/.test(uaString);

      this.isIOS = /ipad|iphone|ipod/.test(uaString);
      this.isMobile = /mobile/.test(uaString);
      if ((this.isChrome) && (this.isSafari)) { this.isSafari = false; }
      if ((this.isChrome) && ((this.isEdgeDesktop) ||
        (this.isEdgeiOS) ||
        (this.isEdgeAndroid))) { this.isChrome = false; }
      if ((this.isSafari) && ((this.isEdgeDesktop) ||
        (this.isEdgeiOS) ||
        (this.isEdgeAndroid))) { this.isSafari = false; }
      if ((this.isChrome) && (this.isOpera)) { this.isChrome = false; }

      if (/ipad/.test(uaString)) {
        this.whereIsShare = 'top';
      }

    }

  }

  trackStandalone() {
    if (this.checkStandalone()) {
      this.isStandalone = true;
    }
  }

  checkStandalone(): boolean {
    if (this.browser.isBrowser()) {
      return (window.matchMedia('(display-mode: standalone)').matches);
    }
    return false;
  }

  trackInstalled() {
    this.userInstalled = true;
  }

  addToHomeScreen() {
    this.customButtonClicked = true;

    if (!this.deferredPrompt) {
      return;
    }

    this.deferredPrompt.prompt();
    this.deferredPromptShown = true;

    this.deferredPrompt.userChoice
      .then((choiceResult) => {

        if (choiceResult.outcome === 'accepted') {
          // no matter the outcome, the prompt cannot be reused ON MOBILE
          // for 3 months or until browser cache is cleared?
        } else {
          this.deferredPromptRejected = true;
        }

      });
  }

  showHide(checkWhat: boolean) {
    if (checkWhat) {
      return 'block';
    } else {
      return 'none';
    }
  }

  browserPromptBtn() {
    if (this.promptIntercepted && !this.userInstalled) {
      return 'block';
    } else {
      return 'none';
    }
  }

  iOSSafariHow2() {
    if (this.isSafari && this.isIOS && !this.isStandalone) {
      return 'block';
    } else {
      return 'none';
    }
  }


  showHideButton_iOS() {
    if (this.isIOS && !this.userInstalled) {
      return 'block';
    } else {
      return 'none';
    }
  }

  trueOrFalse(checkWhat: boolean) {
    if (checkWhat) {
      return 'green';
    } else {
      return 'red';
    }
  }

}