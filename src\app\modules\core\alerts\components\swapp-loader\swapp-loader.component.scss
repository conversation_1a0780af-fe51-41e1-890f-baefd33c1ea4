/* HTML: <div class="loader"></div> */
.loader {
  border-radius: 50%;
  background: #ffffff2d;
  box-shadow: 0 0 0 0 #73258396;
  animation: l2 1.5s infinite linear;
  position: relative;
  width: 60px;
  height: 60px;
  padding: 5px;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.loader:before,
.loader:after {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  box-shadow: 0 0 0 0 rgb(242 104 37 / 55%);
  animation: inherit;
  animation-delay: -0.5s;
}

.loader:after {
  animation-delay: -1s;
}

span {
  position: absolute;
  bottom: -50px;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
  text-wrap: nowrap;
  font-weight: bold;
}

@keyframes l2 {
  100% {
    box-shadow: 0 0 0 40px #0000
  }
}