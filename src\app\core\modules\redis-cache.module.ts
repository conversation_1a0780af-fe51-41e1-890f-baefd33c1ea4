import { InjectionToken, NgModule } from "@angular/core";
import { createKeyv } from '@keyv/redis';
import { environment } from "@src/environments/environment";
import { Cacheable } from 'cacheable';

export const CACHE_INSTANCE = new InjectionToken('redis cache instance');

@NgModule({
    providers: [
        {
            provide: CACHE_INSTANCE,
            useFactory: () => {
                const secondaryStore = createKeyv(environment.localRedis ? 'redis://:@localhost:6379' : 'redis://:<EMAIL>:6379');
                return new Cacheable({ secondary: secondaryStore, ttl: '5m' });
            }
        }
    ],

})
export class RedisCacheModule {

}