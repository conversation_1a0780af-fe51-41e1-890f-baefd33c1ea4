import { CommonModule } from '@angular/common';
import { afterNextRender, Component, OnInit } from '@angular/core';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { ButtonModule } from 'primeng/button';
import { RippleModule } from 'primeng/ripple';
import { catchError, map, Observable, of } from 'rxjs';
import { SuccessIconComponent } from 'src/app/shared/components/success-icon/success-icon.component';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { AuthService } from 'src/app/shared/services/auth.service';

@Component({
  selector: 'app-email-confirm',
  templateUrl: './email-confirm.component.html',
  styleUrls: ['./email-confirm.component.scss'],
  standalone: true,
  imports: [CommonModule, SuccessIconComponent, NtranslatePipe, ButtonModule, RippleModule, RouterModule],
})
export class EmailConfirmComponent implements OnInit {

  public showSuccess: boolean;
  public showError: boolean;
  public errorMessage: string;
  confirmation$: Observable<any>;

  constructor(
    private _authService: AuthService,
    private _route: ActivatedRoute,
    private translateService: TranslateService,
  ) {

    afterNextRender(() => {
      this.confirmEmail();
    });

  }

  ngOnInit(): void {


  }
  confirmEmail = () => {

    this.showError = this.showSuccess = false;

    const token = this._route.snapshot.queryParams['tokenstring'];
    const email = this._route.snapshot.queryParams['newEmail'];
    const userName = this._route.snapshot.queryParams['userName'];



    this.confirmation$ = this._authService.confirmEmail(token, email, userName).pipe(map(res => {

      return of("success");
    }), catchError(e => {
      this.errorMessage = e.errorMessage;
      return of("error");
    }));




  }

  login() {
    this._authService.redirectLogin();
  }
}
