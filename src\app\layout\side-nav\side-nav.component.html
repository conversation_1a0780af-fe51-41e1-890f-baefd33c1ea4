<div class="desktop-menu">
    <div class="menu-list" [ngClass]="{
        'active' : activeMode
    }">
        <ul class="main-menu">
            <ng-container *ngFor="let col of list;trackBy : trackby">
                <ng-container [ngSwitch]="col.items?.length > 0">
                    <li *ngSwitchCase="true" (click)="expand(col)" [ngClass]="{active : col['active']}">
                        <a>
                            <span class="link_title">
                                <img class="icon" [lazyload]="col.icon ?? ''" />
                                <span class="label">{{ col.label}}</span>
                            </span>
                            <i class="pi pi-chevron-right"></i>
                        </a>
                    </li>
                    <li *ngSwitchDefault [ngClass]="{active : col['active']}">
                        <a [routerLink]="col.routerLink">
                            <span class="link_title">
                                <img class="icon" [lazyload]="col.icon ?? ''" />
                                <span class="label">{{ col.label}}</span>
                            </span>
                        </a>
                    </li>
                </ng-container>
            </ng-container>

        </ul>
        <ul class="sub-menu" *ngFor="let child of children;trackBy : trackby">
            <li class="title sub-title">
                <a>
                    <span class="pi pi-chevron-right" (click)="backMenu($event)"></span>
                    <img class="icon" [lazyload]="child.icon ?? ''" />
                    <span class="label">{{ child.label }}</span>
                </a>
            </li>
            <li *ngFor="let item of child.items" [ngClass]="{'all_link' : item.styleClass ==  'all_link'}">
                <a [routerLink]="item.routerLink" (click)="triggerChoose($event)">
                    <div class="info">
                        <span class="label">{{ item.label}}</span>
                    </div>
                </a>
            </li>
        </ul>
    </div>
</div>