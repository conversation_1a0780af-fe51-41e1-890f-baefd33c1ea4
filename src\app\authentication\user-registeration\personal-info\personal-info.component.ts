import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { HeicConversionService } from '@src/app/core/services/heic.service';
import { AlertHandlerService } from 'src/app/modules/core/alerts/alert-handler.service';
import { BrowserService } from 'src/app/modules/core/service/browser.service';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { FormUtils } from 'src/utils/form-utils';
import { ProgressHeadComponent } from '../../../shared/components/progress-head/progress-head.component';
import { Ripple } from 'primeng/ripple';
import { NgIf, NgClass, NgFor, KeyValuePipe } from '@angular/common';
import { InputTextModule } from 'primeng/inputtext';
import { SecondaryBtnComponent } from '../../../shared/components/secondary-btn/secondary-btn.component';
import { NtranslatePipe } from '../../../shared/pipes/ntranslate.pipe';

@Component({
  selector: 'app-personal-info',
  templateUrl: './personal-info.component.html',
  styleUrls: ['./personal-info.component.scss'],
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [
    ProgressHeadComponent,
    FormsModule,
    ReactiveFormsModule,
    Ripple,
    NgIf,
    InputTextModule,
    NgClass,
    NgFor,
    SecondaryBtnComponent,
    KeyValuePipe,
    NtranslatePipe,
  ],
})
export class PersonalInfoComponent implements OnInit {

  maxWidth = 1000;
  public form: FormGroup;
  imageBase: string;
  constructor(private _formBuilder: FormBuilder, private _router: Router, private alertService: AlertHandlerService, private translateService: TranslationService, private browser: BrowserService, private hcs: HeicConversionService) { }
  ngAfterViewInit() {

  }
  ngOnInit(): void {
    this.form = this._formBuilder.group({
      firstName: ['', [
        FormUtils.nameValidator,
        FormUtils.lengthValidator
      ]],
      lastName: ['', [
        FormUtils.nameValidator,
        FormUtils.lengthValidator

      ]],
    });

    this.browser.removeStorageItem('city')

    const firstName = this.browser.getStorageItem('firstName') || "";
    const lastName = this.browser.getStorageItem('lastName') || ""
    const image = this.browser.getStorageItem('image');
    if (firstName) {
      this.form.controls['firstName'].setValue(firstName);
      this.form.controls['lastName'].setValue(lastName);
      this.imageBase = image!;


    }
  }

  public validateControl = (controlName: string) => {
    return (
      this.form.controls[controlName].invalid &&
      this.form.controls[controlName].touched
    );
  };

  lengthValidator(control: FormControl) {
    if (!/^(.{2,20})$/.test(control.value)) {
      return { lengthPattern: "InvalidNameLength" };
    }
    return null;
  };

  nameValidator(control: FormControl) {
    if (!/^[\u0621-\u064Aa-zA-Z]+$/.test(control.value)) {
      return { namePattern: "InvalidNameCharacter" };
    }
    return null;
  };


  readURL(input: any) {
    if (this.browser.isBrowser()) {
      if (input.target.files && input.target.files[0]) {
        var reader = new FileReader();
        reader.onload = function (e: any) {
          let imgSrc = document.getElementById('uploadedImg');
          imgSrc?.setAttribute('src', e.target?.result);
        };

        reader.readAsDataURL(input.target.files[0]);
      }
    }
  }


  public hasError = (controlName: string, errorName: string) => {
    return this.form.controls[controlName].hasError(errorName);
  };

  editPersonalInfo(formValue: any) {
    if (!this.form.valid) { return }
    this.browser.setStorageItem('firstName', this.form.controls['firstName'].value);
    this.browser.setStorageItem('lastName', this.form.controls['lastName'].value);
    if (this.fileToUpload) {
      const reader = new FileReader();
      reader.readAsDataURL(this.fileToUpload);
      reader.onload = () => {
        let result = reader.result?.toString() || ''
        this.browser.setStorageItem('image', result);
      };
    }
    this._router.navigate(['/authentication/register/choose-city'], { replaceUrl: true });
  }
  fileToUpload: any;
  async handleFileInput($event: any) {


    let file = $event.target.files;

    let imageFile = file.item(0);

    let convertedFile;

    if (!FormUtils.validImageFile(imageFile)) {
      this.alertService.warn({ message: this.translateService.instant('Invalid_Extension') });
      return;
    }

    var validHic = FormUtils.validHicFile(imageFile);

    if (validHic) {
      convertedFile = await this.hcs.convertIfHeic(imageFile);
    }





    var reader = new FileReader();

    reader.onload = (event: any) => {
      this.fileToUpload = validHic ? convertedFile : imageFile;
      this.imageBase = event.target.result
    };

    reader.readAsDataURL(validHic ? convertedFile : imageFile);

  }

  resizeAndConvertImage(file: File, callback: (e: any, blob: Blob) => void) {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (event: any) => {
      const img = new Image();
      img.src = event.target.result;
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const scaleSize = this.maxWidth / img.width;
        canvas.width = this.maxWidth;
        canvas.height = img.height * scaleSize;

        this.imageBase = event.target.result


        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

        ((ee) => {
          canvas.toBlob(blob => {
            callback(ee, blob);
          }, 'image/jpeg');
        })(event)
      };
    };
  }
}
