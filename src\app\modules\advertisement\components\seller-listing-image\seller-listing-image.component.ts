import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, ElementRef, Input, OnInit } from '@angular/core';
import { ProfileModel } from '@src/app/shared/models/profile.model';
import { SellerMemebershipIconComponent } from 'src/app/modules/seller/components/seller-memebership-icon/seller-memebership-icon.component';
import { LazyloadDirective } from 'src/app/shared/directives/lazyload.directive';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-seller-listing-image',
  standalone: true,
  imports: [CommonModule, LazyloadDirective, SellerMemebershipIconComponent],
  templateUrl: './seller-listing-image.component.html',
  styleUrls: ['./seller-listing-image.component.scss']
})
export class SellerListingImageComponent implements OnInit, AfterViewInit {

  @Input() seller: ProfileModel;

  sellerImage: string;

  constructor(private elementRef: ElementRef) { }

  ngOnInit(): void {
    this.sellerImage = environment.userMediaPath + this.seller.userID + '.jpg';

  }

  ngAfterViewInit(): void {
    if (this.seller && this.seller.setting) {
      this.elementRef.nativeElement.style.setProperty(
        '--main-membership-color',
        this.seller.setting.membership?.color
      );
    }

  }

}
