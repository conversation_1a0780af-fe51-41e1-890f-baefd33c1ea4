@import "variables";
@import "../../style/common.scss";

.field {
    margin-bottom: 15px;

    @include Large {
        margin-bottom: 30px;
    }
}

.form_fields {
    margin-bottom: 15px;

    @include Large {
        margin-bottom: 30px;
    }
}

.rangeField {
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    h2 {
        font-size: 16px;
        width: 100%;
    }
}


.inputField {
    border-radius: 50px;
    padding: 20px 24px;
    width: 100%;
    background: #fff;
    display: flex;
    position: relative;
    align-items: center;


    input {
        width: 100%;
        width: 100%;
        padding: 0px;
        border: none;
        outline: none;
        box-shadow: none;
        font-size: 16px;
        font-weight: 400;
        left: 25px;
        background: transparent;

        &::placeholder {
            font-size: 16px;
            font-weight: 400;
            color: $text-color;
        }
    }

    &::ng-deep {
        .p-inputnumber-input {
            width: 100%;
            padding: 0px;
            border: none;
            outline: none;
            box-shadow: none;
            font-size: 16px;
            font-weight: 400;
        }



    }
}

.boolean_holder {
    background: #fff;
    padding: 20px;

    &:empty {
        padding: 0px;
        background: transparent;
    }
}

.switchInput,
.toggleInput {
    h2 {
        font-size: 16px;
    }

    &:first-of-type {
        border-top-left-radius: 20px;
        border-top-right-radius: 20px;
    }

    &:last-of-type {
        border-bottom-left-radius: 20px;
        border-bottom-right-radius: 20px;
        margin-bottom: 30px;
    }

}

.switchInput {
    &.required_input {
        border: none !important;
    }
}

.toggleInput {
    margin: 35px 0px;

    h2 {
        margin-bottom: 15px;
    }
}


.toggleList {
    display: flex;
    gap: 15px;
}


.switchInput {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dropInput {
    &::ng-deep {
        .p-dropdown {
            width: 100%;
            border: none;
            border-radius: 50px;
            background: #FFF;
            padding: 20px;
        }

        .p-dropdown-label {
            padding: 0px;
            display: flex;
        }

        .p-dropdown .p-dropdown-label.p-placeholder {
            color: $text-color;
            font-size: 16px;
            font-weight: 400;
            display: flex;
            align-items: center;
        }

        .p-dropdown .p-dropdown-clear-icon {
            right: 70px;
        }

        .selectDrop {
            color: $text-color;
            font-size: 16px;
            font-weight: 400;
        }

        .p-dropdown-item.p-highlight {
            background: #ccc;
            color: $text-color;
        }

        p-dropdown[required] .p-dropdown-label:before {
            content: '*';
            display: inline-block;
            font-size: 1.5rem;
            vertical-align: middle;
            margin-inline-end: 10px;
            font-weight: bold;
            color: $orangeColor;
            vertical-align: middle;
        }
    }
}


.radio_input {
    display: flex;
    gap: 40px;
    margin-bottom: 40px;
    align-items: center;

    h2 {
        color: $text-color;
        font-size: 16px;
        font-weight: 400;
    }
}



@include Large {
    .boolean_holder {
        border-radius: 10px;
    }
}