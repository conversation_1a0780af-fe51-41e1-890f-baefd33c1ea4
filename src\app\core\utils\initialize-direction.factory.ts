import { inject, Injectable } from '@angular/core';

export function initializeDirectionFactory() {
    const initializeDirectionService = inject(InitializeDirectionService);
    return () => initializeDirectionService.initializeDirection();
}

@Injectable({ providedIn: 'root' })
class InitializeDirectionService {

    constructor() {
        // apmService.init({
        //     serviceName: 'swapp-portal',
        //     serverUrl: environment.apmServer,
        //     environment: environment.apmEnvirnment
        // });
    }


    initializeDirection() {


    }



}
