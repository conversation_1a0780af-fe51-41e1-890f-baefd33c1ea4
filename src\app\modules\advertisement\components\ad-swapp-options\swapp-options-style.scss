@import "variables";

form{
    margin-top: 20px;
}
.money_input {
    display: flex;
    background: #575a6812;
    padding: 0px 16px;
    border-radius: 50px;

    svg {
        width: 62px;
        height: 63px;
    }

    &>div {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        padding-inline-start: 4px;

        &::ng-deep {
            .p-inputnumber-input {
                flex: 1;
                font-size: 30px;
                width: 100%;
                background: transparent;
                border: none;
                padding: 0px;

                &:focus{
                    box-shadow:none;
                }

                &::placeholder {
                    font-size: 30px;
                    opacity: 0.3;
                }
            }
        }

        input {
            flex: 1;
            font-size: 30px;
            width: 100%;
            background: transparent;

            &::placeholder {
                font-size: 30px;
                opacity: 0.3;
            }
        }

        span{
            color: opacify($color: $text-color, $amount: 0.3);
        }
    }
    
}

.listing_input {
    display: flex;
    background: #575a6812;
    padding: 16px;
    border-radius: 50px;
    cursor: pointer;
    svg {
        width: 34px;
        height: 28px;
        margin-inline-start: 11px;
    }

    &>div {
        flex: 1;
        display: flex;
        align-items: center;
        padding-inline-start: 20px;
        color: $text-color;
        font-weight: 600;
        font-size: 1rem;
        
    }
    
}




.small_add {
    position: relative;
    padding: 10px;
    z-index: 2;

    
    svg {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 36px;
        height: 36px;
        transform: translate3d(-50% , -50% ,0);
    }
}

.form_holder{
    background: #fff;
    padding: 20px;
    border-radius: 12px;
}

.final_offer_price{
    margin: 30px 0px 0px;
    cursor: pointer;
    width: 100%;
    display: flex;
    justify-content: space-between;
    background: #fff;
    align-items: center;
    color: $text-color;
    padding: 0px;
    font-size: 14px;
    text-align: start;
    .submit_btn{
        background: $text-color;
        border-radius: 70px;
        color: #fff;
        font-size: 15px;
        font-weight: 400;
        padding: 13px 29px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    b{
        font-size: 24px;
        font-weight: 800;
    }

    // &.active{
    //     background: rgba($orangeColor , 1);

    //     .submit_btn{
    //     }

    // }
}

