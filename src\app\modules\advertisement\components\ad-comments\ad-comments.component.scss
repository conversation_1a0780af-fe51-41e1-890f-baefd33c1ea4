@import "variables";
@import "mixins";

form {
    display: flex;
    border-radius: 55px;
    border: 1px solid rgba(72, 77, 99, 0.15);
    justify-content: space-between;
    padding: 14px;

    svg {
        width: 18px;
        height: 18px;
    }

    input {
        background: transparent;
        color: $text-color;
        font-size: 14px;
        flex: 1;

        &::placeholder {
            color: $text-color;
            font-size: 14px;
        }
    }
}

.name_date {
    width: 100%;
}


.formBox {
    padding: 15px 0px;
}


.reply_form {
    padding-inline-start: 41px;
    margin-top: 10px;
}


.comments-box {
    background: #fff;
    padding: 0px 16px;

    &.active {
        border-top: 1px solid rgba(72, 77, 99, 0.15);
    }

}



.comment_body {
    display: flex;

    .image {
        cursor: pointer;

        img {
            width: 37px;
            height: 37px;
            border-radius: 50%;
            object-fit: cover;
        }
    }

    .name {
        color: #3B4158;
        font-size: 14px;
        font-weight: 400;

        span {
            font-size: 11px;
            font-style: italic;
            color: rgba($color: $text-color, $alpha: 0.7);
            padding-inline-start: 2px;
        }
    }

    .date {
        color: rgba(59, 65, 88, 0.50);
        font-size: 11px;
        font-style: italic;
        font-weight: 400;
        padding: 0px 5px;
    }

    .comment_text {
        color: #3B4158;
        font-size: 14px;
        font-weight: 300;
    }
}

.reason_form {
    padding: 0px;
    display: flex;
    flex-direction: column;
    border: none;
}

.replies_boxes {
    padding-inline-start: 34px;
}

.comment_boxes {
    max-height: 460px;
    overflow-y: auto;
}

.comment_box {
    display: flex;
    flex-direction: column;
}

.comment_body_wrapper {
    position: relative;
    padding-top: 19px;







    &::before {
        content: "";
        position: absolute;
        left: 18px;
        top: 0px;
        width: 20px;
        height: 38px;
        border-left: 1px solid var(--secondary-color);
        border-bottom: 1px solid var(--secondary-color);
        opacity: 0.1;
        background: #FFF;
        border-bottom-left-radius: 8px;
        @include rtl2(left, auto);
        @include rtl2(right, 18px);
        @include rtl2(border-left, 0px);
        @include rtl2(border-right, 1px solid var(--secondary-color));
        @include rtl2(border-bottom-left-radius, 0px);
        @include rtl2(border-bottom-right-radius, 8px);

    }

    &::after {
        content: '';
        position: absolute;
        left: 18px;
        top: 38px;
        width: 1px;
        height: calc(100% - 38px);
        opacity: 0.1;
        background: var(--secondary-color);

        @include rtl2(left, auto);
        @include rtl2(right, 18px);
    }

    &:last-of-type {
        &::after {
            display: none;
        }
    }

    &:not(.replies_boxes) {

        &::before {
            opacity: 0;
        }

    }

}

.image {
    padding-inline-end: 16px;
    position: relative;
    z-index: 2;
}



.reply_action_btn {
    background: #2d31421a;
    width: 30px;
    height: 30px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    cursor: pointer;

    svg {
        width: 15px;
        height: 15px;
    }

}

.reply_actions {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.reply_text {
    cursor: pointer;
    color: $text-color;
    font-size: 0.9rem;
    background: rgba($color: $text-color, $alpha: 0.1);
    border-radius: 20px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 0px 12px;
}