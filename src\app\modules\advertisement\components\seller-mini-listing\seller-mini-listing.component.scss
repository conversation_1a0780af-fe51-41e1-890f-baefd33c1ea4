@import "variables";
@import "mixins";


.listin-title {
    display: flex;
    color: $primary;
    font-size: 18px;
    align-items: center;
    gap: 15px;
    margin: 0px 0px 20px;

    .pi {
        width: 40px;
        height: 40px;
        color: #2D3142;
        border: 1px solid #2D3142;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        opacity: 0.5;

    }
}

.back-button {
    position: absolute;
    top: -52px;
    left: 10px;
    background: #898B95;
    border-radius: 50%;
    width: 36px !important;
    height: 36px !important;
    display: flex;
    justify-content: center;
    align-items: center;

    .pi {
        color: #fff;
    }
}

.item {
    display: flex;
    border-radius: 12px;
    overflow: hidden;
    background: #fff;
    transition: all 0.3s ease-out;
    border: 1px solid rgba(250, 175, 64, 0);
    position: relative;
    margin-bottom: 20px;

    .pi-times {
        position: absolute;
        top: 0.8rem;
        right: 0.8rem;
        color: #DC2D2D;
        font-size: 1.5rem;
        z-index: 2;
        opacity: 0;
        @include rtl2(left, 0.8rem);
        @include rtl2(right, auto);
    }




    &.selected {
        border-radius: 12px;
        border: 1px solid rgba(250, 175, 64, 0.7);
        background: rgba(250, 175, 64, 0.04);

        .pi-times {
            opacity: 1;
        }
    }

}

.item-img {
    width: 100px;
    height: 100px;
    position: relative;
    background-color: $text-color;

    img {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0px;
        left: 0px;
        object-fit: cover;
    }

}


.item-content {
    flex: 1;
    padding-inline-start: 20px;
    padding-top: 10px;
    padding-bottom: 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    h2 {
        font-size: 16px;
        font-weight: 400;
        padding-inline-end: 40px;
    }

    h3 {
        color: var(--secondary-color);
        font-size: 16px;
        font-weight: 800;
    }

}



.items {
    list-style: none;
    margin: 0px;
    padding: 0px;
    min-height: 30vh;
    max-height: 50vh;
    overflow: auto;

}



.p-dialog-header-icons {
    right: auto;
    left: 10px;
}


.no_listing {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;

    img {
        width: 72%;
        margin-bottom: 30px;
    }

    h4 {
        font-size: 16px;
    }

    h2 {
        font-size: 26px;
        line-height: 1.5;
    }

    app-primary-btn {
        width: 100%;
        align-items: center;
    }

}


@include Large {
    .list-container {
        padding-top: 20px;

    }
}