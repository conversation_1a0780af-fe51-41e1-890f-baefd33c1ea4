import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Store } from '@ngrx/store';
import { getCategories } from '@src/app/store/app/selectors/app.selector';
import { MenuItem } from 'primeng/api';
import { Observable, map, of, take } from 'rxjs';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { LazyloadDirective } from 'src/app/shared/directives/lazyload.directive';
import { BannerModel } from 'src/app/shared/models/common.model';
import { CategoryMenuDTO } from "src/app/shared/models/lookup.model";
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { SlugPipe } from 'src/app/shared/pipes/slug.pipe';
import { CommonService } from 'src/app/shared/services/common.service';
import { DeviceDetectionService } from 'src/app/shared/services/device-detection.service';
@UntilDestroy({ checkProperties: true })
@Component({
  selector: 'app-featured-categories',
  templateUrl: './featured-categories.component.html',
  styleUrls: ['./featured-categories.component.scss'],
  standalone: true,
  imports: [CommonModule, RouterModule, NtranslatePipe, LazyloadDirective, SlugPipe]
})
export class FeaturedCategoriesComponent implements OnInit {


  categoriesItems: Observable<MenuItem[]>;
  banner$: Observable<BannerModel | null>;


  constructor(
    public deviceDetectionService: DeviceDetectionService,
    private commonService: CommonService,
    private ts: TranslationService,
    private cd: ChangeDetectorRef,
    private store: Store

  ) { }

  convertToMenuItems(array: CategoryMenuDTO[]): MenuItem[] {
    return array.map((item: CategoryMenuDTO) => {
      let image = '';
      if (item.images.length == 1) {
        image = item.images[0].url;
      }
      if (item.images.length > 1) {
        const index = item.images.findIndex(element => element.type == 'Featured');
        if (index >= 0) {
          image = item.images[index].url;
        } else {
          image = item.images[0].url;
        }
      }
      const menuItem: MenuItem = {
        id: item.id.toString(),
        label: this.ts.instant(item.name),
        icon: image
      };

      if (item.subs && item.subs.length > 0) {
        menuItem.items = this.convertToMenuItems(item.subs);
      }

      return menuItem;
    });
  }

  ngOnInit(): void {
    this.categoriesItems = this.store.select(getCategories).pipe(untilDestroyed(this), map(res => {
      return this.convertToMenuItems(res.menu);
    }));

    this.banner$ = this.commonService.getAllBanners().pipe(map(res => res.data), map(res => res.filter(e => e.bannerType.indexOf(this.deviceDetectionService.isMobile ? "MobileFeaturedBanner" : "WebFeaturedBanner") >= 0)), map(res => {
      if (res.length > 0) {
        this.categoriesItems.pipe(take(1)).subscribe(categories => {
          this.categoriesItems = of(categories.slice(0, 4));
          this.cd.detectChanges();
        });
      }
      return res.length > 0 ? res[0] : null;
    }));

  }
}
