import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@angular/common";
import { Compo<PERSON>, ElementRef, OnInit, ViewChild } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { ListingService } from "@services/listing.service";
import { Pagination } from "@shared/models/base.response.model";
import { HomeSections, ListingFilters, ListingView } from "@shared/models/listing.model";
import { SchemaMarkupService } from "@src/app/core/services/schema-markup.service";
import { AdsRoutes, StripAdsType } from "@src/app/shared/models/lookup.model";
import { SplitListPipe } from "@src/app/shared/pipes/split-list.pipe";
import { AdsService } from "@src/app/shared/services/ads.service";
import { Observable, map, tap } from "rxjs";
import { SortBy } from "src/app/modules/core/enums";
import { MetaService } from "src/app/modules/core/service/meta.service";
import { DeviceDetectionService } from "src/app/shared/services/device-detection.service";
import { FormUtils } from "src/utils/form-utils";
import { CategoriesTabsComponent } from "../../../shared/components/categories-tabs/categories-tabs.component";
import { ItemsSliderComponent } from "../../../shared/components/items-slider/items-slider.component";
import { ListingSliderSkeletonComponent } from "../../../shared/components/skeleton/listing-slider-skeleton/listing-slider-skeleton.component";
import { IsAuthorizedDirective } from "../../../shared/directives/isauthorized.directive";
import { NtranslatePipe } from "../../../shared/pipes/ntranslate.pipe";
import { HeroSectionComponent } from "../components/hero-section/hero-section.component";
import { SmartBannerComponent } from "../components/smart-banner/smart-banner.component";
import { MiddleSectionPipe } from "../pipes/middle-section.pipe";
import { BannerModel, ResponsiveBannerModel } from "@src/app/shared/models/common.model";
import { CommonService } from "@src/app/shared/services/common.service";

@Component({
  selector: "app-home",
  templateUrl: "./home.component.html",
  styleUrls: ["./home.component.scss"],
  standalone: true,
  imports: [
    CategoriesTabsComponent,
    HeroSectionComponent,
    IsAuthorizedDirective,
    NgIf,
    ItemsSliderComponent,
    NgFor,
    SmartBannerComponent,
    ListingSliderSkeletonComponent,
    AsyncPipe,
    NtranslatePipe,
    MiddleSectionPipe,
    SplitListPipe,
  ],
})
export class HomeComponent implements OnInit {
  @ViewChild("listingList") listingList: ElementRef<HTMLInputElement>;

  layout = false;

  priceFrom: string;
  priceTo: string;
  selctedCondition: number[];
  listings: ListingView[];

  sortBy: SortBy[] = [
    { value: "firstApprovalDate", viewValue: "Newest", isSortAscending: false },
    { value: "price", viewValue: "HighPrice", isSortAscending: false },
    { value: "price", viewValue: "LowPrice", isSortAscending: true },
  ];

  selectedSortBy?: SortBy;

  isCollapsed = true;

  isCollapsed2 = true;

  filterVisible: boolean = false;

  filter: ListingFilters = {};

  isGrid: boolean;

  pagination: Pagination = {
    currentPage: 1,
    pageSize: 12,
    totalItems: 0,
    totalPages: 0,
  };
  userParams: any = {
    sortBy: "firstApprovalDate",
    isSortAscending: false,
    name: "",
  };
  searchString: string;
  locationString: string;
  userFavourites: number[];
  isUserAuthenticated: boolean = false;
  page: number = 1;

  homeSections: HomeSections[];

  homeSections$: Observable<HomeSections[]>;

  pickslisting$: Observable<ListingView[]>;
  banners$: Observable<BannerModel[]>;

  constructor(
    private activatedRoute: ActivatedRoute,
    private _listingService: ListingService,
    public deviceDetection: DeviceDetectionService,
    private metaService: MetaService,
    private adsService: AdsService,
    private schema: SchemaMarkupService,
    private commonService: CommonService,
    private dv: DeviceDetectionService
  ) {
    this.adsService.setAdsRoute(AdsRoutes.Home);
    this.adsService.setAdsStripType(StripAdsType.Home);
    this.schema.generateHomeSchema();
    this.banners$ = this.commonService.getAllBanners().pipe(
      map((res) => res.data),
      map((res) => {
        if (this.dv.isMobile) {
          return res
            .filter((e) => e.bannerType.includes("MobileMainBanner"))
            .map((e, i) => ({ ...e, index: i }));
        } else {
          return res
            .filter((e) => e.bannerType.includes("WebMainBanner"))
            .map((e, i) => ({ ...e, index: i }));
        }
      }),
      tap((res) => {
        // if (res.length > 0)
        //   this.meta.setPreload("preload-home-banner", res[0].data.imageURL);
      })
    );
  }

  ngOnInit(): void {
    this.metaService.getHomeMetaTag().subscribe((res) => {
      if (res && res.length > 0) {
        this.metaService.set({
          title: res[0].title,
          description: res[0].description,
          keywords: res[0].keywords,
        });
      } else {
        this.metaService.set({});
      }
    });
    this.metaService.set({});
    this.homeSections = this.activatedRoute.snapshot.data["homeResolver"];
    this.pickslisting$ = this._listingService
      .getUserinterestListing()
      .pipe(map((res) => res?.data?.items));
    this.activatedRoute.queryParams.subscribe((params) => {
      this.searchString = params["search"];
      this.locationString = params["location"];

      if (params["pageNumber"]) {
        for (let obj in params) {
          this.filter[obj] =
            obj == "conditions"
              ? +params[obj] > 0
                ? [+params[obj]]
                : []
              : FormUtils.parseValue(params[obj]);
        }

        this.pagination.currentPage = +params["pageNumber"];
      } else {
        this.filter = {
          ...this.filter,
          sortBy: this.userParams.sortBy,
          isSortAscending: this.userParams.isSortAscending,
        };
      }

      this.selectedSortBy = this.sortBy.find(
        (item) =>
          item.value == this.filter.sortBy &&
          item.isSortAscending == this.filter.isSortAscending
      );
    });
  }

  trackby(index, item) {
    return item.id;
  }

  changeGridMode(value) {
    this.isGrid = value;
  }
}
