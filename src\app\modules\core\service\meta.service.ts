import { DOCUMENT } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Inject, Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { Meta, MetaDefinition, Title } from '@angular/platform-browser';
import { BaseResponse } from '@src/app/shared/models/base.response.model';
import { AppcenterService } from '@src/app/shared/services/appcenter.service';
import { environment } from '@src/environments/environment';
import { map } from 'rxjs';
import { MetaModel } from 'src/app/modules/core/model/meta.mode';
import { BrowserService } from 'src/app/modules/core/service/browser.service';

declare const gtag: Function;
declare const fbq: Function;

@Injectable({
    providedIn: 'root'
})
export class MetaService {
    private readonly renderer: Renderer2;

    siteName = '4Swapp';

    constructor(@Inject(DOCUMENT) private document: any, private title: Title, private meta: Meta, private browser: BrowserService, private readonly rendererFactory: RendererFactory2, private lang: AppcenterService, private http: HttpClient) {

        this.renderer = rendererFactory.createRenderer(null, null);

        if (this.lang.lang == 'ar-eg') {
            this.siteName = 'فورسواب';
        }

    }
    set = (meta: MetaModel): void => {

        const defaultTitle = (meta.title && meta.title.indexOf(this.siteName) >= 0) ? meta.title : this.siteName + ' | ' + meta.title;
        meta.title = meta.title ? defaultTitle : this.siteName + ' | فورسواب الموقع الأول للبدل في مصر.. بدل .. بيع .. إشتري';
        this.title.setTitle(meta.title);
        this.meta.updateTag({ name: 'name', content: meta.title });
        this.meta.updateTag({ property: 'og:title', content: meta.title });
        this.meta.updateTag({ name: 'twitter:title', content: meta.title });

        meta.description = meta.description || 'فورسواب عليه آلاف الاعلانات للبدل والبيع .. من عربيات عقارات موبايلات أثاث أو أي حاجة تخطر على بالك';
        meta.description = meta.description.trim().replace(/\s+\n/g, ' ');

        this.meta.updateTag({ name: 'description', content: meta.description });
        this.meta.updateTag({ property: 'og:description', content: meta.description });
        this.meta.updateTag({ name: 'twitter:description', content: meta.description });

        meta.image = meta.image || 'https://4sw.app/assets/images/4swapp-ogimage.jpg?v=1.1.2';
        this.meta.updateTag({ name: 'image', content: meta.image });
        this.meta.updateTag({ property: 'og:image', content: meta.image });
        this.meta.updateTag({ name: 'twitter:image:src', content: meta.image });

        const renderedUrl = meta.url ?? environment.clintURL + '/' + this.lang.lang + '/home';



        if (renderedUrl) {
            let finalUrl = this.lang.lang == 'en-us' ? renderedUrl.replace('en-us', 'ar-eg') : renderedUrl;
            let link: HTMLLinkElement = this.document.querySelector('link[rel="canonical"]');
            if (link) {
                link.setAttribute('href', finalUrl);
            } else {
                let link: HTMLLinkElement = this.document.createElement('link');
                link.setAttribute('rel', 'canonical');
                this.document.head.appendChild(link);
                link.setAttribute('href', finalUrl);
            }
            this.addAlternativeLink(renderedUrl);
            this.addDefaultAlternativeLink(finalUrl);
            this.meta.updateTag({ property: 'url', content: finalUrl } as MetaDefinition);
            this.meta.updateTag({ property: 'og:url', content: finalUrl } as MetaDefinition);
            this.meta.updateTag({ name: 'twitter:url', content: finalUrl } as MetaDefinition);

        }



        let keywords = meta.keywords?.join(',') || 'sell,swapp';
        this.meta.updateTag({ name: 'keywords', content: keywords });

        meta.type = meta.type || 'website';
        this.meta.updateTag({ property: 'og:type', content: meta.type });

    }

    addAlternativeLink(url) {
        let oppositeKeys = {
            'ar-eg': 'en-US',
            'en-us': 'ar-EG'
        }
        let opposite = url.replace(this.lang.lang, oppositeKeys[this.lang.lang]);
        let alernateLink: HTMLLinkElement = this.document.querySelector('link[rel="alternate"]');
        if (alernateLink) {
            alernateLink.setAttribute('href', opposite);
            alernateLink.setAttribute('hreflang', oppositeKeys[this.lang.lang]);
        } else {
            let link: HTMLLinkElement = this.document.createElement('link');
            link.setAttribute('rel', 'alternate');
            this.document.head.appendChild(link);
            link.setAttribute('href', opposite);
            link.setAttribute('hreflang', oppositeKeys[this.lang.lang]);

            // let defaultLink: HTMLLinkElement = this.document.createElement('link');
            // link.setAttribute('rel', 'alternate');
            // this.document.head.appendChild(link);
            // link.setAttribute('href', opposite);
            // link.setAttribute('hreflang', 'x-default');

        }

    }

    addDefaultAlternativeLink(url) {
        let defaultLink: HTMLLinkElement = this.document.querySelector('link[rel="alternate"][hreflang="x-default"]');
        if (defaultLink) {
            defaultLink.setAttribute('href', url);
        } else {
            let link: HTMLLinkElement = this.document.createElement('link');
            link.setAttribute('rel', 'alternate');
            this.document.head.appendChild(link);
            link.setAttribute('href', url);
            link.setAttribute('hreflang', 'x-default');

        }

    }


    setPreload = (id: string, url: string, type: string = "image"): void => {

        let preloadExist: HTMLLinkElement = this.document.querySelector('link[id="' + id + '"]');

        if (!preloadExist) {
            const link: HTMLLinkElement = this.document.createElement('link');
            link.setAttribute('rel', 'preload');
            link.setAttribute('as', type);
            link.setAttribute('id', id);
            link.setAttribute('type', 'image/webp');
            link.setAttribute('fetchpriority', 'high');
            link.setAttribute('href', url);
            this.renderer.appendChild(this.document.head, link);
        }


    }

    setPrefetch(id: string, url: string) {
        let preloadExist: HTMLLinkElement = this.document.querySelector('link[id="' + id + '"]');

        if (!preloadExist) {
            const link: HTMLLinkElement = this.document.createElement('link');
            link.setAttribute('rel', 'prefetch');
            link.setAttribute('href', url);
            this.renderer.appendChild(this.document.head, link);
        }
    }

    addNoIndex() {
        if (!this.browser.isBrowser()) {
            this.meta.addTag({ name: 'robots', content: 'noindex, nofollow' });
            this.meta.addTag({ name: 'googlebot', content: 'noindex, nofollow' });
        }
    }

    getMetaByName(name: string) {
        return this.http.get<BaseResponse<MetaModel[]>>(`${environment.userURL}/api/MetaTag/render?Type=4&TagName=${name.replace(/-/g, ' ')
            }&LanguageID=${this.lang.lang == 'en-us' ? 1 : 2}`);
    }

    getHomeMetaTag() {
        return this.http.get<BaseResponse<MetaModel[]>>(`${environment.userURL}/api/MetaTag/render?Type=3&&ObjectID=1&LanguageID=${this.lang.lang == 'en-us' ? 1 : 2}`).pipe(map(res => res.data));
    }




}
