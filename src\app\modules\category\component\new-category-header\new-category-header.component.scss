app-hero-section {
  display: flex;
  align-items: center;
  height: 95%;
  overflow: hidden;
  border-radius: 10px;
}
:host ::ng-deep .carousel_actions .carousel-button-next-item,
:host ::ng-deep .carousel_actions .carousel-button-prev-item {
  display: none !important;
}

:host ::ng-deep .p-carousel-indicators {
  left: auto;
  right: 4%;

  .dot {
    background-color: #e0e0e0;
    &.active {
      background-color: var(--primary-color);
    }
  }
}
:host-context([dir="rtl"]) ::ng-deep .hero_slider .p-carousel-indicators {
  right: auto !important;
  left: 4% !important;
}

.category-header {
  position: relative;
  height: 306px;
  border-radius: 10px;
  overflow: hidden;
  display: flex;
  align-items: center;

  .slider-cover {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 390px;
    clip-path: polygon(0 0, 101% 0, 70% 101%, 0% 100%);

    .slider-cover-background {
      position: absolute;
      inset: 0;
      width: 100%;
      height: 100%;
      border-bottom-right-radius: 120px;
      border-top-right-radius: 5px;
      object-fit: cover;
      z-index: -1;
    }

    .overlay-content {
      padding: 20px;
      color: white;
      text-align: center;

      .title {
        font-size: 30px;
        font-weight: 700;
        line-height: 27px;
      }

      .vehicle-img {
        width: 100%;
        max-height: 100px;
        object-fit: contain;
      }
    }
  }

  // RTL support

  .slider-cover.rtl {
    left: auto;
    right: 0;
    clip-path: polygon(-1% 0, 100% 0%, 100% 100%, 30% 103%);
    .slider-cover-background {
      border-bottom-left-radius: 120px;
      border-top-left-radius: 5px;
      border-bottom-right-radius: 0px;
    }
  }

  // Responsive (mobile) styles
  @media (max-width: 1000px) and (min-width: 780px) {
    app-hero-section {
      display: block;
    }
  }

  @media (max-width: 768px) {
    app-hero-section {
      display: block;
    }
    height: 160px;

    .slider-cover {
      width: 200px;
      clip-path: polygon(0 0, 101% 0, 70% 101%, 0% 100%);
      .slider-cover-background {
        border-bottom-right-radius: 60px;
      }

      .overlay-content {
        padding: 6px;

        .title {
          font-size: 18px;
        }

        .vehicle-img {
          max-height: 60px;
        }
      }
    }

    .slider-cover.rtl {
      clip-path: polygon(-1% 0, 100% 0%, 100% 100%, 30% 103%);
      .slider-cover-background {
        border-bottom-left-radius: 60px;
      }
    }
  }
}
