@import "mixins";
@import "variables";

.adsStrip {
    width: 100%;
    left: 0px;

    a {
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        margin-bottom: 16px;



        img {
            width: 100%;
            max-width: 1440px;
            height: auto;

        }


    }

    .Sticky {
        position: fixed;
        top: 122px;
        z-index: 4;
        visibility: hidden;
        width: 100%;
        opacity: 0;
        transition: all 0.3s ease-in-out;
    }

    &.stickyMode {
        .Sticky {
            visibility: visible;
            opacity: 1;

        }
    }

    &.hideSearch {
        .Sticky {
            top: 60px;
        }
    }

    @include Large {
        .Sticky {
            top: 73px;
        }

        .Artboard {
            height: 50px;
            margin-bottom: 10px;

            a {
                position: fixed;
                z-index: 4;
                left: 50%;
                transform: translateX(-50%);
                width: 100%;
            }
        }

        // height: 50px;
        // margin-bottom: 10px;

        // a {
        //     position: fixed;
        //     top: 73px;
        //     left: 50%;
        //     transform: translateX(-50%);
        //     width: 100%;
        //     z-index: 4;

        //     &.sticky {
        //         top: 73px;
        //         z-index: 10;
        //         visibility: visible;
        //         opacity: 1;
        //     }


        // }
    }


}