import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseResponse } from '@src/app/shared/models/base.response.model';
import { environment } from '@src/environments/environment';
import { BrowserService } from 'src/app/modules/core/service/browser.service';

@Injectable({
  providedIn: 'root'
})
export class PartnerService {

  storage: string = "partner-register-form";

  acceptRegistration = false;


  constructor(private browser: BrowserService, private http: HttpClient) { }

  setData(value) {
    this.browser.setStorageItem(this.storage, JSON.stringify(value));
  }

  getData() {
    return JSON.parse(this.browser.getStorageItem(this.storage) ?? '');
  }

  update(key, value) {
    let data = JSON.parse(this.browser.getStorageItem(this.storage) ?? '');
    data[key] = value;
    this.setData(data);
  }
  getByKey(key) {
    let data = JSON.parse(this.browser.getStorageItem(this.storage) ?? '');
    return data[key] ?? '';
  }

  getContactTypes() {
    return this.http.get<BaseResponse<string>>(`${environment.authAPI}/api/users/getContactTypesList`);

  }

  destroy() {
    this.browser.removeStorageItem(this.storage);
  }

}
