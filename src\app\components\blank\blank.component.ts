import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BrowserService } from 'src/app/modules/core/service/browser.service';

@Component({
  selector: 'app-blank',
  templateUrl: './blank.component.html',
  styleUrls: ['./blank.component.scss'],
})
export class BlankComponent implements OnInit {
  constructor(private router: Router, private browser: BrowserService) { }

  ngOnInit(): void {
    let postLoginRedirectUri = this.browser.getStorageItem('redirectUri');
    if (postLoginRedirectUri) {
      this.browser.removeStorageItem('redirectUri')
      this.router.navigateByUrl(postLoginRedirectUri);
    } else {
      this.router.navigate(['/home']);
    }
  }
}
