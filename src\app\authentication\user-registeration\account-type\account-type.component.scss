@import "variables";
@import "mixins";
@import "../../../shared/components/inputs/style/common_input.scss";


.account_type {
    padding: 0px;

    .auth_form {

        .agree_to {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 12px;
            margin-bottom: clamp(20px, 11vw, 5vh);
            margin-top: -1.5rem;

            .p-checkbox {
                &-box {
                    border-color: rgba(#2d3142, 0.3);
                    @include transition(0.3s);
                    border-radius: 50%;

                    &.p-focus {
                        box-shadow: none !important;
                    }

                    &.p-highlight {
                        background-color: var(--secondary-color);
                        border-color: var(--secondary-color);
                    }
                }
            }

            label {
                font-size: 14px;
                font-weight: 400;
                color: #2d3142;
                line-height: normal;
                display: flex;
                align-items: center;
                justify-content: flex-start;
                gap: 5px;

                .p-button {
                    padding: 0;
                    background-color: transparent !important;
                    box-shadow: none !important;
                    border: 0 !important;
                    font-size: 14px;
                    font-weight: 400;
                    color: var(--secondary-color);
                    @include transition(0.3s);


                    &:hover {
                        text-decoration: underline;
                    }

                }


            }

        }

        .row {
            height: 100%;

            form {
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: space-around;
            }

            button {
                margin-bottom: 1rem;
            }

            .inputs_holder .p-button {
                background-color: #FFFFFF;
                color: #2D3142;
                display: flex;
                justify-content: space-between;
                padding: 0.5rem 1rem;
                border: none;

                img {
                    width: 73px;
                    height: 76px;
                }

                &.active {
                    background-color: #ffe5b5;
                }
            }
        }
    }
}

.p-dialog {
    max-height: 40rem !important;
}