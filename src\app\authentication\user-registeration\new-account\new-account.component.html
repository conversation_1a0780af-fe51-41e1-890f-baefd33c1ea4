<div class="auth_form_wrapper new_account">
  <div class="auth_form">
    <div class="back_logo d-flex d-lg-none">
      <Button pButton pRipple class="back_icon" [routerLink]="['/']">
        <img src="assets/img/backIcon.svg" alt="" />
      </Button>
      <Button pButton pRipple class="app_logo" [routerLink]="'/'">
        <img src="assets/img/4swapp-logo.svg?v=1.1.2" alt="" />
      </Button>
    </div>
    <div class="row">
      <div class="col-md-8 col-lg-10 col-xl-8 mx-auto">
        <h3>{{ 'Create New Account' | translate}}</h3>
        <form [formGroup]="registerForm" autocomplete="off" novalidate>
          <div class="inputs_holder mb-4">
            <div class="row">
              <div class="col-md-12 mb-4">

                <app-phone-input formControlName="phone"></app-phone-input>
              </div>
              <div class="col-md-12">
                <app-password-input formControlName="password"></app-password-input>
              </div>
            </div>
          </div>
          <app-secondary-btn [btnText]="'Next' | translate" btnType="button" [bigBtn]="true" (click)="submit()"
            [btnDisabled]="checkFormIsInValid()"></app-secondary-btn>

        </form>
        <div class="using">{{'Register Using' | translate}}</div>
        <app-auth-social mBottom="36px"></app-auth-social>
        <div class="agreeing">
          {{ 'by signing up you`re agreeing to our' | translate }}
          <a pButton pRipple routerLink="/page/privacy-policy" [label]="'Terms and Privacy Policy' | translate"></a>
        </div>
        <div class="have_account">
          <h5>{{ 'Already a user?' | translate}}</h5>
          <app-primary-btn [btnText]="'Log in' | translate" btnType="button" [bigBtn]="true"
            (click)="login()"></app-primary-btn>
        </div>
      </div>
    </div>
  </div>
</div>