import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { RippleModule } from 'primeng/ripple';
import { DarkBtnComponent } from 'src/app/shared/components/dark-btn/dark-btn.component';
import { GrayBtnComponent } from 'src/app/shared/components/gray-btn/gray-btn.component';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';

@Component({
  selector: 'app-swapp-loader',
  standalone: true,
  imports: [CommonModule, RippleModule, ButtonModule, DarkBtnComponent, GrayBtnComponent, NtranslatePipe],
  templateUrl: './swapp-loader.component.html',
  styleUrls: ['./swapp-loader.component.scss']
})
export class SwappLoaderComponent implements OnInit {
  message: string;
  btns = [];

  constructor(private ref: DynamicDialogRef, public config: DynamicDialogConfig) { }

  ngOnInit(): void {
    this.message = this.config.data.message;
    if (this.config.data.buttons) {
      this.btns = this.config.data.buttons;
    }
  }

  close(value) {
    if (this.ref) {
      this.ref.close(value);
    }
  }

}
