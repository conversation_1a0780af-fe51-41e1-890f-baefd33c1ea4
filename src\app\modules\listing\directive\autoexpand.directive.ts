import { Directive, HostListener, ElementRef, Renderer2 } from '@angular/core';

@Directive({
  selector: '[appAutoExpand]',
  standalone : true
})
export class AutoExpandDirective {

  constructor(private element: ElementRef, private renderer: Renderer2) {}

  @HostListener('input')
  onInput(): void {
    this.adjustTextareaHeight();
  }

  private adjustTextareaHeight(): void {
    const textarea = this.element.nativeElement;
    this.renderer.setStyle(textarea, 'height', 'auto');
    this.renderer.setStyle(textarea, 'height', `${textarea.scrollHeight}px`);
  }
}
