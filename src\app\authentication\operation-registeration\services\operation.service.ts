import { Injectable } from '@angular/core';
import { BrowserService } from 'src/app/modules/core/service/browser.service';

@Injectable({
  providedIn: 'root'
})
export class OperationService {

  storage: string = "operator-register-form";


  constructor(private browser: BrowserService) { }

  setData(value) {
    this.browser.setStorageItem(this.storage, JSON.stringify(value));
  }

  getData() {
    return JSON.parse(this.browser.getStorageItem(this.storage) ?? '');
  }

  update(key, value) {
    let data = JSON.parse(this.browser.getStorageItem(this.storage) ?? '');
    data[key] = value;
    this.setData(data);
  }
  getByKey(key) {
    let data = JSON.parse(this.browser.getStorageItem(this.storage) ?? '');
    return data[key] ?? '';
  }

  destroy() {
    this.browser.removeStorageItem(this.storage);
  }

}
