import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { ProfileModel } from '@src/app/shared/models/profile.model';
import { SellerListingImageComponent } from 'src/app/modules/advertisement/components/seller-listing-image/seller-listing-image.component';
import { AdvertisementService } from 'src/app/modules/advertisement/services/advertisement.service';
import { LazyloadDirective } from 'src/app/shared/directives/lazyload.directive';
import { ListingDetails } from 'src/app/shared/models/listing.model';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { RoundPipe } from 'src/app/shared/pipes/round.pipe';
import { SellerService } from 'src/app/shared/services/seller.service';

@Component({
  selector: 'app-ad-seller-info',
  standalone: true,
  imports: [CommonModule, RouterModule, NtranslatePipe, LazyloadDirective, RoundPipe, SellerListingImageComponent],
  templateUrl: './ad-seller-info.component.html',
  styleUrls: ['./ad-seller-info.component.scss']
})
export class AdSellerInfoComponent implements OnInit {
  @Input() listing: ListingDetails;
  @Input() isBussnisLogo: boolean = false;
  @Input() showSeeProfile: boolean = true;

  seller: ProfileModel;

  constructor(
    private _sellerService: SellerService,
    private advertisementService: AdvertisementService
  ) {

  }

  ngOnInit(): void {

    this.getSellerInfo();
  }



  getSellerInfo() {
    this._sellerService.getSellerData(this.listing.userID!).subscribe((x) => {
      if (!x.status) return;
      this.seller = x.data;
      this.advertisementService.sellerInfo = x.data;

    });
  }

  shouldApplyMargin() {
    return !this.isBussnisLogo && this.listing?.user ? { margin: '0rem 1rem' } : {};
  }

  get isBusinessInfo(): boolean {
    return this.listing.user && this.listing.user.businessName && (this.listing.user.accountType == 'business')
  }

  get isBusinessWithoutImage(): boolean {
    return this.listing.user && this.listing.user.businessName && (this.listing.user.accountType == 'business') && this.isBussnisLogo && !this.listing.user.imageURL.includes('avatar-temp');
  }
}
