@import "mixins";
.mobile-app-banner {
  width: 100%;
  background: #2d3142;
  color: white;
  padding: 10px 0px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  @include rtl2(direction, rtl);

  .banner-content {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .app-icon {
    width: 37px;
    height: 37px;
    margin: 0px 10px;
  }

  .text-info {
    flex-grow: 1;

    .app-title {
      font-weight: 700;
      font-size: 14px;
      color: white;
    }

    .app-subtitle {
      font-weight: 400;
      font-size: 12px;
      color: #989ca9;
    }
  }

  .open-app-btn {
    background-color: #fbaf43;
    color: white;
    padding: 6px 14px;
    border-radius: 35px;
    text-decoration: none;
    white-space: nowrap;
    font-size: 13px;
    font-weight: 700;
  }
}
@media (max-width: 359px) {
  .mobile-app-banner {
    .text-info {
      .app-title {
        font-size: 11px;
      }

      .app-subtitle {
        font-size: 10px;
      }
    }

    .open-app-btn {
      font-size: 11px;
      padding: 5px 12px;
    }
  }
}
.close-btn {
  background: transparent;
  color: white;
  border: none;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  padding: 0px 9px;
}

@media (min-width: 360px) and (max-width: 430px) {
  .mobile-app-banner {
    .text-info {
      .app-title {
        font-size: 11px;
      }

      .app-subtitle {
        font-size: 9px;
      }
    }
    .open-app-btn {
      font-size: 12px;
    }
  }
}
@include Small {
  .mobile-app-banner {
    .text-info {
      .app-title {
        font-size: 16px;
      }

      .app-subtitle {
        font-size: 14px;
      }
    }
    .open-app-btn {
      font-size: 14px;
    }
  }
}
