@import "variables";
@import "mixins";

.seller_box {
    background: #fff;
    display: flex;
    flex-direction: column;
    gap: 40px;
    padding: 16px;
    cursor: pointer;

    position: relative;

    // &::before{
    //     content: '';
    //     position: absolute;
    //     top: 50%;
    //     left: 0%;
    //     width: 100%;
    //     height: 1px;
    //     background: rgba(45, 49, 66, 0.10);
    // }

    .pi-chevron-right {
        transform: rotate(var(--arrow-rotation));
    }
}


.seller_icon {
    display: flex;
    gap: 15px;
    color: $text-color;


    .seller_info_box {
        display: flex;
        flex: 1;
        justify-content: space-between;
        align-items: center;

        .bussnisLogo {
            width: 131px;
            height: 43px;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            border-radius: 0.5rem;
            margin-right: 0.2rem;

            img {
                height: auto;
                width: 100%;
                object-fit: contain;
            }
        }
    }

    .pi-chevron-right {
        transform: rotate(var(--arrow-rotation));
    }

}

.seller_info {
    h2 {
        font-size: 16px;
        font-weight: 700;
        margin: 0px;
    }

    &>span {
        display: flex;
        align-items: center;
        font-size: 16px;

        svg {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
            color: $orangeColor;
            margin-inline-end: 5px;
        }

        span {
            font-weight: 400;
        }
    }
}





.seller_location {
    display: flex;
    gap: 15px;

    &>svg {
        width: 50px;
        height: 50px;
        flex-shrink: 0;
    }
}

.location_info {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    color: rgba(#3B4158, 6.0);

    h2 {
        font-size: 16px;
        font-weight: 600;
        margin: 0px;
        color: $text-color;
    }
}