import { CommonModule } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { MetaService } from '@src/app/modules/core/service/meta.service';
import { AppcenterService } from '@src/app/shared/services/appcenter.service';
import { ButtonModule } from 'primeng/button';
import { RippleModule } from 'primeng/ripple';
import { SidebarModule } from 'primeng/sidebar';
import { SlideMenuModule } from 'primeng/slidemenu';
import { TopMenuService } from 'src/app/layout/top-nav/service/top-menu.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-top-left-menu',
  standalone: true,
  imports: [CommonModule, RouterModule, SlideMenuModule, RippleModule, ButtonModule, SidebarModule],
  templateUrl: './top-left-menu.component.html',
  styleUrls: ['./top-left-menu.component.scss']
})
export class TopLeftMenuComponent implements OnInit, OnDestroy {
  url!: string;
  constructor(
    private topService: TopMenuService,
    private languageService: AppcenterService,
    public metaService: MetaService
  ) {
    this.url = environment.clintURL + "/" + this.languageService.lang + "/home";
  }

  ngOnInit(): void {



  }

  logoClicked() {

    this.topService.setSearch("");

  }


  ngOnDestroy() {
  }

}
