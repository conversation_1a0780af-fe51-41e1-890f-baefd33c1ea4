@import "mixins";
@import "variables";

ul {
    //grid-template-columns: repeat(3, 1fr);
    //display: grid;
    list-style: none;
    margin: 0px;
    white-space: nowrap;
    overflow: auto;
    display: flex;
    gap: 7px;
    padding: 0px !important;

    &.dimmed {
        opacity: 0.5;
    }

    &.profileMode {
        grid-template-columns: repeat(3, 1fr);
        white-space: inherit;
        display: grid;
        white-space: wrap;
        overflow: inherit;

        .item {
            &.selected {
                .item_icon {
                    border: 1px solid $orangeColor;
                    background-color: rgba($color: $orangeColor, $alpha: 0.1);

                    &:before {
                        display: none;
                    }

                }
            }
        }

        .item_label {
            font-size: 12px;
        }

    }


}

.item {
    display: inline-flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    width: 25vw;
    flex-shrink: 0;
    max-width: 130px;

    &.selected {
        color: $orangeColor;

        .item_icon {
            &:before {
                content: '\2713';
                display: block;
                padding: 0 6px 0 0;
                position: absolute;
                z-index: 2;
                width: 22px;
                height: 22px;
                color: #fff;
                border-radius: 50%;
                background: $orangeColor;
                font-weight: 600;
                text-align: center;
                padding-left: 5px;
                top: 0px;
                right: -6px;
            }

        }
    }
}

.item_label {
    font-size: 14px;
    margin-top: 11px;
    text-align: center;
    white-space: normal;
}


.item_icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #fff;
    border: 1px solid #e0e0e0;
    display: flex;
    padding: 0px;
    position: relative;

    img {
        width: 100%;
        height: 100%;

    }
}


@include Large {
    ul {
        grid-template-columns: repeat(5, 1fr);
        display: grid;
        white-space: wrap;
    }

    .item {
        width: auto;
    }
}