<ng-container>
  <div class="profile_menu">
    <span class="profile_btn" (click)="toggle()">
      <svg data-id="burger_profile" viewBox="0 0 23 21" fill="none">
        <rect width="23" height="2.5" rx="1.25" transform="matrix(1 0 -4.144e-08 -1 0 2.5)" fill="#2D3142" />
        <rect width="23" height="2.5" rx="1.25" transform="matrix(1 0 -6.86136e-08 -1 0 20.5)" fill="#2D3142" />
        <rect width="23" height="2.5" rx="1.25" transform="matrix(1 2.55624e-09 -4.27463e-08 -1 0 11.5)"
          fill="#2D3142" />
      </svg>
    </span>

  </div>


  <ng-container>
    <p-sidebar #NavSideBar [visible]="menuService.openSidebar()" (visibleChange)="onVisibleChange($event)"
      appendTo="body" position="right" [showCloseIcon]="false" [styleClass]="'sideMenu'" [style]="{
    width:'auto'
  }">
      <ng-template pTemplate="header">

      </ng-template>
      <ng-template pTemplate="footer"></ng-template>

      <div class="menu_body">
        <ul *ngIf="!user">
          <li>
            <a [href]="blogUrl" class="link" target="_blank">
              <svg viewBox="0 0 24 24" fill="none">
                <path opacity="0.8"
                  d="M16.3441 24C20.5292 24 23.9275 20.5906 23.9498 16.4278L23.9944 10.3009L23.9219 9.96605L23.721 9.54755L23.3806 9.28528C22.9398 8.93932 20.7022 9.3076 20.0995 8.76075C19.6698 8.37015 19.6029 7.66705 19.4745 6.70728C19.2346 4.85468 19.0839 4.75982 18.7938 4.12927C17.7503 1.90839 14.8877 0.228784 12.9235 0H7.60567C3.4206 0 0 3.41502 0 7.58335V16.4278C0 20.5906 3.4206 24 7.60567 24H16.3441ZM7.70612 6.19391H11.9247C12.7282 6.19391 13.3811 6.84678 13.3811 7.64473C13.3811 8.43711 12.7282 9.09556 11.9247 9.09556H7.70612C6.90258 9.09556 6.24971 8.43711 6.24971 7.64473C6.24971 6.84678 6.897 6.19391 7.70612 6.19391ZM6.24971 16.3162C6.24971 15.5238 6.90258 14.871 7.70612 14.871H16.2771C17.0751 14.871 17.728 15.5183 17.728 16.3162C17.728 17.0974 17.0807 17.7614 16.2771 17.7614H7.70612C6.897 17.7614 6.24971 17.103 6.24971 16.3162Z"
                  fill="currentColor" />
              </svg>


              {{ "Blog" | translate }}
            </a>
            <a [routerLink]="['/settings']" class="link" rel="nofollow">
              <svg width="27" height="27" viewBox="0 0 27 27" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path opacity="0.8"
                  d="M11.8708 1.1547C13.1084 0.44017 14.6332 0.440169 15.8708 1.1547L23.2414 5.4101C24.479 6.12464 25.2414 7.44514 25.2414 8.87421V17.385C25.2414 18.8141 24.479 20.1346 23.2414 20.8491L15.8708 25.1045C14.6332 25.819 13.1084 25.819 11.8708 25.1045L4.50025 20.8491C3.26264 20.1346 2.50025 18.8141 2.50025 17.385V8.87421C2.50025 7.44514 3.26264 6.12464 4.50025 5.4101L11.8708 1.1547Z"
                  fill="currentColor" />
                <rect x="9.21191" y="8.4707" width="9.31779" height="9.31779" rx="4.65889" fill="white" />
              </svg>

              {{ "settings" | translate }}

            </a>

          </li>
        </ul>
        <ul *ngIf="user">
          <li (click)="isOpen = false">

            <a class="user_info" [routerLink]="['/authentication/profile']" rel="nofollow">
              <div>
                <h3 (click)="$event.stopPropagation()">
                  <app-seller-listing-image [seller]="user"></app-seller-listing-image>
                  <span>
                    <b>{{ user.businessName ? user.businessName : user.name }}</b>
                    <div class="user_rate">
                      <svg data-id="rate_star" width="14" height="14" viewBox="0 0 14 14" fill="none">
                        <path
                          d="M6.14024 1.44742C6.52776 0.795014 7.47223 0.795014 7.85976 1.44742L9.39464 4.0314C9.53497 4.26765 9.76633 4.43574 10.0344 4.4962L12.9662 5.15746C13.7064 5.32441 13.9983 6.22266 13.4976 6.79282L11.5143 9.05107C11.333 9.25754 11.2447 9.52952 11.27 9.80313L11.5471 12.7958C11.617 13.5514 10.8529 14.1065 10.1559 13.8065L7.39538 12.6182C7.14299 12.5096 6.85701 12.5096 6.60462 12.6182L3.84405 13.8065C3.14706 14.1065 2.38297 13.5514 2.45293 12.7958L2.73001 9.80313C2.75535 9.52952 2.66698 9.25754 2.48565 9.05107L0.502445 6.79282C0.00172246 6.22266 0.293579 5.32441 1.03381 5.15746L3.96562 4.4962C4.23367 4.43574 4.46503 4.26765 4.60536 4.0314L6.14024 1.44742Z"
                          fill="currentColor" />
                      </svg>
                      <b>{{ +(user.rate) | round }}</b>
                    </div>
                  </span>
                </h3>

              </div>
              <i class="pi pi-chevron-right"></i>
            </a>


          </li>

          <li class="link custom" [routerLink]="['/offers']" (VerifiedClick)="isOpen = false" VerifiedClick>
            <svg viewBox="0 0 23 25" fill="none">
              <g>
                <path
                  d="M11.5251 17.7105C9.86219 17.7128 9.18066 15.5488 10.4239 14.2141L15.5177 8.74576C16.7936 7.37611 18.8147 8.23322 18.7053 10.0975L18.6326 11.3353C18.6023 11.8524 18.7495 12.3452 19.0457 12.7178L20.203 14.1739C21.2538 15.496 20.2089 17.6988 18.5297 17.701L11.5251 17.7105Z"
                  fill="currentColor" />
                <path
                  d="M11.4914 6.25239C13.1535 6.3182 13.7691 8.4915 12.4891 9.77464L7.24182 15.0346C5.92374 16.3558 3.91867 15.4317 4.07988 13.5772L4.18884 12.3237C4.2332 11.8134 4.10141 11.3207 3.81876 10.94L2.69263 9.42358C1.68183 8.06242 2.8025 5.90837 4.48678 5.97506L11.4914 6.25239Z"
                  fill="currentColor" />
              </g>
            </svg>

            {{ "Offers" | translate }}
          </li>
          <li class="link" (VerifiedClick)="isOpen = false" [routerLink]="['/offers/messages']" VerifiedClick>
            <svg data-id="envelop_icon" viewBox="0 0 23 19" fill="none">
              <rect x="1.39844" y="0.761719" width="20.1948" height="18.1753" rx="5" fill="currentColor" />
              <path d="M0.828125 6.37891L11.5028 11.9971L22.1774 6.37891" stroke="white" />
            </svg>

            {{ 'Messages' | translate }}
          </li>
          <li class="link" [routerLink]="['/listing/list']" (VerifiedClick)="isOpen = false" VerifiedClick>
            <svg data-id="listing_icon" viewBox="0 0 23 22" fill="none">
              <path
                d="M18.5225 0.916914C20.5295 0.249345 22.6587 1.83479 22.6264 3.97275L22.4232 17.3966C22.3916 19.4869 20.3055 20.8427 18.3384 20.0512L13.7364 18.2C13.4927 18.81 13.1261 19.3787 12.6356 19.8691C10.6303 21.8744 7.31508 21.8105 5.23074 19.7262C3.72541 18.2208 3.27396 16.0734 3.89474 14.2411L1.98732 13.4738C1.09559 13.1151 0.485481 12.2644 0.439291 11.3156L0.313486 8.72651C0.263627 7.69938 0.883904 6.78395 1.84669 6.46373L18.5225 0.916914ZM5.74157 14.9841C5.37732 16.1187 5.66453 17.4367 6.59235 18.3646C7.89511 19.6673 9.96709 19.7072 11.2204 18.4539C11.5144 18.1599 11.7373 17.8207 11.8895 17.4571L5.74157 14.9841Z"
                fill="currentColor" />
            </svg>

            {{ 'Listing' | translate }}
          </li>
          <li class="link" [routerLink]="['/listing/list/favourites']" (click)="isOpen = false">
            <svg data-id="heart_icon" viewBox="0 0 24 23" fill="none">
              <path
                d="M12 22.7617C20.7273 18.3611 24 12.8604 24 7.3597C23.9994 5.94193 23.5461 4.56203 22.7073 3.42426C21.8686 2.28648 20.6888 1.4513 19.3427 1.04232C17.9966 0.633343 16.5557 0.672309 15.2332 1.15345C13.9108 1.6346 12.7771 2.53235 12 3.71382C11.2229 2.53235 10.0892 1.6346 8.76676 1.15345C7.44433 0.672309 6.00342 0.633343 4.65731 1.04232C3.31121 1.4513 2.13145 2.28648 1.29266 3.42426C0.453863 4.56203 0.000614815 5.94193 0 7.3597C0 12.8604 3.27273 18.3611 12 22.7617Z"
                fill="currentColor" />
            </svg>

            {{ 'Favorites' | translate }}
          </li>
          <li>
            <a [href]="blogUrl" class="link" target="_blank">
              <svg viewBox="0 0 24 24" fill="none">
                <path opacity="0.8"
                  d="M16.3441 24C20.5292 24 23.9275 20.5906 23.9498 16.4278L23.9944 10.3009L23.9219 9.96605L23.721 9.54755L23.3806 9.28528C22.9398 8.93932 20.7022 9.3076 20.0995 8.76075C19.6698 8.37015 19.6029 7.66705 19.4745 6.70728C19.2346 4.85468 19.0839 4.75982 18.7938 4.12927C17.7503 1.90839 14.8877 0.228784 12.9235 0H7.60567C3.4206 0 0 3.41502 0 7.58335V16.4278C0 20.5906 3.4206 24 7.60567 24H16.3441ZM7.70612 6.19391H11.9247C12.7282 6.19391 13.3811 6.84678 13.3811 7.64473C13.3811 8.43711 12.7282 9.09556 11.9247 9.09556H7.70612C6.90258 9.09556 6.24971 8.43711 6.24971 7.64473C6.24971 6.84678 6.897 6.19391 7.70612 6.19391ZM6.24971 16.3162C6.24971 15.5238 6.90258 14.871 7.70612 14.871H16.2771C17.0751 14.871 17.728 15.5183 17.728 16.3162C17.728 17.0974 17.0807 17.7614 16.2771 17.7614H7.70612C6.897 17.7614 6.24971 17.103 6.24971 16.3162Z"
                  fill="currentColor" />
              </svg>


              {{ "Blog" | translate }}
            </a>
            <a [routerLink]="['/settings']" class="link" rel="nofollow">
              <svg width="27" height="27" viewBox="0 0 27 27" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path opacity="0.8"
                  d="M11.8708 1.1547C13.1084 0.44017 14.6332 0.440169 15.8708 1.1547L23.2414 5.4101C24.479 6.12464 25.2414 7.44514 25.2414 8.87421V17.385C25.2414 18.8141 24.479 20.1346 23.2414 20.8491L15.8708 25.1045C14.6332 25.819 13.1084 25.819 11.8708 25.1045L4.50025 20.8491C3.26264 20.1346 2.50025 18.8141 2.50025 17.385V8.87421C2.50025 7.44514 3.26264 6.12464 4.50025 5.4101L11.8708 1.1547Z"
                  fill="currentColor" />
                <rect x="9.21191" y="8.4707" width="9.31779" height="9.31779" rx="4.65889" fill="white" />
              </svg>

              {{ "settings" | translate }}

            </a>

          </li>

        </ul>

        <app-side-nav [items]="categoriesItems" (onChoose)="displayDesktopMenu = false"></app-side-nav>

        <div class="middle_btn">

          <app-primary-btn [btnText]="'sell_and_swap' | translate" [withIcon]="true" icon="assets/img/plus.svg"
            [rightPos]="false" btnType="button" [midBtn]="true" [color]="true" VerifiedClick
            (VerifiedClick)="goToCreateListing()" (unVerifiedClick)="sendEvent($event)"></app-primary-btn>


        </div>
        <div id="bottom_links">
          <a class="info_link" target="_blank" href="https://wa.me/201098684444" rel="nofollow">
            <i>
              <svg data-id="phone_icon" viewBox="0 0 15 15" fill="none">
                <path opacity="0.7"
                  d="M13.8474 10.1901L10.5594 8.78804C10.3898 8.712 10.2035 8.68047 10.0183 8.69641C9.83309 8.71236 9.65499 8.77526 9.50083 8.87918L7.74119 10.0499C6.66875 9.52907 5.80045 8.6657 5.27348 7.59625L6.43723 5.81557C6.54045 5.66125 6.6035 5.4836 6.62064 5.29873C6.63778 5.11386 6.60847 4.92764 6.53538 4.75698L5.12626 1.46904C5.02793 1.24702 4.86144 1.0621 4.65093 0.941088C4.44042 0.820076 4.19683 0.769265 3.9555 0.796024C3.00879 0.920043 2.13933 1.38363 1.50876 2.10059C0.878203 2.81754 0.529449 3.73909 0.527344 4.69388C0.527344 10.2603 5.05615 14.7891 10.6225 14.7891C11.5773 14.787 12.4989 14.4382 13.2158 13.8076C13.9328 13.1771 14.3964 12.3076 14.5204 11.3609C14.5471 11.1196 14.4963 10.876 14.3753 10.6655C14.2543 10.455 14.0694 10.2885 13.8474 10.1901Z"
                  fill="#2D3142" />
              </svg>

            </i> <span>01098684444</span>
          </a>
          <a href="mailto:<EMAIL>" target="_blank" class="info_link" rel="nofollow">
            <i>
              <svg data-id="email_icon" viewBox="0 0 16 13" fill="none">
                <rect opacity="0.7" x="1.17969" y="0.261719" width="13.9181" height="12.5263" rx="6" fill="#2D3142" />
                <path d="M0.789062 4.13281L8.14594 8.00485L15.5028 4.13281" stroke="#F3EBE8" stroke-width="2" />
              </svg>

            </i> <span>support&#64;4sw.app</span>
          </a>

          <div class="custom_lang" *ngIf="user">
            <span class="link" (click)="logout()">
              <svg data-id="signout_icon" viewBox="0 0 16 16" fill="none">
                <path
                  d="M15.9111 7.64446C15.8222 7.55557 15.8222 7.46668 15.7333 7.37779L13.0667 4.71112C12.7111 4.35557 12.1778 4.35557 11.8222 4.71112C11.4667 5.06668 11.4667 5.60001 11.8222 5.95557L12.9778 7.11112H8.88889C8.35556 7.11112 8 7.46668 8 8.00001C8 8.53335 8.35556 8.8889 8.88889 8.8889H12.9778L11.8222 10.0445C11.4667 10.4 11.4667 10.9333 11.8222 11.2889C12 11.4667 12.2667 11.5556 12.4444 11.5556C12.6222 11.5556 12.8889 11.4667 13.0667 11.2889L15.7333 8.62224C15.8222 8.53335 15.9111 8.44446 15.9111 8.35557C16 8.0889 16 7.91112 15.9111 7.64446Z"
                  fill="#ABADB3" />
                <path
                  d="M11.1111 13.4222C10.1333 13.9556 9.06667 14.2222 8 14.2222C4.53333 14.2222 1.77778 11.4667 1.77778 8C1.77778 4.53333 4.53333 1.77778 8 1.77778C9.06667 1.77778 10.1333 2.04444 11.1111 2.57778C11.5556 2.84444 12.0889 2.66667 12.3556 2.22222C12.6222 1.77778 12.4444 1.24444 12 0.977778C10.7556 0.355555 9.42222 0 8 0C3.55556 0 0 3.55556 0 8C0 12.4444 3.55556 16 8 16C9.42222 16 10.7556 15.6444 12 14.9333C12.4444 14.6667 12.5333 14.1333 12.3556 13.6889C12.0889 13.3333 11.5556 13.1556 11.1111 13.4222Z"
                  fill="#ABADB3" />
              </svg>

              {{ "Logout" | translate }}</span>
          </div>
        </div>

      </div>


    </p-sidebar>
  </ng-container>
</ng-container>