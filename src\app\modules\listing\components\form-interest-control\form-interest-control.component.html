<ul *ngIf="items$ | async as items" [ngClass]="{
    'profileMode' : profileMode,
    'dimmed' : !profileMode && SelectedList.size == maxSelection
}">
    <li *ngFor="let item of items" class="item" [ngClass]="{'selected' :  SelectedList.has(item.id)}">
        <span class="item_icon" (click)="SelectedItem(item.id)"><img [src]="item.imageURL"/></span>
        <span class="item_label">{{ item.name | translate }}</span>
    </li>
</ul>