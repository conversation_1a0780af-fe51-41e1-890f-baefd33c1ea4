<div class="list-container" *ngIf="listing$ | async as items; else loader">

  <ng-container [ngSwitch]="items.length > 0">
    <div *ngSwitchCase="true">
      <i *ngIf="allowBack" class="p-dialog-header-icons back-button" (click)="onBack.emit(true)">
        <span class="pi pi-chevron-left"></span>
      </i>
      <h2 class="listin-title"><i class="pi pi-plus"></i> {{ 'Offer Listings' | translate }}</h2>
      <ul class="items">
        <li *ngFor="let item of items" [ngClass]="{'selected' :  selectedList && selectedList.has(item.id)}"
          class="item" (click)="SelectedItem(item)">
          <div class="item-img">
            <img class="item_img" [lazyload]="item.imageURL | listingImagePath:'previewlist'"
              [placeholder]="'assets/img/placeholder/no-image.svg'" />
          </div>
          <div class="item-content">
            <h2>{{ item.name }}</h2>
            <h3 [innerHTML]="item.price | ncurrency"></h3>
          </div>

          <i class="pi pi-times"></i>
        </li>
      </ul>

      <app-secondary-btn [btnDisabled]="selectedList.size == 0" [btnText]="'Confirm' | translate" btnType="button"
        [bigBtn]="true" (click)="onSubmit.emit(selectedList);"></app-secondary-btn>
    </div>
    <div *ngSwitchDefault class="no_listing">
      <img src="/assets/images/no-listing.webp?v=1.1.2" />
      <h4>{{ 'You dont have published listings' | translate}}</h4>
      <h2>{{ 'Publish a Listing to submit an Offer' | translate }}</h2>
      <app-primary-btn [btnText]="'sell_and_swap' | translate" [bigBtn]="true" [withIcon]="true"
        icon="assets/img/plus.svg" [rightPos]="false" btnType="button" [routerLink]="['/listing/create']"
        class="d-none d-lg-flex"></app-primary-btn>
    </div>
  </ng-container>
</div>


<ng-template #loader>
  <app-loader-spinner></app-loader-spinner>
</ng-template>