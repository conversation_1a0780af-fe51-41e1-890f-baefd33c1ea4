import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { ReactiveFormsModule } from '@angular/forms';
import { DialogModule } from 'primeng/dialog';
import { InputNumberModule } from 'primeng/inputnumber';
import { RadioButtonModule } from 'primeng/radiobutton';
import { RippleModule } from 'primeng/ripple';
import { DarkBtnComponent } from 'src/app/shared/components/dark-btn/dark-btn.component';
import { GrayBtnComponent } from 'src/app/shared/components/gray-btn/gray-btn.component';
import { ItemCarouselComponent } from 'src/app/shared/components/item-carousel/item-carousel.component';
import { ItemsSliderComponent } from 'src/app/shared/components/items-slider/items-slider.component';
import { SecondaryBtnComponent } from 'src/app/shared/components/secondary-btn/secondary-btn.component';
import { ItemDetailsSkeletonComponent } from 'src/app/shared/components/skeleton/item-details-skeleton/item-details-skeleton.component';
import { SwappableItemsComponent } from 'src/app/shared/components/swappable-items/swappable-items.component';
import { IsAuthorizedDirective } from 'src/app/shared/directives/isauthorized.directive';
import { VerifiedClickDirective } from 'src/app/shared/directives/isverifiedclick.directive';
import { FromNowPipe } from 'src/app/shared/pipes/from-now.pipe';
import { NCurrencyPipe } from 'src/app/shared/pipes/ncurrency.pipe';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { AdvertisementRoutingModule } from './advertisement-routing.module';
import { AdCheckofferBtnComponent } from './components/ad-checkoffer-btn/ad-checkoffer-btn.component';
import { AdCommentsComponent } from './components/ad-comments/ad-comments.component';
import { AdSellerInfoComponent } from './components/ad-seller-info/ad-seller-info.component';
import { AdStatusViewComponent } from './components/ad-status-view/ad-status-view.component';
import { AdSwappOptionsComponent } from './components/ad-swapp-options/ad-swapp-options.component';
import { AdTopLinksComponent } from './components/ad-top-links/ad-top-links.component';
import { ListingInstructionsComponent } from './components/listing-instructions/listing-instructions.component';
import { ListingOffersComponent } from './components/listing-offers/listing-offers.component';
import { AdvertisementIndexComponent } from './pages/advertisement-index/advertisement-index.component';
import { HideListingPipe } from './pipe/hidelisting.pipe';
import { IsViewModePipe } from './pipe/isviewmode.pipe';
import { ListingImagesUrlPipe } from './pipe/listing-images-url.pipe';


@NgModule({
    imports: [
        CommonModule,
        AdvertisementRoutingModule,
        ItemCarouselComponent,
        AdSwappOptionsComponent,
        AdSellerInfoComponent,
        AdCommentsComponent,
        IsAuthorizedDirective,
        GrayBtnComponent,
        SecondaryBtnComponent,
        AdCheckofferBtnComponent,
        AdTopLinksComponent,
        AdStatusViewComponent,
        DialogModule,
        NCurrencyPipe,
        VerifiedClickDirective,
        RadioButtonModule,
        RippleModule,
        ReactiveFormsModule,
        DarkBtnComponent,
        ItemsSliderComponent,
        FromNowPipe,
        NtranslatePipe,
        InputNumberModule,
        ItemDetailsSkeletonComponent,
        SwappableItemsComponent,
        HideListingPipe,
        ListingImagesUrlPipe,
        ListingInstructionsComponent,
        ListingOffersComponent,
        IsViewModePipe,
        AdvertisementIndexComponent
    ]
})
export class AdvertisementModule { }
