import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Store } from '@ngrx/store';
import { BrowserService } from '@src/app/modules/core/service/browser.service';
import { ConversionEvents } from '@src/app/shared/constants/conversion-events';
import { DialogModule } from 'primeng/dialog';
import { InputNumberModule } from 'primeng/inputnumber';
import { RippleModule } from 'primeng/ripple';
import { finalize, Subscription } from 'rxjs';
import { User } from 'src/app/authentication/models/dto/user.dto.model';
import { AdMiniClosableComponent } from 'src/app/modules/advertisement/components/ad-mini-closable/ad-mini-closable.component';
import { SellerMiniListingComponent } from 'src/app/modules/advertisement/components/seller-mini-listing/seller-mini-listing.component';
import { AdvertisementService } from 'src/app/modules/advertisement/services/advertisement.service';
import { AlertHandlerService } from 'src/app/modules/core/alerts/alert-handler.service';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { ArabicToEnglishNumeralsDirective } from 'src/app/shared/directives/arabictoenglishnum.directive';
import { VerifiedClickDirective } from 'src/app/shared/directives/isverifiedclick.directive';
import { SendMessege } from 'src/app/shared/models/chat.model';
import { ListingDetails, ListingPaymentMethod, ListingStatus, ListingView } from 'src/app/shared/models/listing.model';
import { CreateOffer } from 'src/app/shared/models/offer.model';
import { NCurrencyPipe } from 'src/app/shared/pipes/ncurrency.pipe';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { AuthService } from 'src/app/shared/services/auth.service';
import { CommonService } from 'src/app/shared/services/common.service';
import { ListingService } from 'src/app/shared/services/listing.service';
import { OfferService } from 'src/app/shared/services/offer.service';
import { getUser } from 'src/app/store/app/selectors/app.selector';
import { FormUtils } from 'src/utils/form-utils';
import { AdSellerInfoComponent } from '../ad-seller-info/ad-seller-info.component';
@UntilDestroy({ checkProperties: true })
@Component({
  selector: 'app-ad-swapp-options',
  standalone: true,
  imports: [CommonModule,
    ReactiveFormsModule,
    FormsModule,

    SellerMiniListingComponent,
    AdMiniClosableComponent,
    RippleModule,
    RouterModule,
    DialogModule,
    NCurrencyPipe,
    NtranslatePipe,
    InputNumberModule,
    ArabicToEnglishNumeralsDirective,
    VerifiedClickDirective,
    AdSellerInfoComponent
  ],
  templateUrl: './ad-swapp-options.component.html',
  styleUrls: ['./ad-swapp-options.component.scss']
})
export class AdSwappOptionsComponent implements OnInit, OnDestroy {
  formStep: number = 1;
  form: FormGroup;
  offerMenuVisible: boolean = false;
  successModelVisible: boolean = false;
  ListingStatus = ListingStatus;
  sellerPhone!: string | undefined;



  PaymentMethod = ListingPaymentMethod;

  @Input() listing: ListingDetails;
  @Input() isDesktop: boolean = false;

  @Output() onSubmit = new EventEmitter();

  @Output() onShowMobile = new EventEmitter();

  selectedList = new Set<ListingView>();

  currentUser: User;

  stepWatcher: Subscription;

  submitted: boolean;

  constructor(
    private fb: FormBuilder,
    private offerService: OfferService,
    private alertService: AlertHandlerService,
    private translateService: TranslationService,
    private _offersService: OfferService,
    private store: Store,
    private router: Router,
    private advertisementService: AdvertisementService,
    private _listingService: ListingService,
    private commonService: CommonService,
    private browser: BrowserService,
    private authService: AuthService,
  ) { }
  ngOnDestroy(): void {
    this.stepWatcher.unsubscribe();
  }

  ngOnInit(): void {

    this.sellerPhone = this.translateService.instant('Show Number');

    this.store.select(getUser).pipe(untilDestroyed(this)).subscribe(res => {
      if (!res) return;
      this.currentUser = res;
    });
    this.form = this.fb.group({
      price: [null],
    });

    this.stepWatcher = this.advertisementService.formStep.subscribe(res => {
      this.formStep = res;
    });




  }

  get forcePhoneVisible() {
    return this.advertisementService.sellerInfo?.forcePhoneVisibility;
  }

  canViewPhone() {
    return this.advertisementService.sellerInfo?.forcePhoneVisibility || this.advertisementService.sellerInfo?.phoneVisible;
  }

  scrollToComments() {
    this._listingService.scrollToComment();
  }

  showPhoneNumber() {

    this.sellerPhone = this.advertisementService.sellerInfo.phoneNumber;
    this.commonService.sendEvent('show_phone_button_clicked', 'Show Phone', this.listing.listingID);

    // Track phone number reveal
    // this.elasticLogger.logUserAction(
    //   'phone_number_revealed',
    //   this.listing.listingID.toString(),
    //   this.currentUser?.id,
    //   this.listing.name,
    //   this.listing.categoryName
    // );

    this.onShowMobile.emit();

  }

  sendEvent(event: Event) {
    this.commonService.sendEvent('show_phone_button_clicked_unlogged', 'Show Phone', this.listing.listingID);

    // Track unlogged phone reveal attempt
    // this.elasticLogger.logUserAction(
    //   'phone_reveal_attempt_unlogged',
    //   this.listing.listingID.toString(),
    //   'anonymous',
    //   this.listing.name,
    //   this.listing.categoryName
    // );
  }
  isCashable() {
    return [this.PaymentMethod.cashandswapp, this.PaymentMethod.cash].indexOf(this.listing.paymentMethod) >= 0;
  }

  isSwappable() {
    return [this.PaymentMethod.cashandswapp, this.PaymentMethod.swapp].indexOf(this.listing.paymentMethod) >= 0;
  }

  get originalPrice() {
    return parseFloat(this.form.get('price')!.value);
  }

  get price() {
    return FormUtils.safeNumber(this.form.get('price')!.value) + parseFloat(Array.from(this.selectedList).reduce((a, b) => a + b.price, 0).toString());
  }

  get selectedListIds() {
    return Array.from(this.selectedList).map(e => e.id);
  }

  addListing(event: Set<ListingView>) {

    this.selectedList.clear();
    event.forEach(element => {
      this.selectedList.add(element);
    });

    this.advertisementService.setStep(1);
    this.offerMenuVisible = false;
  }

  removeItem(event: ListingView) {
    if (this.selectedList.has(event)) {
      this.selectedList.delete(event);
    }
  }

  openList() {
    if (this.isDesktop) {
      this.offerMenuVisible = true;
    } else {
      this.advertisementService.setStep(2);
    }

  }

  resetForm() {
    this.offerMenuVisible = false;
    this.advertisementService.setStep(1);
  }

  createOffer() {
    const priceValue = this.originalPrice > 0 ? this.originalPrice : 0;
    let model: CreateOffer = {
      price: FormUtils.fixNumbers(priceValue),
      description: "",
      swappWithListing: this.listing.listingID,
      listing: this.selectedListIds
    };


    if (this.price == 0 && this.listing.price > 0) {
      this.alertService.warn({
        message: this.translateService.instant('price_non_zero')
      })
      return;
    }

    if (this.submitted) return;

    this.submitted = true;


    this.offerService.createOffer(model).pipe(finalize(() => {
      setTimeout(() => {
        this.submitted = false;
      }, 1000);
    })).subscribe((x) => {
      if (x.succeded) {


        this.commonService.pushDataLayer({ ecommerce: null });
        this.commonService.pushDataLayer({
          event: ConversionEvents.Make_Offer,
          listing_id: this.listing.listingID,
          listing_name: this.listing.name,
          category_name: this.listing.categoryName,
          seller_id: this.listing.userID,
          ecommerce: {
            transaction_id: x.data,
            value: this.price,
            currency: "EGP",
            items: [
              {
                item_id: this.listing.listingID,
                item_name: this.listing.name,
                index: 0,
                item_category: this.listing.categoryName,
                price: this.listing.price,
              }
            ]
          }
        });

        this.alertService.successOffer({
          message: this.translateService.instant('Your Offer has been Submitted Successfully!'),
          buttons: [
            { title: this.translateService.instant("Continue Browsing"), value: true },
          ]
        }, (e) => {
          if (e?.value == 1) {
            this.sendMessage(x.data, e.message);
          } else {
            this.alertService.loader({});
            this.browser.reload();

          }
        });
        this.onSubmit.emit(true);

      }
    });
  }

  sendMessage(offerid, message) {

    if (message.length == 0 || !this.currentUser) return;
    const fromUser = this.currentUser.id;
    const toUser = this.listing.userID;
    const model: SendMessege = {
      offerId: offerid,
      text: message,
      userID: fromUser.toString(),
      userName: this.currentUser.userName,
      toUserID: this.listing.userID!.toString(),
      toUserName: ""

    };
    this._offersService.sendChatMessge(model).subscribe(res => {
      this.commonService.pushDataLayer({
        event: ConversionEvents.Contact_Inquiry,
        offer_id: offerid
      }, () => {
        this.commonService.navidate('/offers/messages?id=' + offerid);

      });

    });
  }

}
