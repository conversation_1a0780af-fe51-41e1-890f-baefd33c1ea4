<div class="featured_categories">
  <div class="container">
    <div *ngIf="categoriesItems | async as categories" class="list">
      <a *ngFor="let item of categories" class="item" [routerLink]="['/category/' , item.id , item.label! | slug]">
        <div class="image">
          <img [lazyload]="item.icon!" width="73" height="55" />
        </div>
        <span>{{ item.label}}</span>
      </a>
      <div class="item banner">
        <a *ngIf="banner$ | async as banner" [routerLink]="[banner.bannerURL]">
          <img [lazyload]="banner.imageURL" height="95" width="100%" />
          <span>{{ banner.name | translate}}</span>
        </a>
      </div>
    </div>
  </div>
</div>