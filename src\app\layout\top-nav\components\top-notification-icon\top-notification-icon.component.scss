@import "mixins";
@import "variables";

:host {

    position: relative;

    &::ng-deep {

        .p-menu-overlay {
            top: 30px;
            padding: 0px;
            left: auto;
            right: 0px;
            @include rtl2(direction, rtl);

        }

        .p-menu-list {
            margin: 0px;
        }

    }
}

.notifications_holder {
    position: relative;


}


.item_img {
    width: 50px;
    height: 50px;
    border-radius: 10px;

}

.icon_img {
    position: relative;
    display: inline-block
}

.noti_type {
    // position:absolute;
    // bottom:-10px;
    // right:-20px;
    width: 30px;
    height: 30px;
}

.noti_item {
    padding: 10px 10px;
    height: auto;
    background: rgba(0, 0, 0, .04);
    overflow: hidden;

    &.not-seen {
        background: #fff;

        &::after {
            position: absolute;
            width: 10px;
            height: 10px;
            content: '';
            background: $primary;
            border-radius: 5px;
            top: 10px;
            right: 10px;

        }
    }


    .title {
        white-space: normal;
        padding-inline-end: 50px;
        line-height: 1.5;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

}

.no-message {
    font-size: 14px;
    text-align: center;
    padding: 20px 0px;
}

.noti_item {
    position: relative;

    a {
        display: flex;
        align-items: flex-start;
        gap: 10px;
        line-height: 1;
        box-sizing: content-box;
        position: relative;
    }
}

.icon_text {
    margin: 6px 0px;
}

.show_all {
    padding: 10px;
    text-align: center;
    font-size: 14px;
    background: #ececec;

    a {
        width: 100%;
        color: $text-color;

    }
}

::ng-deep app-notifications-header .notifications-header {
    margin: 0rem 0.4rem 0.4rem 0.4rem;
}