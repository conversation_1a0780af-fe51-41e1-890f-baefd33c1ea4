import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { OperationRegisterationComponent } from './page/operation-registeration/operation-registeration.component';
import { OperationIndexComponent } from './page/operation-index/operation-index.component';
import { OperationSuccessComponent } from './page/operation-success/operation-success.component';
import { OperationEditPhoneComponent } from './page/operation-edit-phone/operation-edit-phone.component';
import { OperationVerificationComponent } from './page/operation-verification/operation-verification.component';
import { OperationInterestComponent } from './page/operation-interest/operation-interest.component';

const routes: Routes = [{
  path : '' , component : OperationRegisterationComponent,
  children: [
    {
      path: 'verification',
      component: OperationVerificationComponent,
    },
    {
      path: 'edit-phone-number',
      component: OperationEditPhoneComponent,
    },
    {
      path: 'interests',
      component: OperationInterestComponent,
    },
    {
      path: 'success',
      component: OperationSuccessComponent,
    },
    {
      path: '',
      component: OperationIndexComponent,
    },
    {
      path: ':code',
      component: OperationIndexComponent,
    },
  ],
}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class OperationRegisterationRoutingModule { }
