import { Component, OnInit } from '@angular/core';
import { BrowserService } from 'src/app/modules/core/service/browser.service';
import { SuccessStepComponent } from '../../../shared/components/success-step/success-step.component';

@Component({
    selector: 'app-success',
    templateUrl: './success.component.html',
    styleUrls: ['./success.component.scss'],
    standalone: true,
    imports: [SuccessStepComponent]
})
export class SuccessComponent implements OnInit {

  constructor(private browser: BrowserService) { }
  firstName: string = '';
  ngOnInit(): void {
    this.firstName = this.browser.getStorageItem('firstName') ?? '';
  }

}
