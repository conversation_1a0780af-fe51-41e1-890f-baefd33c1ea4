import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { BrowserService } from 'src/app/modules/core/service/browser.service';
import { ProgressHeadComponent } from '../../../shared/components/progress-head/progress-head.component';
import { VerificationNumberComponent } from '../../../shared/components/verification-number/verification-number.component';
import { EditPhoneModalComponent } from '../../../shared/components/edit-phone-modal/edit-phone-modal.component';

@Component({
    selector: 'app-verification',
    templateUrl: './verification.component.html',
    styleUrls: ['./verification.component.scss'],
    encapsulation: ViewEncapsulation.None,
    standalone: true,
    imports: [
        ProgressHeadComponent,
        VerificationNumberComponent,
        EditPhoneModalComponent,
    ],
})
export class VerificationComponent implements OnInit {
  constructor(private browser: BrowserService) { }
  phoneNumber: string
  ngOnInit(): void {
    this.phoneNumber = this.browser.getStorageItem('phone') ?? '';
  }
}
