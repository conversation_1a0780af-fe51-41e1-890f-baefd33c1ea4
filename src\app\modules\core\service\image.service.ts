import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ImageService {

  constructor() { }

  base64ToBlob(base64Image: string): Blob {
    const parts = base64Image.split(';base64,');
    const mimeType = parts[0].split(':')[1];
    const imageData = parts[1];

    let byteString;
    if (mimeType.includes('base64')) {
      byteString = atob(imageData);
    } else {
      byteString = unescape(imageData);
    }

    const byteNumbers = new Array(byteString.length);
    for (let i = 0; i < byteString.length; i++) {
      byteNumbers[i] = byteString.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);

    return new Blob([byteArray], { type: mimeType });
  }
}
