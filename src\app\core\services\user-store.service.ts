import { computed, effect, inject, Injectable, signal } from "@angular/core";
import { Actions, ofType } from "@ngrx/effects";
import { User } from "@src/app/authentication/models/dto/user.dto.model";
import { setUser } from "@src/app/store/app/actions/app.actions";

@Injectable({
    providedIn: 'root'
})
export class UserStoreService {

    private readonly actions$ = inject(Actions);

    private readonly _user = signal<User | null>(null);

    public readonly user = this._user.asReadonly();

    public readonly isAuthenticated = computed(() => this._user != null);

    constructor() {
        effect(() => {
            this.actions$.pipe(
                ofType(setUser)
            ).subscribe(action => {
                console.log(action);

                this.setUser(action.user);
            });
        });
    }

    public setUser(user: User): void {
        this._user.set(user);
    }

    public updateUser(updates: Partial<User>): void {
        const currentUser = this._user();
        if (currentUser) {
            this._user.set({ ...currentUser, ...updates });
        }
    }

    public clearUser(): void {
        this._user.set(null);
    }

    public logout(): void {
        this.clearUser();
    }
}