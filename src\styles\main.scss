/* You can add global styles to this file, and also import other style files */

@import "variables";
@import "mixins";

@import "bootstrap/bootstrap.scss";
@import "style-files/slide_menu.scss";
@import "style-files/breadcrumb.scss";

// common
@import "style-files/common/ui.scss";
@import "style-files/common/form.scss";
@import "style-files/common/pagination.scss";




@import "style-files/base";
@import "style-files/utilities";
@import "style-files/rtl";



:root {
  --ng-progress-color: linear-gradient(to right, var(--primary-color), var(--secondary-color));
}



.p-carousel-item {
  flex: 1 0 100%;
}

body:not(.no-scroll) {
  // .p-component-overlay.p-sidebar-mask.p-component-overlay-enter.p-component-overlay-leave:empty {
  //   display: none;
  // }
}





*::placeholder {
  font-size: 1.125rem;
  font-weight: 400;
  color: #2d3142;
}

::placeholder {
  font-size: 1.125rem;
  font-weight: 400;
  color: #2d3142;
}

.container,
.container-fluid {
  padding: 0px;
}

.smartmatchingIcon {
  display: flex;
  align-items: center;
  gap: 5px;
}




@media screen and (min-width:1200px) {
  body {
    --side-menu-top-pos: 70px;
  }
}


.p-button.p-button-rounded {
  border-radius: 2rem;
}

.p-button.p-button-icon-only {
  width: 3rem;
  padding: 0.75rem 0;
}


.p-carousel-prev,
.p-carousel-next {
  position: absolute;
  top: 50%;
  left: 0px;
  z-index: 2;
  background: transparent;
  color: #6c757d;
  opacity: 0.8;
  padding: 10px;
  transform: translateY(-50%);
  color: #fff;

  svg {
    width: 20px;
    height: 20px;
  }
}

.p-carousel-next {
  left: auto;
  right: 0;
}

.p-carousel-prev:hover,
.p-carousel-next:hover {
  opacity: 1;
}

.p-dialog .p-dialog-content {
  background: #fff;
}

.no-scroll {
  overflow: hidden;
}

.p-dialog {
  max-height: inherit !important;
}

.body_wrap {
  padding-top: 10px;
}

.notifi_body {
  border-radius: 10px;
  padding: 0px;
}

.sticky_submit.submit {
  position: sticky;
  bottom: 0px;
  z-index: 10;
  padding: 10px !important;
  background: #f7f7fb;

  @media screen and (min-width:990px) {
    background: transparent;
    width: 50%;
    margin-inline-start: auto;
  }

}

.suit_for_content_style {
  margin: 0px !important;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  overflow: hidden;

  .p-dialog-content {
    padding: 0px !important;
  }

  .p-dialog-header-icons {
    display: none !important;
  }
}



.p-dialog.alertModal .p-dialog-header .p-dialog-header-icon:last-child,
.p-dialog.messageModal .p-dialog-header .p-dialog-header-icon:last-child {
  color: #fff;
  transform: translateY(-40px);
  background: #7d7d7d;
}


.p-dialog .p-dialog-header {
  padding: 0rem;
}

.offer_form {
  .p-dialog-content {
    background: #fff;
    padding: 0px;
  }

}


app-minipage {
  line-height: 2;
  margin: 4rem 0px;
  display: block;

  h2,
  h3 {
    font-size: 1rem;
    margin: 1rem 0px;
    font-weight: 700;
    margin-top: 1.8rem;

    +p,
    +ul,
    +ol {
      line-height: 1.8;
    }
  }

  a {
    font-weight: bold;
  }

}



.p-dialog.alertModal,
.p-dialog.messageModal {
  position: absolute;
  bottom: 0px;

  form {
    margin: 0px
  }


  .list-container {
    padding: 0px;
  }



  .p-dialog-header .p-dialog-header-icon:last-child {
    color: #fff;
  }

  .p-dialog-content:last-of-type {
    border-bottom-right-radius: 0px;
    border-bottom-left-radius: 0px;
  }

  .alert-body {
    text-align: center;

    h2 {
      font-size: 1rem;
    }

    svg {
      width: clamp(70px, 36vw, 170px);
      height: clamp(70px, 36vw, 170px);
      margin-bottom: 20px;
    }

    button {
      width: 100%;
      border-radius: 43px;
      background: #2D3142;
      border: #2D3142;
      font-size: 1rem;
      margin-top: 30px;

      .p-button-label {
        font-weight: normal;
      }
    }

    .btns_list {
      margin-top: 30px;

      button {
        margin-top: 10px;
      }
    }


  }

}

.p-dialog.messageModal {
  .p-dialog-header {
    padding: 0.5rem;
  }
}

.mw-form {
  max-width: 400px;
}



.notification_icon {
  display: inline-flex;
  border-radius: 50%;
  justify-content: center;
  align-items: center;

  img {
    width: 20px;
    height: 25px;
  }
}

.notifications_holder {
  margin-inline-end: 16px;

  .notification_btn {
    padding: 0;
    min-width: auto;
    line-height: 20px;
    position: relative;
    transform: translateY(-1px);

    .notification_count {
      position: absolute;
      min-width: 21px;
      height: 21px;
      background-color: var(--primary-color);
      color: #fff;
      top: 0px;
      left: 14px;
      border-radius: 10px;
      -webkit-border-radius: 10px;
      -moz-border-radius: 10px;
      -ms-border-radius: 10px;
      -o-border-radius: 10px;
      line-height: 0.8;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1rem;
      font-weight: 600;
      // @include ltr(transform, translate(50%, -50%));
      // @include rtl(transform, translate(-50%, -50%));
    }
  }

  @include Large {
    margin-inline-end: 20px;
    margin-inline-start: 0px;
  }


}

body.appview {
  app-open-app-nav,
  app-search-listing,
  app-top-right-menu,
  .footer_mobile,
  .footer {
    display: none;

  }

  app-top-left-menu {
    pointer-events: none;
  }

  .header_wrapper {
    margin-bottom: 0px;
  }


}

html[dir=ltr] .compbox {
  direction: rtl;
  // transform: translateX(-10px);
}



html[dir=rtl] .container,
html[dir=rtl] .container-fluid,
html[dir=rtl] .container-xxl,
html[dir=rtl] .container-xl,
html[dir=rtl] .container-lg,
html[dir=rtl] .container-md,
html[dir=rtl] .container-sm {
  // padding: 0px;
  // margin: 0px auto;
}

html[dir=rtl] [type=tel],
html[dir=rtl] [type=url],
html[dir=rtl] [type=email],
html[dir=rtl] [type=number] {
  direction: rtl;
}

html[dir=rtl] .row>* {
  // padding: 0px;
  // margin: 0px;
}

html[dir=rtl] {

  --ng-progress-color: linear-gradient(to right, var(--secondary-color), var(--primary-color));

  .p-carousel-items-content {
    direction: ltr;
  }

  .smartmatchingIcon svg {
    transform: scaleX(-1);
  }

  .p-datepicker-header {
    direction: ltr;
  }

  .p-toast .p-toast-message .p-toast-message-content .p-toast-message-text {
    margin: 0px 1rem 0px 0px;
  }


  .ql-editor ol li:not(.ql-direction-rtl),
  .ql-editor ul li:not(.ql-direction-rtl) {
    padding-right: 1.5em;
    padding-left: 0px;
  }

  .ql-editor li:not(.ql-direction-rtl)::before {
    margin-right: -1.5em;
    margin-left: 0.3em;
    text-align: start;
  }

  .p-dropdown-panel .p-dropdown-header .p-dropdown-filter {
    padding-right: 0.75rem;
    padding-left: 1.75rem;
  }

  .p-dropdown-panel .p-dropdown-header .p-dropdown-filter-icon {
    right: auto;
    left: 0.75rem;
  }

  .p-accordion .p-accordion-header .p-accordion-header-link .p-accordion-toggle-icon {
    transform: rotate(180deg);
  }

  .ltr_text {
    direction: ltr;
  }

  .noti_item {
    &.not-seen {
      &::after {
        right: auto;
        left: 10px;

      }
    }
  }

  .noti_type {
    right: auto;
    left: -20px;
  }

  .hero_slider {
    .p-carousel-indicators {
      right: 4%;
      left: auto;
      direction: rtl;

      @include Large {
        right: 5%;
        left: auto;
      }
    }
  }


  .back_logo {
    .back_icon {
      transform: rotate(180deg);
    }
  }

  .p-slidemenu .p-menuitem-link .p-submenu-icon {
    transform: rotate(180deg);
  }

  .p-sidebar .p-sidebar-header+.p-sidebar-content h2 .pi {
    transform: rotate(180deg);
  }
}

.successAlert {
  .p-dialog {
    max-width: 550px;
  }
}

.successAlert .p-dialog-content {
  padding: 0px !important;
}

.successAlert .p-dialog-content app-dark-btn {
  margin: 50px 0px;
  width: 60%;
}

.swappable_icon {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  background: #f6ebff;
  font-size: 0.68rem;
  font-weight: 600;
  text-transform: capitalize;
  color: var(--secondary-color);
  padding: 5px 10px;
  border-radius: 21px;
  font-style: italic;
  flex-shrink: 0;
  flex-grow: 0;

  svg {
    width: 25px;
    height: 20px;
  }

}


.loader_place {
  display: none;
  justify-content: center;
  position: relative;
  width: 100%;
  transform: translateY(66px);

  &.active {
    display: flex;
  }

  .loaderIcon {
    display: inline-block;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-inline-end: 10px;
    border-top: 3px solid var(--primary-color);
    border-right: 3px solid transparent;
    box-sizing: border-box;
    animation: loaderRotation 1s linear infinite;
  }
}



.loaderModal .p-dialog-header {
  background: transparent;
}


.loaderModal {
  box-shadow: none;
  max-width: min-content !important;
}


.loaderModal .p-dialog-content {
  background: transparent;
  padding: 0px;
  overflow: visible;
}

.p-menu ul {
  max-height: 60vh;
  overflow: auto;
}

.p-sidebar .p-sidebar-header+.p-sidebar-content {
  height: calc(100% - 65px) !important;
}

.p-menu {
  width: auto;

  ul {
    padding: 0px !important;
  }
}

.notifi_body {
  min-width: 340px !important;
  max-width: 350px !important;
  overflow: hidden;
  max-height: none;



  .notifi_head {
    padding: 16px;
    font-size: 1.125rem;
    font-weight: 800;
    color: #14213d;
  }

  .noti_item {
    font-size: 14px;
  }

  .notifi_content {
    flex-grow: 1;
    overflow: auto;
    max-height: 360px;
  }

  .notifi_foot {
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: center;

    a {
      padding: 16px 0;
      border-top: 1px solid #f7f7fb;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

    }

  }
}




.filterPopup .p-dialog-content {
  padding: 0px;
}

// .filterPopup .p-dialog-header-icons {
//   position: absolute;
//   top: -55px;
//   right: 10px;
//   border-radius: 50%;
//   background: #ffffff40;
//   width: 44px;
//   height: 44px;
//   display: flex;
//   justify-content: center;
//   align-items: center;
// }


// .filterPopup .p-dialog-draggable .p-dialog-header {
//   padding: 0px;
// }

// .filterPopup .p-dialog .p-dialog-header .p-dialog-header-icon .pi {
//   font-size: 15px;
//   color: #fff;
// }


@media screen and (min-width:990px) {

  .p-dialog.alertModal,
  .p-dialog.messageModal {
    position: relative;
    bottom: auto;

    .p-dialog-header {
      border-top-right-radius: 12px;
      border-top-left-radius: 12px;
    }

    .p-dialog-header-icons {
      right: 0px;
    }

    .p-dialog-content:last-of-type {
      border-bottom-right-radius: 12px;
      border-bottom-left-radius: 12px;
    }
  }





}

.p-password {
  .pi {
    @include rtl(right, auto !important);
    @include rtl(left, 0.75rem);
  }
}

.p-component-overlay {
  background-color: #2d3142b2;
}

.input_holder {
  padding: 20px;
  padding-inline-end: 40px;
  background-color: #fff;
  @include borderRadius(64px);
  width: 100%;
  border: 1px solid transparent !important;
  box-shadow: none !important;
  @include transition(0.3s);

  &.error_inpt {
    border: 1px solid var(--primary-color) !important;
  }
}

.custom_checkBox {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;

  p-checkbox {
    flex-shrink: 0;

    .p-checkbox {
      &-box {
        width: 24px;
        height: 24px;
        border-color: rgba(#2d3142, 0.3);
        @include transition(0.3s);

        &.p-focus {
          box-shadow: none !important;
        }

        &.p-highlight {
          background-color: var(--secondary-color);
          border-color: var(--secondary-color);
        }
      }
    }
  }

  label {
    font-size: 0.875rem;
    font-weight: 400;
    color: #2d3142;
  }
}

.custom_radioBtn {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 18px;

  p-radiobutton {
    flex-shrink: 0;

    .p-radiobutton {
      width: 24px;
      height: 24px;

      &-box {
        width: 24px;
        height: 24px;
        background-color: #fff !important;
        border-color: rgba(#2d3142, 0.3) !important;
        @include transition(0.3s);

        .p-radiobutton-icon {
          background-color: var(--secondary-color) !important;
          background-image: url("../assets/img/check-icon.svg");
          background-repeat: no-repeat;
          background-position: center;
          background-size: auto;
          width: 14px;
          height: 14px;
          opacity: 0;
          visibility: hidden;
          @include transition(0.3s);
        }

        &.p-focus {
          box-shadow: none !important;
        }

        &.p-highlight {
          background-color: var(--secondary-color) !important;
          border-color: var(--secondary-color) !important;

          .p-radiobutton-icon {
            opacity: 1;
            visibility: visible;
          }
        }
      }
    }
  }

  label {
    font-size: 1.125rem;
    font-weight: 400;
    color: #2d3142;
  }
}

.row {
  margin: 0px;
}

.auth_form_wrapper {
  .secondary_btn {
    &:disabled {
      background-color: #2d31421a !important;
      color: #2d3142 !important;
      opacity: 1 !important;

      span {
        color: #2d3142 !important;
      }
    }
  }
}

// Back Btn & Logo [Responsive]
.back_logo {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 16px;
  margin-bottom: clamp(16px, 11vw, 56px);

  .back_icon {
    flex-shrink: 0;
    padding: 4px;
    background-color: transparent !important;
    border: 0 !important;
    box-shadow: none !important;
    color: rgba(#2d3142, 0.25);
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
    }
  }

  .app_logo {
    padding: 0;
    background-color: transparent !important;
    border: 0 !important;
    box-shadow: none !important;
    width: 100%;
    max-width: 112px;

    img {
      width: 100%;
    }
  }
}



.p-sidebar .p-sidebar-header+.p-sidebar-content {
  height: 100%;
  overflow-x: hidden;
}

.required_drop .p-dropdown {
  border: 1px solid $orangeColor !important;
}

.required_input {
  // text-shadow: 0px 0px 10px $orangeColor;
  border: 1px solid $orangeColor !important;
  position: relative;




}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-background-clip: text;
  -webkit-text-fill-color: #2d3142;
  transition: background-color 5000s ease-in-out 0s;
  box-shadow: inset 0 0 20px 20px rgba(45, 49, 66, 0.0509803922);
  -webkit-box-shadow: 0 0 0px 1000px rgb(255 255 255 / 5%) inset;
}

.icon_text {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 5px;
  margin-bottom: 14px;
  line-height: normal;
  flex-shrink: 0;

  svg {
    flex-shrink: 0;
    width: 12px;
    height: 12px;
  }

  span {
    font-size: 0.85rem;
    font-weight: 400;
    color: rgba(#3b4158, 0.6);
  }
}

.toast-container {
  position: fixed;
}

.general-success-model {
  .modelTop {
    padding: clamp(50px, 15vw, 117px) 20px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    app-success-icon {
      position: absolute;
      top: -70px;
      left: 50%;
      transform: translateX(-50%);
    }
  }

  .modalTitle {
    min-height: 90px;
    font-size: 1.4rem;
    text-align: center;
    width: 80%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .model_actions {
    padding: 0px;
  }

  &.closable .p-dialog-header {
    display: none;
  }

  &.warnmode,
  &.errormode,
  &.normalmode {
    .modelTop {
      padding: 30px 0px 0px;

    }
  }

}




p-dialog {

  .p-dialog {
    .p-dialog-header {
      padding: 0px;
    }
  }

  .p-dialog .p-dialog-header .p-dialog-header-icon:last-child {
    position: absolute;
    top: -52px;
    right: 10px;
    background: $text-color;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;

    .pi {
      color: #fff;
    }
  }


  .p-dialog-mask {
    align-items: end;
  }

  .p-dialog-content {
    border-top-left-radius: 0px !important;
    border-top-right-radius: 0px !important;
  }


  .p-dialog-footer {
    border-bottom-left-radius: 0px !important;
    border-bottom-right-radius: 0px !important;
  }



  @include Large {

    .body_wrap {
      padding-top: 30px;
    }

    .p-dialog-mask {
      align-items: center;
    }


    .p-dialog-footer {
      border-bottom-left-radius: 12px !important;
      border-bottom-right-radius: 12px !important;
    }
  }
}

.p-dialog {
  width: 100% !important;
  max-width: 446px;



  /*
  @media (max-width: 1199.98px) {
    width: 60vw !important;
  }

  @media (max-width: 991.98px) {
    width: 70vw !important;
  }

  @media (max-width: 575.98px) {
    width: 90vw !important;
  }
  */
}

.no_result {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 60px 0px;
}

.no_result img {
  width: 80%;
  max-width: 650px;
  margin: 20px 0px;
}

.no_result h2 {
  font-size: 1.25rem;
}

.no_result h3 {
  font-size: 1rem !important;
  font-style: normal !important;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

.p-dialog .p-dialog-header-icons {
  height: 6px;
}

.warn_note {
  font-size: 0.875rem;
  margin-top: 20px;
  display: flex;
  gap: 10px;
  align-items: center;
  color: #414141;
}

.warn_note .pi {
  color: var(--primary-color);
  font-size: 1.25rem;
}

.sideMenu {
  top: var(--side-menu-top-pos) !important;

  &+.p-component-overlay {
    top: var(--side-menu-top-pos);
  }

  .p-sidebar-header {
    display: none;
  }
}

button,
input {
  border: none;
}

.filter_section {
  background: $secondary;

  // @media (max-width: 767.98px) {
  //   padding: 30px 0px;
  //   background: #f7f7fb;
  // }

  .filters_sidebar {
    height: 100%;
    background-color: #fff;
    padding: 25px;

    // @media (max-width: 767.98px) {
    //   padding: 0;
    // }
  }

  .listing_side_filter {
    max-width: 505px;

    @include XLarge {
      width: 380px;
      padding: 0px;
    }
  }

  .listing_result {
    max-width: 1150px;
    padding: 0px 15px;

    @include XLarge {
      width: calc(100% - 380px);
    }
  }






  .new_listing {
    background-color: #f7f7fb;
    padding: 0px 0px 58px 0px;

    .p-paginator {
      margin-top: 35px;
      background: rgb(255, 255, 255);
      background: -moz-linear-gradient(0deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 65%);
      background: -webkit-linear-gradient(0deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 65%);
      background: linear-gradient(0deg, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 65%);
      filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#ffffff", endColorstr="#ffffff", GradientType=1);
      padding-bottom: 20px;
    }



    // @media (max-width: 767.98px) {
    //   padding: 16px 16px 33px 16px;
    // }

    h3,
    h1 {
      font-size: 1.125rem;
      font-weight: 400;
      color: #2d3142;
      margin-bottom: 31px;
      line-height: normal;
    }



    .filters_btn {
      background: transparent;

      padding: 0px !important;
      color: $text-color;
      font-size: 0.875rem;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      border: none;

      svg {
        flex-shrink: 0;
        width: 14px;
        height: 15px;
      }
    }

    &_head {
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 20px;
      flex-wrap: wrap;

      w h3,
      h1 {
        font-size: clamp(18px, 20vw, 20px);
        font-weight: 400;
        color: #2d3142;
        margin-bottom: 0;
        line-height: normal;
        width: 100%;
      }


    }

  }
}

.head_right {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 24px;

  .layout_btns {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;


  }

}

.new_listing {



  .grid_layout {
    grid-template-columns: repeat(1, 1fr);
    display: grid;
    grid-gap: 9px;

    @include Large {
      grid-template-columns: repeat(2, 1fr);
    }

    @include XLarge {
      grid-template-columns: repeat(3, 1fr);
      grid-gap: 20px;
    }

    .item_card {
      page-break-inside: avoid;
      break-inside: avoid;
    }
  }

  .row_layout {
    app-item-card-row {
      &:not(:last-child) {
        display: block;
        margin-bottom: 18px;
      }
    }
  }
}

.filter_boxes {
  .filter_box {
    &:not(:last-child) {
      padding-bottom: 20px;
      margin-bottom: 20px;
      border-bottom: 1px solid rgba(133, 133, 133, 0.3);
    }
  }
}

.new_listing_head {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.radio_items {
  display: flex;

  .p-radiobutton .p-radiobutton-box .p-radiobutton-icon {
    background: url(/assets/img/check-icon.svg) $orangeColor no-repeat center center;
    background-color: $orangeColor;
  }

  .radio_item {
    display: flex;
    gap: 12px;

    label {
      font-size: 1rem;
    }

  }
}


@include Large {

  .filter_section {
    .new_listing {
      padding: 0px 0px 0px 30px;

      h1 {
        margin-bottom: 0px;
      }
    }

    .container-fluid {
      padding: 0px;
    }

    .col-lg-4 {
      padding: 0px;
    }

  }


}

@include XLarge {
  .filter_section {
    .listing_result {
      padding: 47px 0px 0px 0px;
      padding-inline-start: 45px;
    }

    .new_listing_head {
      margin-top: 0px;

      h1 {
        width: auto;
      }
    }

    .new_listing {
      padding: 0px 0px 0px 0px
    }
  }

  .side-col {
    padding-inline-start: 6vw;
  }

  .max-container {
    max-width: clamp(10vw, 42vw, 580px);
    margin-inline-start: auto;
  }
}

@include Wide {
  .side-col {
    padding-inline-start: 8vw;
  }

  .max-container {
    max-width: 40vw;
  }
}



@keyframes loaderRotation {
  0% {
    transform: translateY(-50%) rotate(0deg);
  }

  100% {
    transform: translateY(-50%) rotate(360deg);
  }
}