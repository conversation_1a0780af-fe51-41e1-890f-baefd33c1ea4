import { Component, OnInit } from '@angular/core';
import { ProgressHeadComponent } from '../../../shared/components/progress-head/progress-head.component';
import { ChooseUrCityComponent } from '../../../shared/components/choose-ur-city/choose-ur-city.component';
import { NtranslatePipe } from '../../../shared/pipes/ntranslate.pipe';

@Component({
    selector: 'app-choose-city',
    templateUrl: './choose-city.component.html',
    styleUrls: ['./choose-city.component.scss'],
    standalone: true,
    imports: [
        ProgressHeadComponent,
        ChooseUrCityComponent,
        NtranslatePipe,
    ],
})
export class ChooseCityComponent implements OnInit {
  constructor() { }

  ngOnInit(): void { }
}
