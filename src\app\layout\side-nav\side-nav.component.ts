import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { RouterModule } from '@angular/router';
import { MetaService } from '@src/app/modules/core/service/meta.service';
import { GeneratedHrefPipe } from '@src/app/shared/pipes/generated-href.pipe';
import { MenuItem } from 'primeng/api';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { LazyloadDirective } from 'src/app/shared/directives/lazyload.directive';
import { CategoryMenuDTO } from "src/app/shared/models/lookup.model";
import { DeviceDetectionService } from 'src/app/shared/services/device-detection.service';

interface CategoryLevel extends MenuItem {
  level: number;
  active?: boolean;
}

@Component({
  selector: 'app-side-nav',
  templateUrl: './side-nav.component.html',
  styleUrls: ['./side-nav.component.scss'],
  standalone: true,
  providers: [GeneratedHrefPipe],
  imports: [CommonModule, RouterModule, LazyloadDirective]
})
export class SideNavComponent implements OnInit {

  index: number = 0;
  list: MenuItem[];
  children: CategoryLevel[] = [];
  oldItem!: CategoryLevel | null;
  activeMode: boolean = false;
  @Output() onChoose = new EventEmitter();
  @Input()
  set items(value: CategoryMenuDTO[]) {
    this._items = value;
    if (this._items) {
      this.list = this.convertToMenuItems(this._items);

    }
  }

  _items: CategoryMenuDTO[];
  constructor(
    public device: DeviceDetectionService,
    private ts: TranslationService,
    private metaService: MetaService,
    private linkGeneratorPipe: GeneratedHrefPipe
  ) { }

  ngOnInit(): void {

  }

  triggerChoose(e) {
    this.activeMode = false;
    if (this.oldItem) {
      this.oldItem.active = false;
    }
    this.activeMode = false;
    this.children = [];
    this.oldItem = null;
    this.onChoose.emit(true)
  }

  convertToMenuItems(array: CategoryMenuDTO[], parentId: number = 0): MenuItem[] {

    return array.map((item: CategoryMenuDTO) => {
      let image = '';
      if (item.images.length == 1) {
        image = item.images[0].url;
      }

      if (item.images.length > 1) {
        const index = item.images.findIndex(element => element.type == 'LabelIcon');
        if (index >= 0) {
          image = item.images[index].url;
        } else {
          image = item.images[0].url;
        }
      }
      const menuItem: MenuItem = {
        id: item.id.toString(),
        label: this.ts.instant(item.name),
      };

      if (item.subs && item.subs.length > 0) {
        menuItem.icon = image;
        menuItem.items = [];

        //const url = this.linkGeneratorPipe.transform(['category', item.id, this.slug(this.ts.instant(item.name))]);
        //this.metaService.setPrefetch(url, url);


        menuItem.items = menuItem.items.concat(this.convertToMenuItems(item.subs, item.id));
        menuItem.items.push({
          id: item.id.toString(),
          label: this.ts.instant('All_' + item.name),
          routerLink: ['/category/' + item.id + '/' + this.slug(this.ts.instant(item.name))],
          styleClass: 'all_link'
        });

      } else {
        //const url = this.linkGeneratorPipe.transform(['category', ...(parentId ? [parentId, item.id, this.slug(this.ts.instant(item.name))] : [item.id, this.slug(this.ts.instant(item.name))])]);
        //this.metaService.setPrefetch(url, url);

        menuItem.routerLink = ['/category/' + (parentId ? (parentId + '/' + item.id + '/' + this.slug(this.ts.instant(item.name))) : item.id + '/' + this.slug(this.ts.instant(item.name)))];


      }

      return menuItem;
    });
  }

  trackby(index, item) {
    return item.label;
  }

  slug(value: string) {
    return value.toString().toLowerCase().replace(/ /g, '-');
  }

  expand(e: CategoryLevel | MenuItem | any) {
    if (this.oldItem) {
      this.oldItem.active = false;
    }
    e.active = true;
    this.activeMode = true;
    if (e.items!.length > 0) {
      this.children = this.children.splice(e.level, this.children.length - 1);
      this.children.push(e);
    } else {
      this.children = [];
    }
    this.oldItem = e;

  }

  backMenu(e) {
    if (this.oldItem) {
      this.oldItem.active = false;
    }
    this.activeMode = false;
    this.children = [];
    this.oldItem = null;
  }

}
