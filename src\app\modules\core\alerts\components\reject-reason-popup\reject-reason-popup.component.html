<form [formGroup]="form" class="reason_form">
    <div class="modelTop reasons_box">
        <h3>{{ 'cancellation_reason' | translate}}</h3>
        <ng-container *ngIf="reasons$ | async as reasons">
            <div *ngFor="let reason of reasons" class="reason_item">
                <p-radioButton [inputId]="reason.id.toString()" formControlName="reasonId"
                    [value]="reason.id"></p-radioButton>
                <label [for]="reason.id">{{ reason.name | translate }}</label>
            </div>
        </ng-container>

    </div>
    <div class="model_actions">
        <app-dark-btn [btnDisabled]="!form.value.reasonId" [btnText]="'OK' | translate" btnType="button" [bigBtn]="true"
            (click)="reason()"></app-dark-btn>
        <p (click)="cancel()">{{'Skip' | translate}}</p>
    </div>
</form>