import { CommonModule, LocationStrategy } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { SearchListingComponent } from 'src/app/shared/components/search-listing/search-listing.component';
import { NotificationDto } from 'src/app/shared/models/notification.model';
import { TopLeftMenuComponent } from './components/top-left-menu/top-left-menu.component';
import { TopRightMenuComponent } from './components/top-right-menu/top-right-menu.component';
@Component({
  selector: 'app-top-nav',
  templateUrl: './top-nav.component.html',
  styleUrls: ['./top-nav.component.scss'],
  standalone: true,
  imports: [CommonModule, TopLeftMenuComponent, TopRightMenuComponent, SearchListingComponent],
})

export class TopNavComponent implements OnInit {

  notifications: NotificationDto[];
  isPopState = false;





  constructor(
    private locStrat: LocationStrategy,


  ) {






  }
  ngOnInit(): void {
    this.locStrat.onPopState(() => {
      this.isPopState = true;
    });

  }




}
