import { ApplicationRef, Injectable, inject } from '@angular/core';

@Injectable({ providedIn: 'root' })
export class GtmService {
    private appRef = inject(ApplicationRef);

    initializeGtm() {
        // Wait for app to be stable (initial render complete)
        this.appRef.isStable.subscribe(stable => {
            if (stable) {


                // Add 3 second delay
                setTimeout(async () => {

                    const { GoogleTagManagerModule } = await import('angular-google-tag-manager');
                    const { importProvidersFrom } = await import('@angular/core');

                    // Dynamically add GTM provider
                    const provider = importProvidersFrom(
                        GoogleTagManagerModule.forRoot({
                            id: 'GTM-PCZ9LRFC',
                        })
                    );

                    // Add provider to application
                    const injector = this.appRef.injector;
                    injector.get(provider);
                }, 3000);
            }
        });
    }
}
