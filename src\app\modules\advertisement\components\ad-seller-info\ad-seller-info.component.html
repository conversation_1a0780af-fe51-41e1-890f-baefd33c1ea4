<div class="seller_box" *ngIf="seller">
    <a class="seller_icon" [routerLink]="['/seller', seller.userID]">
        <app-seller-listing-image *ngIf="!isBusinessInfo" [seller]="seller"></app-seller-listing-image>
        <div class="seller_info_box" [ngStyle]="shouldApplyMargin()">
            <div class="seller_info">
                <h2> {{ seller.businessName && isBusinessInfo ? seller.businessName : (seller.firstName + ' ' +
                    seller.lastName) }}</h2>
                <span>
                    <svg data-id="rate_star" width="14" height="14" viewBox="0 0 14 14" fill="none">
                        <path
                            d="M6.14024 1.44742C6.52776 0.795014 7.47223 0.795014 7.85976 1.44742L9.39464 4.0314C9.53497 4.26765 9.76633 4.43574 10.0344 4.4962L12.9662 5.15746C13.7064 5.32441 13.9983 6.22266 13.4976 6.79282L11.5143 9.05107C11.333 9.25754 11.2447 9.52952 11.27 9.80313L11.5471 12.7958C11.617 13.5514 10.8529 14.1065 10.1559 13.8065L7.39538 12.6182C7.14299 12.5096 6.85701 12.5096 6.60462 12.6182L3.84405 13.8065C3.14706 14.1065 2.38297 13.5514 2.45293 12.7958L2.73001 9.80313C2.75535 9.52952 2.66698 9.25754 2.48565 9.05107L0.502445 6.79282C0.00172246 6.22266 0.293579 5.32441 1.03381 5.15746L3.96562 4.4962C4.23367 4.43574 4.46503 4.26765 4.60536 4.0314L6.14024 1.44742Z"
                            fill="currentColor" />
                    </svg>
                    <span>{{ seller.rating | round }} ({{ seller.ratingCount }})</span>
                </span>
                <div *ngIf="isBusinessWithoutImage && !showSeeProfile">
                    {{'See Profile' | translate}} <span class="pi pi-chevron-right"></span>
                </div>

            </div>

            <div *ngIf="isBusinessWithoutImage;else seeProfile" class="bussnisLogo">
                <img [lazyload]=" listing.user.imageURL" />
            </div>
            <ng-template #seeProfile>
                <div *ngIf="!showSeeProfile">
                    {{'See Profile' | translate}} <span class="pi pi-chevron-right"></span>
                </div>
            </ng-template>

        </div>

    </a>

</div>