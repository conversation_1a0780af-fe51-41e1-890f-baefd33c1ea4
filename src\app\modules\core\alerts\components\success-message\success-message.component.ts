import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { RippleModule } from 'primeng/ripple';
import { DarkBtnComponent } from 'src/app/shared/components/dark-btn/dark-btn.component';
import { GrayBtnComponent } from 'src/app/shared/components/gray-btn/gray-btn.component';
import { SuccessIconComponent } from 'src/app/shared/components/success-icon/success-icon.component';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';

@Component({
  selector: 'app-success-message',
  standalone: true,
  imports: [CommonModule, RippleModule, ButtonModule, SuccessIconComponent, DarkBtnComponent, GrayBtnComponent, NtranslatePipe],
  templateUrl: './success-message.component.html',
  styleUrls: ['./success-message.component.scss']
})
export class SuccessMessageComponent implements OnInit {

  message: string;
  btns: any = [];

  constructor(private ref: DynamicDialogRef, public config: DynamicDialogConfig) { }

  ngOnInit(): void {
    this.message = this.config.data.message;
    if (this.config.data.buttons) {
      this.btns = this.config.data.buttons;
    }
  }

  close(value) {
    if (this.ref) {
      this.ref.close(value);
    }
  }

}
