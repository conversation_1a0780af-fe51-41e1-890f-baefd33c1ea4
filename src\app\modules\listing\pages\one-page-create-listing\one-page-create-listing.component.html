@defer {

<div class="container">
  <p-dialog header=""
            [closable]="true"
            [modal]="true"
            [(visible)]="modalVisible"
            [style]="{ width: '80vw' }"
            [className]="'notClosable successAlert'"
            (onHide)="gotoHome()">
    <div class="modelTop">
      <app-success-icon></app-success-icon>
      <div class="modalTitle">{{ 'SUCCESS' | translate}}</div>
      <div class="modelContent">
        {{ ((!isEdit) ? 'UNDER REVIEW' : 'UPATED SUCCESSFULLY') | translate }}
      </div>

      <app-dark-btn id="goto_my_listings_btn"
                    [btnText]="'My Listings' | translate"
                    btnType="button"
                    [bigBtn]="true"
                    [routerLink]="[!isEdit ? '/listing/list/waiting' : '/listing/list/waiting']"></app-dark-btn>

      <img src="assets/img/success-img.png"
           alt=""
           class="success_img" />
    </div>

  </p-dialog>

  <div class="tabHeader">
    <div *ngIf="(id == 0 && tabIndex > 2) || (id > 0 && tabIndex > 2)"
         class="back_holder"
         [ngClass]="{ active: selectMainCategory }">
      <div *ngIf="activeSection && tabIndex == 1 && device.isMobile"
           class="over_btn"
           (click)="activeSection = false">
      </div>
      <button id="create_listing_back_btn"
              pButton
              pRipple
              type="button"
              icon="pi pi-chevron-left"
              class="p-button-rounded back_btn"
              (click)="back()"></button>
    </div>

    {{ headerTitles[tabIndex - 1] }}
  </div>

  <ng-container *ngIf="tabIndex == 3">
    <app-form-listing-details-review [locationName]="locationName"
                                     [details]="details"
                                     [images]="detailImages"
                                     [props]="tempProps"
                                     [intersetedCategories]="intersetedCategories!"
                                     (onSubmit)="createListing($event)"
                                     (onEdit)="tabIndex = 1"
                                     [mainCategory]="selectedCategory!"
                                     [subCategory]="selectedSubCategory!"></app-form-listing-details-review>
  </ng-container>

  <div *ngIf="tabIndex == 2"
       class="row form_area">

    <div class="col-md-6">
      <app-form-active-category [editMode]="id > 0"
                                [mainCategory]="selectedCategory!"
                                [subCategory]="selectedSubCategory!"
                                (back)="restart()"></app-form-active-category>

      <app-form-listing-details-props [formPosition]="ListingPropsFormPosition.BeforeForm"
                                      [props]="props"
                                      [mainCategory]="selectedCategory!"
                                      [subCategory]="selectedSubCategory!"
                                      (onSubmit)="saveProps($event)"></app-form-listing-details-props>


      <app-form-listing-details [editMode]="id > 0"
                                [details]="details"
                                [mainCategory]="selectedCategory!"
                                [subCategory]="selectedSubCategory!"
                                (onSubmit)="saveDetails($event)"
                                (back)="restart()"></app-form-listing-details>

      <app-form-listing-details-location [locationForm]="locationForm"
                                         (onSubmit)="saveLocation($event)"></app-form-listing-details-location>

      <app-form-listing-details-props [props]="props"
                                      [mainCategory]="selectedCategory!"
                                      [subCategory]="selectedSubCategory!"
                                      (onSubmit)="saveProps($event)"></app-form-listing-details-props>

      <app-form-listing-details-swapp-interest *ngIf="details?.form?.paymentMethod > 1"
                                               [treadBy]="details.treadBy"
                                               [intersetedCategories]="intersetedCategories"
                                               (onSubmit)="saveInterest($event)"></app-form-listing-details-swapp-interest>




    </div>
    <div class="col-md-6 max-container submit_area">
      <app-form-images [details]="detailImages"
                       (onSubmit)="saveImages($event)"></app-form-images>

    </div>

    <div class="submit sticky_submit">
      <app-secondary-btn id="goto_preview_btn"
                         [btnText]="'Next' | translate"
                         btnType="button"
                         [bigBtn]="true"
                         (click)="gotoPreview()"></app-secondary-btn>
    </div>




  </div>


  <div *ngIf="tabIndex < 2"
       [ngClass]="{'editMode' : id > 0}"
       #maintarget>
    <div *ngIf="categories$ | async as categories">
      <div class="menu_box row"
           [ngClass]="{
              active: (selectedCategory && selectedCategory.items!.length > 0),
              visible : !activeSection
            }">
        <div class="col-md-6">
          <ul class="menuitems mainmenu">
            <li *ngFor="let item of categories"
                class="menuitem"
                (click)="selectMainCategory(item)">
              <a class="menuitem__link"
                 [ngClass]="{
                      active: selectedCategory && selectedCategory.id == item.id
                    }"><span class="menuitem__icon"><img [src]="item.icon" /></span>
                <span class="menuitem__text">{{ item.label }}</span></a>
            </li>
          </ul>
        </div>
        <div class="col-md-6 sub_category_menu">
          <div class="sub_category_menu_list"
               *ngIf="selectedCategory && selectedCategory.items!.length > 0">
            <ng-container>
              <div class="select_main_category">
                <span><img [src]="selectedCategory.icon" /></span>
                <span>{{ selectedCategory.label }}</span>
              </div>
              <ul class="menuitems submenu">
                <li *ngFor="let item of selectedCategory.items"
                    class="menuitem"
                    (click)="selectSubCategory(item)">
                  <a class="menuitem__link"
                     [ngClass]="{
                        active:
                          selectedSubCategory && selectedSubCategory.id == item.id
                      }"><span class="menuitem__text">{{ item.label }}</span></a>
                </li>
              </ul>

            </ng-container>



          </div>
          <div class="side_image">
            <img src="/assets/images/no-listing.webp?v=1.1.2" />
            <h2>{{ categoryHelpText }}</h2>
          </div>
        </div>
      </div>
    </div>
    <div class="submit sticky_submit">
      <app-secondary-btn id="goto_listing_details_form_btn"
                         [btnText]="'Next' | translate"
                         btnType="button"
                         [bigBtn]="true"
                         (click)="moveNext()"></app-secondary-btn>
    </div>
  </div>
</div>

}
@placeholder {
<div></div>
}