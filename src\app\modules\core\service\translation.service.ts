import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { AppcenterService } from 'src/app/shared/services/appcenter.service';
import { setLanguage } from 'src/app/store/app/actions/app.actions';

@Injectable({
  providedIn: 'root'
})
export class TranslationService {
  constructor(private translate: TranslateService, private store: Store, private appService: AppcenterService) { }


  get currentLang(): string {
    return this.translate.currentLang;
  }

  initializeTranslation(): Promise<any> {

    let base = this.appService.lang;

    // const matchResult = this.location.path().match(/(ar-eg|en-us)/);
    // const language = matchResult ? matchResult[1] : null;
    // if (language) {
    //   base = language;
    // }



    this.store.dispatch(setLanguage({ lang: base }));





    return new Promise((resolve) => {
      this.translate.use(base).subscribe(
        {
          next: () => {
            resolve(true);

          }, error: () => {
            resolve(false);
          }
        }
      );
    });
  }

  instant(key: string | Array<string>, interpolateParams?: Object): string | any {
    return this.capitalizeFirstLetter(this.translate.instant(key.toString().toLocaleLowerCase(), interpolateParams));
  }

  capitalizeFirstLetter(value: string): string {
    return value.charAt(0).toUpperCase() + value.slice(1);
  }
}